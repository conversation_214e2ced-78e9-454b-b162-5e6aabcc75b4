{"name": "spin/spin", "description": "Spin/Lumen Framework Skeleton", "keywords": ["spin", "framework", "laravel", "lumen"], "license": "MIT", "type": "project", "require": {"php": "^7.4 || ^8.0", "ext-json": "*", "spin/framework": "^3.0", "spin/logger": "^1.0", "weipaitang/communityserver-sdk-auto": " v1.99.0", "weipaitang/diamond-sdk-auto": "1.0.0", "weipaitang/sky-sdk": "0.0.8", "weipaitang/user-sdk-auto": "1.1.106", "weipaitang/zhenjin-sdk-auto": "1.0.0"}, "require-dev": {"fakerphp/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^9.3"}, "autoload": {"files": ["app/Helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"classmap": ["tests/"], "psr-4": {"Tests\\": "tests/"}}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "platform": {"php": "7.4"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "start": ["php -S 127.0.0.1:8800 -t public/"]}, "repositories": [{"type": "composer", "url": "https://mirrors.aliyun.com/composer/"}, {"type": "composer", "url": "https://packagist.wpt.la"}]}