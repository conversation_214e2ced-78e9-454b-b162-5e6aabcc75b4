<?php

namespace Tests\Feature\Controller\Inner;

use App\Constants\OrderConst;
use App\ErrCode\OrderErr;
use App\Exceptions\ErrException;
use App\Http\Controllers\Inner\OrderController;
use App\Http\Requests\BaseRequest;
use App\Logic\Inner\OrderLogic;
use App\Models\OrderModel;
use App\Params\Order\OrderParams;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Tests\TestCase;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Mockery;
use Spin\Logger\Facades\Log;

class OrderControllerTest extends TestCase
{
    use DatabaseTransactions;

    protected OrderController $controller;
    protected BaseRequest $baseRequest;

    protected function setUp(): void
    {
        parent::setUp();
        $this->baseRequest = $this->app->make(BaseRequest::class);
        $this->controller = new OrderController($this->baseRequest);

        // Mock the Log facade
        Log::shouldReceive('info')->andReturn(null);
        Log::shouldReceive('error')->andReturn(null);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * 测试创建订单 - 有效数据
     */
    public function testCreate_WithValidData_ShouldCreateOrder()
    {
        // 准备测试数据
        $requestData = [
            'businessId' => 1,
            'businessNo' => 'TEST-BN-' . time(),
            'businessMasterNo' => 'TEST-BMN-' . time(),
            'categoryIdentifier' => 'test-category',
            'categoryId' => 1,
            'endTime' => time() + 3600,
            'inputTemplateId' => 1,
            'outputTemplateId' => 2,
            'orderAmount' => 100,
            'subType' => 0,
            'items' => [
                [
                    'imgs' => ['http://example.com/test.jpg'],
                    'video' => [],
                    'remark' => 'Test remark'
                ]
            ]
        ];

        // 创建请求对象
        $request = Request::create('/inner/order/create', 'POST', $requestData);

        // 模拟OrderLogic的create方法
        $mockResult = [
            'uri' => 'test-uri-123456',
            'appraiserId' => 0,
            'appraiserName' => '',
        ];

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('create')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->create($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertEquals('test-uri-123456', $result['uri']);
    }

    /**
     * 测试获取订单详情 - 有效URI
     */
    public function testDetail_WithValidUri_ShouldReturnOrderDetails()
    {
        // 准备测试数据
        $uri = 'test-uri-' . time();
        $requestData = ['uri' => $uri];

        // 创建请求对象
        $request = Request::create('/inner/order/detail', 'GET', $requestData);

        // 模拟OrderLogic的detail方法
        $mockResult = [
            'uri' => $uri,
            'businessId' => 1,
            'businessName' => 'Test Business',
            'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY,
        ];

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('detail')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->detail($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertEquals($uri, $result['uri']);
    }

    /**
     * 测试获取订单详情 - 无效URI
     */
    public function testDetail_WithInvalidUri_ShouldThrowException()
    {
        // 准备测试数据
        $uri = 'invalid-uri';
        $requestData = ['uri' => $uri];

        // 创建请求对象
        $request = Request::create('/inner/order/detail', 'GET', $requestData);

        // 模拟OrderLogic的detail方法抛出异常
        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('detail')->once()->andThrow(new ErrException(OrderErr::ORDER_DOES_NOT_EXIST));

        // 设置期望的异常
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);

        // 执行控制器方法
        $this->controller->detail($request);
    }

    /**
     * 测试获取简单订单详情 - 有效URI
     */
    public function testSimpleDetail_WithValidUri_ShouldReturnSimpleOrderDetails()
    {
        // 准备测试数据
        $uri = 'test-uri-' . time();
        $requestData = ['uri' => $uri];

        // 创建请求对象
        $request = Request::create('/inner/order/simple-detail', 'GET', $requestData);

        // 模拟OrderLogic的simpleDetail方法
        $mockResult = [
            'uri' => $uri,
            'businessId' => 1,
            'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY,
        ];

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('simpleDetail')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->simpleDetail($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertEquals($uri, $result['uri']);
    }

    /**
     * 测试获取简单订单详情 - 无效URI
     */
    public function testSimpleDetail_WithInvalidUri_ShouldThrowException()
    {
        // 准备测试数据
        $uri = 'invalid-uri';
        $requestData = ['uri' => $uri];

        // 创建请求对象
        $request = Request::create('/inner/order/simple-detail', 'GET', $requestData);

        // 模拟OrderLogic的simpleDetail方法抛出异常
        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('simpleDetail')->once()->andThrow(new ErrException(OrderErr::ORDER_DOES_NOT_EXIST));

        // 设置期望的异常
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);

        // 执行控制器方法
        $this->controller->simpleDetail($request);
    }

    /**
     * 测试批量获取简单订单详情 - 有效URIs
     */
    public function testBatchSimpleDetail_WithValidUris_ShouldReturnBatchDetails()
    {
        // 准备测试数据
        $uris = ['test-uri-1', 'test-uri-2'];
        $requestData = ['uris' => $uris];

        // 创建请求对象
        $request = Request::create('/inner/order/batch-simple-detail', 'GET', $requestData);

        // 模拟OrderLogic的batchSimpleDetail方法
        $mockResult = [
            [
                'uri' => 'test-uri-1',
                'businessId' => 1,
                'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY,
            ],
            [
                'uri' => 'test-uri-2',
                'businessId' => 1,
                'state' => OrderConst::ORDER_STATE_COMPLETE,
            ]
        ];

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('batchSimpleDetail')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->batchSimpleDetail($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertCount(2, $result);
        $this->assertEquals('test-uri-1', $result[0]['uri']);
        $this->assertEquals('test-uri-2', $result[1]['uri']);
    }

    /**
     * 测试获取模板参数 - 有效URI
     */
    public function testGetParams_WithValidUri_ShouldReturnOrderParams()
    {
        // 准备测试数据
        $uri = 'test-uri-' . time();
        $requestData = ['uri' => $uri];

        // 创建请求对象
        $request = Request::create('/inner/order/get-params', 'GET', $requestData);

        // 模拟OrderLogic的getParams方法
        $mockResult = [
            'inputParams' => [
                [
                    'fieldKey' => 'param1',
                    'fieldName' => 'Parameter 1',
                    'fieldType' => 'text',
                ]
            ]
        ];

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('getParams')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->getParams($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertArrayHasKey('inputParams', $result);
    }

    /**
     * 测试获取模板参数 - 无效URI
     */
    public function testGetParams_WithInvalidUri_ShouldThrowException()
    {
        // 准备测试数据
        $uri = 'invalid-uri';
        $requestData = ['uri' => $uri];

        // 创建请求对象
        $request = Request::create('/inner/order/get-params', 'GET', $requestData);

        // 模拟OrderLogic的getParams方法抛出异常
        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('getParams')->once()->andThrow(new ErrException(OrderErr::ORDER_DOES_NOT_EXIST));

        // 设置期望的异常
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);

        // 执行控制器方法
        $this->controller->getParams($request);
    }

    /**
     * 测试分配鉴定师 - 有效数据
     */
    public function testAssignAppraiser_WithValidData_ShouldAssignAppraiser()
    {
        // 准备测试数据
        $uri = 'test-uri-' . time();
        $appraiserId = 1;
        $requestData = [
            'uri' => $uri,
            'appraiserId' => $appraiserId
        ];

        // 创建请求对象
        $request = Request::create('/inner/order/assign-appraiser', 'POST', $requestData);

        // 模拟OrderLogic的assignAppraiser方法
        $mockResult = [
            'state' => 1,
        ];

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('assignAppraiser')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->assignAppraiser($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertArrayHasKey('state', $result);
        $this->assertEquals(1, $result['state']);
    }

    /**
     * 测试分配鉴定师 - 无效URI
     */
    public function testAssignAppraiser_WithInvalidUri_ShouldThrowException()
    {
        // 准备测试数据
        $uri = 'invalid-uri';
        $appraiserId = 1;
        $requestData = [
            'uri' => $uri,
            'appraiserId' => $appraiserId
        ];

        // 创建请求对象
        $request = Request::create('/inner/order/assign-appraiser', 'POST', $requestData);

        // 模拟OrderLogic的assignAppraiser方法抛出异常
        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('assignAppraiser')->once()->andThrow(new ErrException(OrderErr::ORDER_DOES_NOT_EXIST));

        // 设置期望的异常
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);

        // 执行控制器方法
        $this->controller->assignAppraiser($request);
    }

    /**
     * 测试取消订单 - 有效URI
     */
    public function testCancel_WithValidUri_ShouldCancelOrder()
    {
        // 准备测试数据
        $uri = 'test-uri-' . time();
        $requestData = ['uri' => $uri];

        // 创建请求对象
        $request = Request::create('/inner/order/cancel', 'POST', $requestData);

        // 模拟OrderLogic的cancel方法
        $mockResult = [];

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('cancel')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->cancel($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertEmpty($result);
    }

    /**
     * 测试取消订单 - 无效URI
     */
    public function testCancel_WithInvalidUri_ShouldThrowException()
    {
        // 准备测试数据
        $uri = 'invalid-uri';
        $requestData = ['uri' => $uri];

        // 创建请求对象
        $request = Request::create('/inner/order/cancel', 'POST', $requestData);

        // 模拟OrderLogic的cancel方法抛出异常
        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('cancel')->once()->andThrow(new ErrException(OrderErr::ORDER_DOES_NOT_EXIST));

        // 设置期望的异常
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);

        // 执行控制器方法
        $this->controller->cancel($request);
    }

    /**
     * 测试打回订单 - 有效数据
     */
    public function testReject_WithValidData_ShouldRejectOrder()
    {
        // 准备测试数据
        $uri = 'test-uri-' . time();
        $userinfoId = 1;
        $rejectReason = 'Test reject reason';
        $requestData = [
            'uri' => $uri,
            'userinfoId' => $userinfoId,
            'rejectReason' => $rejectReason
        ];

        // 创建请求对象
        $request = Request::create('/inner/order/reject', 'POST', $requestData);

        // 模拟OrderLogic的reject方法
        $mockResult = true;

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('reject')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->reject($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertTrue($result);
    }

    /**
     * 测试打回订单 - 无效URI
     */
    public function testReject_WithInvalidUri_ShouldThrowException()
    {
        // 准备测试数据
        $uri = 'invalid-uri';
        $userinfoId = 1;
        $rejectReason = 'Test reject reason';
        $requestData = [
            'uri' => $uri,
            'userinfoId' => $userinfoId,
            'rejectReason' => $rejectReason
        ];

        // 创建请求对象
        $request = Request::create('/inner/order/reject', 'POST', $requestData);

        // 模拟OrderLogic的reject方法抛出异常
        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('reject')->once()->andThrow(new ErrException(OrderErr::ORDER_DOES_NOT_EXIST));

        // 设置期望的异常
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);

        // 执行控制器方法
        $this->controller->reject($request);
    }

    /**
     * 测试获取订单列表 - 有效参数
     */
    public function testList_WithValidParams_ShouldReturnOrderList()
    {
        // 准备测试数据
        $requestData = [
            'page' => 1,
            'pageSize' => 10,
            'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY
        ];

        // 创建请求对象
        $request = Request::create('/inner/order/list', 'GET', $requestData);

        // 模拟OrderLogic的list方法
        $mockResult = [
            'page' => 2,
            'pageSize' => 10,
            'list' => [
                [
                    'uri' => 'test-uri-1',
                    'businessId' => 1,
                    'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY,
                ],
                [
                    'uri' => 'test-uri-2',
                    'businessId' => 1,
                    'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY,
                ]
            ],
            'isEnd' => false
        ];

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('list')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->list($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertArrayHasKey('list', $result);
        $this->assertCount(2, $result['list']);
    }

    /**
     * 测试提交订单 - 有效数据
     */
    public function testSubmit_WithValidData_ShouldSubmitOrder()
    {
        // 准备测试数据
        $uri = 'test-uri-' . time();
        $requestData = [
            'uri' => $uri,
            'identResult' => 'Test result',
            'identTruth' => 1,
            'items' => [
                [
                    'param1' => 'value1',
                    'param2' => 'value2'
                ]
            ]
        ];

        // 创建请求对象
        $request = Request::create('/inner/order/submit', 'POST', $requestData);

        // 模拟OrderLogic的submit方法
        $mockResult = [];

        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('submit')->once()->andReturn($mockResult);

        // 执行控制器方法
        $result = $this->controller->submit($request);

        // 验证结果
        $this->assertEquals($mockResult, $result);
        $this->assertEmpty($result);
    }

    /**
     * 测试提交订单 - 无效URI
     */
    public function testSubmit_WithInvalidUri_ShouldThrowException()
    {
        // 准备测试数据
        $uri = 'invalid-uri';
        $requestData = [
            'uri' => $uri,
            'identResult' => 'Test result',
            'identTruth' => 1,
            'items' => [
                [
                    'param1' => 'value1',
                    'param2' => 'value2'
                ]
            ]
        ];

        // 创建请求对象
        $request = Request::create('/inner/order/submit', 'POST', $requestData);

        // 模拟OrderLogic的submit方法抛出异常
        $mockOrderLogic = Mockery::mock('overload:' . OrderLogic::class);
        $mockOrderLogic->shouldReceive('getInstance')->andReturnSelf();
        $mockOrderLogic->shouldReceive('submit')->once()->andThrow(new ErrException(OrderErr::ORDER_DOES_NOT_EXIST));

        // 设置期望的异常
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);

        // 执行控制器方法
        $this->controller->submit($request);
    }
}
