<?php

namespace Tests\Feature\Logic\Inner;

use App\Constants\AppraiserConst;
use App\Constants\BusinessConst;
use App\Constants\OrderConst;
use App\Constants\TemplateConst;
use App\ErrCode\AppraiserErr;
use App\ErrCode\BaseErr;
use App\ErrCode\FieldErr;
use App\ErrCode\OrderErr;
use App\ErrCode\TemplateErr;
use App\Exceptions\ErrException;
use App\Logic\Inner\OrderLogic;
use App\Models\BusinessCategoryModel;
use App\Models\BusinessFieldModel;
use App\Models\BusinessModel;
use App\Models\BusinessTemplateFieldModel;
use App\Models\BusinessTemplateModel;
use App\Models\OrderItemFieldModel;
use App\Models\OrderItemModel;
use App\Models\OrderModel;
use App\Params\Order\OrderParams;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;

class OrderLogicTest extends TestCase
{
    use DatabaseTransactions;

    protected $orderLogic;

    protected function setUp(): void
    {
        parent::setUp();
        $this->orderLogic = OrderLogic::getInstance();
    }

    /**
     * 测试正常创建订单
     */
    public function testCreateSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 11740300; // 使用有效的鉴定师ID
        $params->businessNo = 'BN001';
        $params->businessMasterNo = 'BMN001';
        $params->orderAmount = 100;
        $params->subType = 1;
        $params->items = [
            [
                'imgs' => ['img1.jpg', 'img2.jpg'],
                'video' => [['videoUrl' => 'video1.mp4']],
                'remark' => '测试备注',
                'testField' => '测试值'
            ]
        ];
        $params->order = ['orderField' => '订单值'];

        $result = $this->orderLogic->create($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertArrayHasKey('appraiserId', $result);
        $this->assertArrayHasKey('appraiserName', $result);
        $this->assertEquals(11740300, $result['appraiserId']);

        // 验证订单已创建
        $order = OrderModel::query()->where('uri', $result['uri'])->first();
        $this->assertNotNull($order);
        $this->assertEquals($testData['businessId'], $order->business_id);
        $this->assertEquals('test_category', $order->category_identifier);
    }

    /**
     * 测试缺少业务ID时创建订单
     */
    public function testCreateMissingBusinessId()
    {
        $params = new OrderParams();
        $params->categoryIdentifier = 'test_category';
        $params->items = [['imgs' => ['img1.jpg']]];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->orderLogic->create($params);
    }

    /**
     * 测试缺少类目标识时创建订单
     */
    public function testCreateMissingCategoryIdentifier()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->items = [['imgs' => ['img1.jpg']]];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::CATEGORY_NOT_CONFIGURED_CORRECTLY[0]);
        $this->expectExceptionMessage('类目未传递或者未找到相关配置');

        $this->orderLogic->create($params);
    }

    /**
     * 测试缺少订单项时创建订单
     */
    public function testCreateMissingItems()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->orderLogic->create($params);
    }

    /**
     * 测试无效的图片数据
     */
    public function testCreateInvalidItemImages()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 11740300; // 使用有效的鉴定师ID
        $params->items = [['imgs' => '这不是数组']];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_ITEM_IMG_ERR[0]);
        $this->expectExceptionMessage('订单物品图片必须且为数组');

        $this->orderLogic->create($params);
    }

    /**
     * 测试无效的视频数据
     */
    public function testCreateInvalidItemVideo()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 11740300; // 使用有效的鉴定师ID
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'video' => '这不是数组'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_ITEM_VIDEO_ERR[0]);
        $this->expectExceptionMessage('订单物品视频必须且为数组');

        $this->orderLogic->create($params);
    }

    /**
     * 测试备注太长
     */
    public function testCreateRemarkTooLong()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 11740300; // 使用有效的鉴定师ID
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'remark' => str_repeat('a', 501) // 501个字符
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_SUBMIT_PARAMS_ERR[0]);
        $this->expectExceptionMessage('remark不能超过500字符');

        $this->orderLogic->create($params);
    }

    /**
     * 测试鉴定师不存在
     */
    public function testCreateAppraiserNotExist()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 3; // 使用无效的鉴定师ID
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'remark' => '测试备注'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(AppraiserErr::APPRAISER_NO_EXIST[0]);
        $this->expectExceptionMessage('鉴定师不存在');

        $this->orderLogic->create($params);
    }

    /**
     * 测试businessId=4的海外图文鉴定自动派单
     */
    public function testCreateOverseasBusiness()
    {
        $testData = $this->insertTestData();

        // 修改业务ID为海外图文ID
        BusinessModel::query()->where('id', $testData['businessId'])->update(['id' => 4]);
        BusinessCategoryModel::query()->where('business_id', $testData['businessId'])->update(['business_id' => 4]);
        BusinessTemplateModel::query()->where('business_id', $testData['businessId'])->update(['business_id' => 4]);
        BusinessFieldModel::query()->where('business_id', $testData['businessId'])->update(['business_id' => 4]);

        $params = new OrderParams();
        $params->businessId = 4; // 海外图文ID
        $params->categoryIdentifier = 'test_category';
        $params->items = [
            [
                'imgs' => ['img1.jpg', 'img2.jpg'],
                'video' => [['videoUrl' => 'video1.mp4']],
                'remark' => '测试备注',
                'testField' => '测试值'
            ]
        ];
        $params->order = ['orderField' => '订单值'];

        $result = $this->orderLogic->create($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertArrayHasKey('appraiserId', $result);
        $this->assertTrue(in_array($result['appraiserId'], [11740300, 12260004, 100320642, 11742588]));
    }

    /**
     * 测试consign_evaluate业务类型
     */
    public function testCreateConsignEvaluate()
    {
        $testData = $this->insertTestData();

        // 修改业务ID为寄卖评估ID
        BusinessModel::query()->where('id', $testData['businessId'])->update(['id' => BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE]);
        BusinessCategoryModel::query()->where('business_id', $testData['businessId'])->update(['business_id' => BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE]);
        BusinessTemplateModel::query()->where('business_id', $testData['businessId'])->update(['business_id' => BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE]);
        BusinessFieldModel::query()->where('business_id', $testData['businessId'])->update(['business_id' => BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE]);

        $params = new OrderParams();
        $params->businessId = BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE;
        $params->categoryIdentifier = 'test_category';
        $params->items = [
            [
                'imgs' => ['img1.jpg', 'img2.jpg'],
                'remark' => '测试备注',
                'testField' => '测试值'
            ]
        ];
        $params->order = ['orderField' => '订单值'];

        $result = $this->orderLogic->create($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertArrayHasKey('appraiserId', $result);
        $this->assertEquals(100320642, $result['appraiserId']);
    }

    /**
     * 测试创建businessId=6时的订单
     */
    public function testCreateWithValidBusiness6()
    {
        $testData = $this->insertTestData();

        // 修改业务ID为6
        BusinessModel::query()->where('id', $testData['businessId'])->update(['id' => 6]);
        BusinessCategoryModel::query()->where('business_id', $testData['businessId'])->update([
            'business_id' => 6,
            'category_identifier' => '1923'
        ]);
        BusinessTemplateModel::query()->where('business_id', $testData['businessId'])->update(['business_id' => 6]);
        BusinessFieldModel::query()->where('business_id', $testData['businessId'])->update(['business_id' => 6]);

        $params = new OrderParams();
        $params->businessId = 6;
        $params->categoryIdentifier = '1923';
        $params->appraiserId = 11740300;
        $params->items = [
            [
                'imgs' => ['img1.jpg', 'img2.jpg'],
                'remark' => '测试备注',
                'testField' => '测试值'
            ]
        ];
        $params->order = ['orderField' => '订单值'];

        $result = $this->orderLogic->create($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertArrayHasKey('appraiserId', $result);
        $this->assertEquals(11740300, $result['appraiserId']);
    }

    /**
     * 测试无效的视频URL
     */
    public function testCreateWithInvalidVideoUrl()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 11740300;
        $params->items = [
            [
                'imgs' => ['img1.jpg', 'img2.jpg'],
                'video' => [['不包含videoUrl字段' => 'test']],
                'remark' => '测试备注',
                'testField' => '测试值'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_SUBMIT_PARAMS_ERR[0]);
        $this->expectExceptionMessage('videoUrl不能为空');

        $this->orderLogic->create($params);
    }

    /**
     * 测试图片JSON太长
     */
    public function testCreateImgsJsonTooLong()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 11740300;
        
        // 创建一个非常长的图片数组
        $longImgs = [];
        for ($i = 0; $i < 100; $i++) {
            $longImgs[] = 'https://example.com/very/long/path/to/image/that/exceeds/the/maximum/allowed/length/for/this/test/case/' . str_repeat('a', 100) . '.jpg';
        }
        
        $params->items = [
            [
                'imgs' => $longImgs,
                'remark' => '测试备注',
                'testField' => '测试值'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_SUBMIT_PARAMS_ERR[0]);
        $this->expectExceptionMessage('imgs不能超过1500字符');

        $this->orderLogic->create($params);
    }

    /**
     * 测试视频JSON太长
     */
    public function testCreateVideoJsonTooLong()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 11740300;
        
        // 创建一个非常长的视频数组
        $longVideos = [];
        for ($i = 0; $i < 10; $i++) {
            $longVideos[] = [
                'videoUrl' => 'https://example.com/very/long/path/to/video/that/exceeds/the/maximum/allowed/length/for/this/test/case/' . str_repeat('v', 100) . '.mp4',
                'coverUrl' => 'https://example.com/very/long/path/to/cover/that/exceeds/the/maximum/allowed/length/for/this/test/case/' . str_repeat('c', 100) . '.jpg',
            ];
        }
        
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'video' => $longVideos,
                'remark' => '测试备注',
                'testField' => '测试值'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_SUBMIT_PARAMS_ERR[0]);
        $this->expectExceptionMessage('video不能超过800字符');

        $this->orderLogic->create($params);
    }

    /**
     * 测试getParams成功获取参数
     */
    public function testGetParamsSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        // 创建订单数据
        $orderData = $this->createOrderData($testData);

        $result = $this->orderLogic->getParams($orderData['uri']);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('inputParams', $result);
    }

    /**
     * 测试getParams订单不存在
     */
    public function testGetParamsOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');

        $this->orderLogic->getParams('not_exist_uri');
    }

    /**
     * 测试detail成功获取订单详情
     */
    public function testDetailSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        // 创建订单数据
        $orderData = $this->createOrderData($testData);

        $result = $this->orderLogic->detail($orderData['uri']);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertArrayHasKey('businessId', $result);
        $this->assertArrayHasKey('categoryIdentifier', $result);
        $this->assertArrayHasKey('state', $result);
        $this->assertArrayHasKey('input', $result);
        $this->assertArrayHasKey('output', $result);
        $this->assertEquals($orderData['uri'], $result['uri']);
        $this->assertEquals($testData['businessId'], $result['businessId']);
        $this->assertEquals('test_category', $result['categoryIdentifier']);
    }

    /**
     * 测试detail订单不存在
     */
    public function testDetailOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');

        $this->orderLogic->detail('not_exist_uri');
    }

    /**
     * 测试simpleDetail成功获取简化订单详情
     */
    public function testSimpleDetailSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        // 创建订单数据
        $orderData = $this->createOrderData($testData);

        $result = $this->orderLogic->simpleDetail($orderData['uri']);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertArrayHasKey('businessId', $result);
        $this->assertArrayHasKey('categoryIdentifier', $result);
        $this->assertArrayHasKey('state', $result);
        $this->assertArrayHasKey('output', $result);
        $this->assertEquals($orderData['uri'], $result['uri']);
        $this->assertEquals($testData['businessId'], $result['businessId']);
        $this->assertEquals('test_category', $result['categoryIdentifier']);
    }

    /**
     * 测试simpleDetail订单不存在
     */
    public function testSimpleDetailOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');

        $this->orderLogic->simpleDetail('not_exist_uri');
    }

    /**
     * 测试batchSimpleDetail成功批量获取简化订单详情
     */
    public function testBatchSimpleDetailSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        // 创建多个订单数据
        $orderData1 = $this->createOrderData($testData);
        $orderData2 = $this->createOrderData($testData);

        $result = $this->orderLogic->batchSimpleDetail([$orderData1['uri'], $orderData2['uri']]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertCount(2, $result['list']);
        $this->assertArrayHasKey($orderData1['uri'], $result['list']);
        $this->assertArrayHasKey($orderData2['uri'], $result['list']);
    }

    /**
     * 测试batchSimpleDetail包含不存在的URI
     */
    public function testBatchSimpleDetailWithInvalidUris()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        // 创建订单数据
        $orderData = $this->createOrderData($testData);

        $result = $this->orderLogic->batchSimpleDetail([$orderData['uri'], 'invalid_uri']);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertCount(1, $result['list']);
        $this->assertArrayHasKey($orderData['uri'], $result['list']);
    }

    /**
     * 测试batchSimpleDetail所有URI都不存在
     */
    public function testBatchSimpleDetailAllInvalidUris()
    {
        $result = $this->orderLogic->batchSimpleDetail(['invalid_uri1', 'invalid_uri2']);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertCount(0, $result['list']);
    }

    /**
     * 测试成功分配鉴定师
     */
    public function testAssignAppraiserSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 1. 创建待分配状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_DISTRIBUTION);
        
        // 2. 分配鉴定师
        $result = $this->orderLogic->assignAppraiser($orderData['uri'], 11740300);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('state', $result);
        $this->assertEquals(1, $result['state']);
        
        // 3. 验证订单状态已更新
        $order = OrderModel::query()->where('uri', $orderData['uri'])->first();
        $this->assertNotNull($order);
        $this->assertEquals(OrderConst::ORDER_STATE_WAIT_IDENTIFY, $order->state);
        $this->assertEquals(11740300, $order->userinfo_id);
    }
    
    /**
     * 测试修改鉴定师（待鉴定状态）
     */
    public function testModifyAppraiserSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 1. 创建待鉴定状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_IDENTIFY);
        
        // 2. 修改鉴定师
        $result = $this->orderLogic->assignAppraiser($orderData['uri'], 11740300);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('state', $result);
        $this->assertEquals(1, $result['state']);
        
        // 3. 验证订单鉴定师已更新，状态不变
        $order = OrderModel::query()->where('uri', $orderData['uri'])->first();
        $this->assertNotNull($order);
        $this->assertEquals(OrderConst::ORDER_STATE_WAIT_IDENTIFY, $order->state);
        $this->assertEquals(11740300, $order->userinfo_id);
    }
    
    /**
     * 测试分配鉴定师时订单不存在
     */
    public function testAssignAppraiserOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');
        
        $this->orderLogic->assignAppraiser('not_exist_uri', 11740300);
    }
    
    /**
     * 测试分配鉴定师时订单状态无效
     */
    public function testAssignAppraiserInvalidState()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建已完成状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_COMPLETE);
        
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_STATUS_CANNOT_ASSIGN_OR_MODIFY[0]);
        $this->expectExceptionMessage('订单状态不允许分配或修改鉴定师');
        
        $this->orderLogic->assignAppraiser($orderData['uri'], 11740300);
    }
    
    /**
     * 测试分配不存在的鉴定师
     */
    public function testAssignAppraiserNotExist()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建待分配状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_DISTRIBUTION);
        
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(AppraiserErr::APPRAISER_NO_EXIST[0]);
        $this->expectExceptionMessage('鉴定师不存在');
        
        $this->orderLogic->assignAppraiser($orderData['uri'], 3); // 使用无效的鉴定师ID
    }
    
    /**
     * 测试成功取消订单
     */
    public function testCancelSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 1. 创建待分配状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_DISTRIBUTION);
        
        // 2. 取消订单
        $result = $this->orderLogic->cancel($orderData['uri']);
        
        $this->assertIsArray($result);
        
        // 3. 验证订单状态已更新为取消
        $order = OrderModel::query()->where('uri', $orderData['uri'])->first();
        $this->assertNotNull($order);
        $this->assertEquals(OrderConst::ORDER_STATE_CANCEL, $order->state);
    }
    
    /**
     * 测试取消待鉴定状态的订单
     */
    public function testCancelWaitIdentifySuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 1. 创建待鉴定状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_IDENTIFY);
        
        // 2. 取消订单
        $result = $this->orderLogic->cancel($orderData['uri']);
        
        $this->assertIsArray($result);
        
        // 3. 验证订单状态已更新为取消
        $order = OrderModel::query()->where('uri', $orderData['uri'])->first();
        $this->assertNotNull($order);
        $this->assertEquals(OrderConst::ORDER_STATE_CANCEL, $order->state);
    }
    
    /**
     * 测试取消不存在的订单
     */
    public function testCancelOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');
        
        $this->orderLogic->cancel('not_exist_uri');
    }
    
    /**
     * 测试取消无效状态的订单
     */
    public function testCancelInvalidState()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建已完成状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_COMPLETE);
        
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_STATUS_CANNOT_CANCEL[0]);
        $this->expectExceptionMessage('订单状态不允许取消');
        
        $this->orderLogic->cancel($orderData['uri']);
    }

    /**
     * 测试成功拒绝订单
     */
    public function testRejectSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 1. 创建已完成状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_COMPLETE);
        
        // 2. 拒绝订单
        $result = $this->orderLogic->reject($orderData['uri'], 11740300, '测试拒绝原因');
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('result', $result);
        
        // 3. 验证订单状态已更新
        $order = OrderModel::query()->where('uri', $orderData['uri'])->first();
        $this->assertNotNull($order);
        $this->assertEquals(OrderConst::ORDER_STATE_WAIT_IDENTIFY, $order->state);
        $this->assertEquals(11740300, $order->userinfo_id);
        
        // 4. 验证拒绝原因已保存
        $detailJson = json_decode($order->detail_json, true);
        $this->assertArrayHasKey('rejectFlag', $detailJson);
        $this->assertEquals(1, $detailJson['rejectFlag']);
        
        // 5. 验证拒绝原因字段
        $rejectField = OrderItemFieldModel::query()
            ->where('order_id', $orderData['orderId'])
            ->where('field_key', 'rejectReason')
            ->first();
        $this->assertNotNull($rejectField);
        $this->assertEquals('测试拒绝原因', $rejectField->field_value);
    }
    
    /**
     * 测试拒绝不存在的订单
     */
    public function testRejectOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');
        
        $this->orderLogic->reject('not_exist_uri', 11740300, '测试拒绝原因');
    }
    
    /**
     * 测试拒绝无效状态的订单
     */
    public function testRejectInvalidState()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建待鉴定状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_IDENTIFY);
        
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_STATUS_CANNOT_REJECT[0]);
        $this->expectExceptionMessage('订单状态不允许退回');
        
        $this->orderLogic->reject($orderData['uri'], 11740300, '测试拒绝原因');
    }
    
    /**
     * 测试使用不存在的鉴定师拒绝订单
     */
    public function testRejectAppraiserNotExist()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建已完成状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_COMPLETE);
        
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(AppraiserErr::APPRAISER_NO_EXIST[0]);
        $this->expectExceptionMessage('鉴定师不存在');
        
        $this->orderLogic->reject($orderData['uri'], 3, '测试拒绝原因'); // 使用无效的鉴定师ID
    }
    
    /**
     * 测试成功获取订单列表
     */
    public function testListSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建多个订单
        $this->createOrderData($testData);
        $this->createOrderData($testData);
        
        // 获取列表
        $result = $this->orderLogic->list([]);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('page', $result);
        $this->assertArrayHasKey('pageSize', $result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('isEnd', $result);
        $this->assertCount(2, $result['list']);
    }
    
    /**
     * 测试分页获取订单列表
     */
    public function testListWithPagination()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建多个订单
        $this->createOrderData($testData);
        $this->createOrderData($testData);
        $this->createOrderData($testData);
        
        // 获取第一页，每页1条
        $result1 = $this->orderLogic->list(['page' => 1, 'pageSize' => 1]);
        $this->assertCount(1, $result1['list']);
        $this->assertEquals(2, $result1['page']);
        $this->assertEquals(1, $result1['pageSize']);
        $this->assertFalse($result1['isEnd']);
        
        // 获取第二页，每页1条
        $result2 = $this->orderLogic->list(['page' => 2, 'pageSize' => 1]);
        $this->assertCount(1, $result2['list']);
        $this->assertEquals(3, $result2['page']);
        $this->assertFalse($result2['isEnd']);
        
        // 获取第三页，每页1条
        $result3 = $this->orderLogic->list(['page' => 3, 'pageSize' => 1]);
        $this->assertCount(1, $result3['list']);
        $this->assertEquals(4, $result3['page']);
        $this->assertTrue($result3['isEnd']);
    }
    
    /**
     * 测试按业务ID筛选订单列表
     */
    public function testListWithBusinessIdFilter()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建普通订单
        $this->createOrderData($testData);
        
        // 创建另一个业务的订单
        $anotherBusinessId = BusinessModel::query()->insertGetId([
            'name' => '测试业务2',
            'status' => 1,
            'create_time' => time(),
            'is_deleted' => 0,
        ]);
        
        $orderId = OrderModel::query()->insertGetId([
            'uri' => 'test_uri_another_' . time(),
            'business_id' => $anotherBusinessId,
            'business_no' => 'BN002',
            'business_master_no' => 'BMN002',
            'category_identifier' => 'test_category',
            'category_id' => $testData['categoryId'],
            'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY,
            'input_template_id' => $testData['inputTemplateId'],
            'output_template_id' => $testData['outputTemplateId'],
            'userinfo_id' => 1001,
            'cover' => 'test_cover.jpg',
            'accept_time' => time(),
            'create_time' => time(),
            'detail_json' => '{}',
            'is_deleted' => 0,
        ]);
        
        // 按第一个业务ID筛选
        $result1 = $this->orderLogic->list(['businessId' => $testData['businessId']]);
        $this->assertCount(1, $result1['list']);
        $this->assertEquals($testData['businessId'], $result1['list'][0]['businessId']);
        
        // 按第二个业务ID筛选
        $result2 = $this->orderLogic->list(['businessId' => $anotherBusinessId]);
        $this->assertCount(1, $result2['list']);
        $this->assertEquals($anotherBusinessId, $result2['list'][0]['businessId']);
    }
    
    /**
     * 测试按业务IDs筛选订单列表
     */
    public function testListWithBusinessIdsFilter()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建普通订单
        $this->createOrderData($testData);
        
        // 创建另一个业务的订单
        $anotherBusinessId = BusinessModel::query()->insertGetId([
            'name' => '测试业务2',
            'status' => 1,
            'create_time' => time(),
            'is_deleted' => 0,
        ]);
        
        $orderId = OrderModel::query()->insertGetId([
            'uri' => 'test_uri_another_' . time(),
            'business_id' => $anotherBusinessId,
            'business_no' => 'BN002',
            'business_master_no' => 'BMN002',
            'category_identifier' => 'test_category',
            'category_id' => $testData['categoryId'],
            'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY,
            'input_template_id' => $testData['inputTemplateId'],
            'output_template_id' => $testData['outputTemplateId'],
            'userinfo_id' => 1001,
            'cover' => 'test_cover.jpg',
            'accept_time' => time(),
            'create_time' => time(),
            'detail_json' => '{}',
            'is_deleted' => 0,
        ]);
        
        // 按两个业务ID筛选
        $result = $this->orderLogic->list(['businessIds' => [$testData['businessId'], $anotherBusinessId]]);
        $this->assertCount(2, $result['list']);
    }
    
    /**
     * 测试按状态筛选订单列表
     */
    public function testListWithStateFilter()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建待鉴定状态的订单
        $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_IDENTIFY);
        
        // 创建已完成状态的订单
        $this->createOrderData($testData, OrderConst::ORDER_STATE_COMPLETE);
        
        // 按待鉴定状态筛选
        $result1 = $this->orderLogic->list(['state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY]);
        $this->assertCount(1, $result1['list']);
        $this->assertEquals(OrderConst::ORDER_STATE_WAIT_IDENTIFY, $result1['list'][0]['state']);
        
        // 按已完成状态筛选
        $result2 = $this->orderLogic->list(['state' => OrderConst::ORDER_STATE_COMPLETE]);
        $this->assertCount(1, $result2['list']);
        $this->assertEquals(OrderConst::ORDER_STATE_COMPLETE, $result2['list'][0]['state']);
    }
    
    /**
     * 测试按鉴定结果筛选订单列表
     */
    public function testListWithIdentTruthFilter()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建鉴定结果为真的订单
        $orderDataTrue = $this->createOrderData($testData);
        OrderModel::query()->where('id', $orderDataTrue['orderId'])->update(['ident_truth' => 1]);
        
        // 创建鉴定结果为假的订单
        $orderDataFalse = $this->createOrderData($testData);
        OrderModel::query()->where('id', $orderDataFalse['orderId'])->update(['ident_truth' => 0]);
        
        // 按鉴定结果为真筛选
        $result1 = $this->orderLogic->list(['identTruth' => 1]);
        $this->assertCount(1, $result1['list']);
        
        // 按鉴定结果为假筛选
        $result2 = $this->orderLogic->list(['identTruth' => 0]);
        $this->assertCount(1, $result2['list']);
    }
    
    /**
     * 测试按鉴定师筛选订单列表
     */
    public function testListWithAppraiserFilter()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建鉴定师1的订单
        $orderData1 = $this->createOrderData($testData);
        OrderModel::query()->where('id', $orderData1['orderId'])->update(['userinfo_id' => 11740300]);
        
        // 创建鉴定师2的订单
        $orderData2 = $this->createOrderData($testData);
        OrderModel::query()->where('id', $orderData2['orderId'])->update(['userinfo_id' => 12260004]);
        
        // 按鉴定师1筛选
        $result1 = $this->orderLogic->list(['userinfoId' => 11740300]);
        $this->assertCount(1, $result1['list']);
        $this->assertEquals(11740300, $result1['list'][0]['appraiserId']);
        
        // 按鉴定师2筛选
        $result2 = $this->orderLogic->list(['userinfoId' => 12260004]);
        $this->assertCount(1, $result2['list']);
        $this->assertEquals(12260004, $result2['list'][0]['appraiserId']);
    }

    /**
     * 测试成功提交订单
     */
    public function testSubmitSuccess()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建待鉴定状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_IDENTIFY);
        
        // 提交订单
        $params = [
            'uri' => $orderData['uri'],
            'identResult' => '真品',
            'identTruth' => 1,
            'items' => [
                [
                    'testField' => '测试字段值'
                ]
            ],
            'orderField' => '订单字段值'
        ];
        
        $result = $this->orderLogic->submit($params);
        
        $this->assertIsArray($result);
        
        // 验证订单状态已更新
        $order = OrderModel::query()->where('uri', $orderData['uri'])->first();
        $this->assertNotNull($order);
        $this->assertEquals(OrderConst::ORDER_STATE_COMPLETE, $order->state);
        $this->assertEquals('真品', $order->ident_result);
        $this->assertEquals(1, $order->ident_truth);
        
        // 验证字段已保存
        $orderItemField = OrderItemFieldModel::query()
            ->where('order_id', $orderData['orderId'])
            ->where('biz_type', OrderConst::BIZ_TYPE_OUTPUT)
            ->first();
        $this->assertNotNull($orderItemField);
    }
    
    /**
     * 测试提交不存在的订单
     */
    public function testSubmitOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');
        
        $params = [
            'uri' => 'not_exist_uri',
            'identResult' => '真品',
            'identTruth' => 1,
            'items' => [
                [
                    'testField' => '测试字段值'
                ]
            ]
        ];
        
        $this->orderLogic->submit($params);
    }
    
    /**
     * 测试提交无效状态的订单
     */
    public function testSubmitInvalidState()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建已完成状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_COMPLETE);
        
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_STATE_ABNORMAL[0]);
        $this->expectExceptionMessage('订单状态异常');
        
        $params = [
            'uri' => $orderData['uri'],
            'identResult' => '真品',
            'identTruth' => 1,
            'items' => [
                [
                    'testField' => '测试字段值'
                ]
            ]
        ];
        
        $this->orderLogic->submit($params);
    }
    
    /**
     * 测试提交缺少参数的订单
     */
    public function testSubmitMissingParams()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建待鉴定状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_IDENTIFY);
        
        // 缺少items参数
        $params = [
            'uri' => $orderData['uri'],
            'identResult' => '真品',
            'identTruth' => 1
        ];
        
        $this->expectException(ErrException::class);
        
        $this->orderLogic->submit($params);
    }
    
    /**
     * 测试订单提交时添加newSubmit标记
     */
    public function testSubmitAddNewSubmitFlag()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建待鉴定状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_IDENTIFY);
        
        // 提交订单
        $params = [
            'uri' => $orderData['uri'],
            'identResult' => '真品',
            'identTruth' => 1,
            'items' => [
                [
                    'testField' => '测试字段值'
                ]
            ],
            'orderField' => '订单字段值'
        ];
        
        $this->orderLogic->submit($params);
        
        // 验证订单detail_json中newSubmit标记
        $order = OrderModel::query()->where('uri', $orderData['uri'])->first();
        $detailJson = json_decode($order->detail_json, true);
        $this->assertArrayHasKey('newSubmit', $detailJson);
        $this->assertEquals(1, $detailJson['newSubmit']);
    }
    
    /**
     * 测试提交时清除已有的输出字段
     */
    public function testSubmitClearExistingOutputFields()
    {
        // 插入测试数据
        $testData = $this->insertTestData();
        
        // 创建待鉴定状态的订单
        $orderData = $this->createOrderData($testData, OrderConst::ORDER_STATE_WAIT_IDENTIFY);
        
        // 先添加一个输出字段
        OrderItemFieldModel::query()->insert([
            'order_id' => $orderData['orderId'],
            'order_item_id' => $orderData['orderItemId'],
            'biz_type' => TemplateConst::BIZ_TYPE_OUTPUT,
            'field_id' => $testData['itemFieldId'],
            'field_name' => '测试字段',
            'field_key' => 'testField',
            'field_value' => '旧值',
            'field_type' => 'string',
            'output_type' => OrderConst::OUTPUT_TYPE_ORDER_ITEM,
            'create_time' => time(),
        ]);
        
        // 提交订单
        $params = [
            'uri' => $orderData['uri'],
            'identResult' => '真品',
            'identTruth' => 1,
            'items' => [
                [
                    'testField' => '新值'
                ]
            ],
            'orderField' => '订单字段值'
        ];
        
        $this->orderLogic->submit($params);
        
        // 验证旧字段已被清除，新字段已添加
        $fields = OrderItemFieldModel::query()
            ->where('order_id', $orderData['orderId'])
            ->where('biz_type', TemplateConst::BIZ_TYPE_OUTPUT)
            ->get();
        
        $testFieldFound = false;
        foreach ($fields as $field) {
            if ($field->field_key == 'testField') {
                $this->assertEquals('新值', $field->field_value);
                $testFieldFound = true;
            }
        }
        
        $this->assertTrue($testFieldFound, '未找到测试字段');
    }

    /**
     * 插入测试基础数据
     *
     * @return array
     */
    private function insertTestData()
    {
        // 创建业务数据
        $businessId = BusinessModel::query()->insertGetId([
            'name' => '测试业务',
            'status' => 1,
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        // 创建业务类目数据
        $categoryId = BusinessCategoryModel::query()->insertGetId([
            'business_id' => $businessId,
            'category_name' => '测试类目',
            'category_identifier' => 'test_category',
            'input_template_id' => 0,
            'output_template_id' => 0,
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        // 创建输入模板
        $inputTemplateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => $businessId,
            'name' => '测试输入模板',
            'biz_type' => TemplateConst::BIZ_TYPE_INPUT,
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        // 创建输出模板
        $outputTemplateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => $businessId,
            'name' => '测试输出模板',
            'biz_type' => TemplateConst::BIZ_TYPE_OUTPUT,
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        // 更新类目模板ID
        BusinessCategoryModel::query()->where('id', $categoryId)->update([
            'input_template_id' => $inputTemplateId,
            'output_template_id' => $outputTemplateId,
        ]);

        // 创建业务字段
        $itemFieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => $businessId,
            'name' => '测试字段',
            'field_key' => 'testField',
            'field_type' => 'string',
            'options' => '',
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        $orderFieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => $businessId,
            'name' => '订单字段',
            'field_key' => 'orderField',
            'field_type' => 'string',
            'options' => '',
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        // 创建模板字段关联
        BusinessTemplateFieldModel::query()->insert([
            'template_id' => $inputTemplateId,
            'field_id' => $itemFieldId,
            'field_key' => 'testField',
            'output_type' => OrderConst::OUTPUT_TYPE_ORDER_ITEM,
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        BusinessTemplateFieldModel::query()->insert([
            'template_id' => $inputTemplateId,
            'field_id' => $orderFieldId,
            'field_key' => 'orderField',
            'output_type' => OrderConst::OUTPUT_TYPE_ORDER,
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        // 创建拒绝原因字段
        $rejectFieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => $businessId,
            'name' => '拒绝原因',
            'field_key' => 'rejectReason',
            'field_type' => 'string',
            'options' => '',
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        BusinessTemplateFieldModel::query()->insert([
            'template_id' => $inputTemplateId,
            'field_id' => $rejectFieldId,
            'field_key' => 'rejectReason',
            'output_type' => OrderConst::OUTPUT_TYPE_ORDER,
            'create_time' => time(),
            'is_deleted' => 0,
        ]);

        return [
            'businessId' => $businessId,
            'categoryId' => $categoryId,
            'inputTemplateId' => $inputTemplateId,
            'outputTemplateId' => $outputTemplateId,
            'itemFieldId' => $itemFieldId,
            'orderFieldId' => $orderFieldId,
            'rejectFieldId' => $rejectFieldId,
        ];
    }

    /**
     * 创建订单数据
     *
     * @param array $testData 测试数据
     * @param int $state 订单状态
     * @return array
     */
    private function createOrderData(array $testData, int $state = OrderConst::ORDER_STATE_WAIT_IDENTIFY)
    {
        $uri = 'test_uri_' . time();
        $orderId = OrderModel::query()->insertGetId([
            'uri' => $uri,
            'business_id' => $testData['businessId'],
            'business_no' => 'BN001',
            'business_master_no' => 'BMN001',
            'category_identifier' => 'test_category',
            'category_id' => $testData['categoryId'],
            'state' => $state,
            'input_template_id' => $testData['inputTemplateId'],
            'output_template_id' => $testData['outputTemplateId'],
            'userinfo_id' => 1001,
            'cover' => 'test_cover.jpg',
            'accept_time' => time(),
            'create_time' => time(),
            'detail_json' => '{}',
            'is_deleted' => 0,
        ]);

        // 创建订单项
        $orderItemId = OrderItemModel::query()->insertGetId([
            'order_id' => $orderId,
            'imgs' => json_encode(['img1.jpg', 'img2.jpg']),
            'video' => json_encode([['videoUrl' => 'video1.mp4']]),
            'remark' => '测试备注',
            'create_time' => time(),
        ]);

        return [
            'orderId' => $orderId,
            'orderItemId' => $orderItemId,
            'uri' => $uri,
        ];
    }
} 