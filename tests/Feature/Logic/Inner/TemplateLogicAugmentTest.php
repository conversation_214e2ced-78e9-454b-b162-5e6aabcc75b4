<?php

namespace Tests\Feature\Logic\Inner;

use App\Constants\CommonConst;
use App\ErrCode\BaseErr;
use App\ErrCode\FieldErr;
use App\ErrCode\TemplateErr;
use App\Exceptions\ErrException;
use App\Logic\Inner\TemplateLogic;
use App\Models\BusinessFieldModel;
use App\Models\BusinessModel;
use App\Models\BusinessTemplateFieldModel;
use App\Models\BusinessTemplateModel;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;

class TemplateLogicAugmentTest extends TestCase
{
    use DatabaseTransactions;

    protected $templateLogic;

    protected function setUp(): void
    {
        parent::setUp();
        $this->templateLogic = TemplateLogic::getInstance();
    }

    /**
     * Test list method with successful pagination
     */
    public function testListSuccess()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create test templates
        $templates = [
            [
                'business_id' => $businessId,
                'template_name' => 'Template 1',
                'biz_type' => 'input',
                'state' => CommonConst::STATE_ACTIVE,
                'is_deleted' => CommonConst::NOT_DELETED,
                'create_time' => time()
            ],
            [
                'business_id' => $businessId,
                'template_name' => 'Template 2',
                'biz_type' => 'output',
                'state' => CommonConst::STATE_ACTIVE,
                'is_deleted' => CommonConst::NOT_DELETED,
                'create_time' => time()
            ]
        ];
        BusinessTemplateModel::query()->insert($templates);

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId
        ];

        $result = $this->templateLogic->list($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('page', $result);
        $this->assertArrayHasKey('pageSize', $result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('isEnd', $result);
        $this->assertEquals(2, $result['page']);
        $this->assertEquals(10, $result['pageSize']);
        $this->assertCount(2, $result['list']);
        $this->assertTrue($result['isEnd']);
    }

    /**
     * Test list method with filters
     */
    public function testListWithFilters()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create test templates with different states and types
        BusinessTemplateModel::query()->insert([
            'business_id' => $businessId,
            'template_name' => 'Active Input Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        BusinessTemplateModel::query()->insert([
            'business_id' => $businessId,
            'template_name' => 'Disabled Output Template',
            'biz_type' => 'output',
            'state' => CommonConst::STATE_DISABLE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Test filter by bizType
        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId,
            'bizType' => 'input'
        ];

        $result = $this->templateLogic->list($params);
        $this->assertCount(1, $result['list']);
        $this->assertEquals('input', $result['list'][0]['bizType']);

        // Test filter by state
        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId,
            'state' => CommonConst::STATE_DISABLE
        ];

        $result = $this->templateLogic->list($params);
        $this->assertCount(1, $result['list']);
        $this->assertEquals(CommonConst::STATE_DISABLE, $result['list'][0]['state']);
    }

    /**
     * Test list method with empty results
     */
    public function testListEmpty()
    {
        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => 999999
        ];

        $result = $this->templateLogic->list($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result['list']);
        $this->assertTrue($result['isEnd']);
    }

    /**
     * Test list method with boundary conditions
     */
    public function testListBoundaryConditions()
    {
        // Test with page = 0 (should be converted to 1)
        $params = [
            'page' => 0,
            'pageSize' => 10
        ];

        $result = $this->templateLogic->list($params);
        $this->assertEquals(2, $result['page']); // page + 1

        // Test with negative page (should be converted to 1)
        $params = [
            'page' => -5,
            'pageSize' => 10
        ];

        $result = $this->templateLogic->list($params);
        $this->assertEquals(2, $result['page']); // page + 1

        // Test with pageSize = 0
        $params = [
            'page' => 1,
            'pageSize' => 0
        ];

        $result = $this->templateLogic->list($params);
        $this->assertEquals(0, $result['pageSize']);
    }

    /**
     * Test create method success
     */
    public function testCreateSuccess()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        $params = [
            'businessId' => $businessId,
            'templateName' => 'Test Template',
            'bizType' => 'input'
        ];

        $result = $this->templateLogic->create($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template was created
        $template = BusinessTemplateModel::query()
            ->where('business_id', $businessId)
            ->where('template_name', 'Test Template')
            ->first();

        $this->assertNotNull($template);
        $this->assertEquals('input', $template->biz_type);
        $this->assertEquals(CommonConst::STATE_ACTIVE, $template->state);
        $this->assertEquals(CommonConst::NOT_DELETED, $template->is_deleted);
    }

    /**
     * Test create method with non-existent business
     */
    public function testCreateWithNonExistentBusiness()
    {
        $params = [
            'businessId' => 999999,
            'templateName' => 'Test Template',
            'bizType' => 'input'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->create($params);
    }

    /**
     * Test create method with duplicate template name
     */
    public function testCreateWithDuplicateTemplateName()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create existing template
        BusinessTemplateModel::query()->insert([
            'business_id' => $businessId,
            'template_name' => 'Existing Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'businessId' => $businessId,
            'templateName' => 'Existing Template',
            'bizType' => 'output'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(TemplateErr::TEMPLATE_EXISTS[0]);
        $this->expectExceptionMessage('模板已存在');

        $this->templateLogic->create($params);
    }

    /**
     * Test edit method success
     */
    public function testEditSuccess()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Original Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'id' => $templateId,
            'templateName' => 'Updated Template'
        ];

        $result = $this->templateLogic->edit($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template was updated
        $template = BusinessTemplateModel::query()->find($templateId);
        $this->assertEquals('Updated Template', $template->template_name);
    }

    /**
     * Test edit method with non-existent template
     */
    public function testEditWithNonExistentTemplate()
    {
        $params = [
            'id' => 999999,
            'templateName' => 'Updated Template'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->edit($params);
    }

    /**
     * Test detail method success
     */
    public function testDetailSuccess()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => $businessId,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $result = $this->templateLogic->detail($templateId);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('templateName', $result);
        $this->assertArrayHasKey('bizType', $result);
        $this->assertArrayHasKey('state', $result);
        $this->assertArrayHasKey('fields', $result);
        $this->assertEquals($templateId, $result['id']);
        $this->assertEquals('Test Template', $result['templateName']);
        $this->assertEquals('input', $result['bizType']);
    }

    /**
     * Test detail method with non-existent template
     */
    public function testDetailWithNonExistentTemplate()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->detail(999999);
    }

    /**
     * Test enable method success
     */
    public function testEnableSuccess()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_DISABLE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $result = $this->templateLogic->enable($templateId, CommonConst::STATE_ACTIVE);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template state was updated
        $template = BusinessTemplateModel::query()->find($templateId);
        $this->assertEquals(CommonConst::STATE_ACTIVE, $template->state);
    }

    /**
     * Test enable method with non-existent template
     */
    public function testEnableWithNonExistentTemplate()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->enable(999999, CommonConst::STATE_ACTIVE);
    }

    /**
     * Test enable method with invalid state
     */
    public function testEnableWithInvalidState()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->enable($templateId, 2); // Invalid state
    }

    /**
     * Test enable method with boundary state values
     */
    public function testEnableWithBoundaryStates()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Test with state 0
        $result = $this->templateLogic->enable($templateId, 0);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        $template = BusinessTemplateModel::query()->find($templateId);
        $this->assertEquals(0, $template->state);

        // Test with state 1
        $result = $this->templateLogic->enable($templateId, 1);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        $template = BusinessTemplateModel::query()->find($templateId);
        $this->assertEquals(1, $template->state);
    }

    /**
     * Test addField method success
     */
    public function testAddFieldSuccess()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Create test field
        $fieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => 1,
            'name' => 'Test Field',
            'field_type' => 'text',
            'field_key' => 'test_field',
            'placeholder' => 'Enter test value',
            'max_length' => 100,
            'biz_type' => 'input',
            'is_required' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'templateId' => $templateId,
            'fieldId' => $fieldId,
            'outputType' => 1,
            'parentId' => 0,
            'parentOptionName' => ''
        ];

        $result = $this->templateLogic->addField($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template field was created
        $templateField = BusinessTemplateFieldModel::query()
            ->where('template_id', $templateId)
            ->where('field_id', $fieldId)
            ->first();

        $this->assertNotNull($templateField);
        $this->assertEquals($templateId, $templateField->template_id);
        $this->assertEquals($fieldId, $templateField->field_id);
        $this->assertEquals('test_field', $templateField->field_key);
        $this->assertEquals(1, $templateField->output_type);
    }

    /**
     * Test addField method with non-existent template
     */
    public function testAddFieldWithNonExistentTemplate()
    {
        // Create test field
        $fieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => 1,
            'name' => 'Test Field',
            'field_type' => 'text',
            'field_key' => 'test_field',
            'placeholder' => 'Enter test value',
            'max_length' => 100,
            'biz_type' => 'input',
            'is_required' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'templateId' => 999999,
            'fieldId' => $fieldId,
            'outputType' => 1
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->addField($params);
    }

    /**
     * Test addField method with non-existent field
     */
    public function testAddFieldWithNonExistentField()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'templateId' => $templateId,
            'fieldId' => 999999,
            'outputType' => 1
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(FieldErr::FIELD_NOT_EXISTS[0]);
        $this->expectExceptionMessage('field字段不存在');

        $this->templateLogic->addField($params);
    }

    /**
     * Test addField method with optional parameters
     */
    public function testAddFieldWithOptionalParameters()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Create test field
        $fieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => 1,
            'name' => 'Test Field',
            'field_type' => 'text',
            'field_key' => 'test_field',
            'placeholder' => 'Enter test value',
            'max_length' => 100,
            'biz_type' => 'input',
            'is_required' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'templateId' => $templateId,
            'fieldId' => $fieldId,
            'outputType' => 2,
            'parentId' => 5,
            'parentOptionName' => 'Parent Option'
        ];

        $result = $this->templateLogic->addField($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template field was created with optional parameters
        $templateField = BusinessTemplateFieldModel::query()
            ->where('template_id', $templateId)
            ->where('field_id', $fieldId)
            ->first();

        $this->assertNotNull($templateField);
        $this->assertEquals(5, $templateField->parent_id);
        $this->assertEquals('Parent Option', $templateField->parent_option_name);
        $this->assertEquals(2, $templateField->output_type);
    }

    /**
     * Test editField method success
     */
    public function testEditFieldSuccess()
    {
        // Create test template field
        $templateFieldId = BusinessTemplateFieldModel::query()->insertGetId([
            'template_id' => 1,
            'parent_id' => 0,
            'parent_option_name' => '',
            'field_id' => 1,
            'field_key' => 'test_field',
            'output_type' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'sort' => 1,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'id' => $templateFieldId,
            'outputType' => 2,
            'parentId' => 5,
            'parentOptionName' => 'Updated Parent Option'
        ];

        $result = $this->templateLogic->editField($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template field was updated
        $templateField = BusinessTemplateFieldModel::query()->find($templateFieldId);
        $this->assertEquals(2, $templateField->output_type);
        $this->assertEquals(5, $templateField->parent_id);
        $this->assertEquals('Updated Parent Option', $templateField->parent_option_name);
    }

    /**
     * Test editField method with non-existent template field
     */
    public function testEditFieldWithNonExistentTemplateField()
    {
        $params = [
            'id' => 999999,
            'outputType' => 2,
            'parentId' => 5,
            'parentOptionName' => 'Updated Parent Option'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->editField($params);
    }

    /**
     * Test editField method with optional parameters only
     */
    public function testEditFieldWithOptionalParametersOnly()
    {
        // Create test template field
        $templateFieldId = BusinessTemplateFieldModel::query()->insertGetId([
            'template_id' => 1,
            'parent_id' => 0,
            'parent_option_name' => '',
            'field_id' => 1,
            'field_key' => 'test_field',
            'output_type' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'sort' => 1,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'id' => $templateFieldId,
            'outputType' => 2
        ];

        $result = $this->templateLogic->editField($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template field was updated
        $templateField = BusinessTemplateFieldModel::query()->find($templateFieldId);
        $this->assertEquals(2, $templateField->output_type);
        $this->assertEquals(0, $templateField->parent_id); // Should remain default
        $this->assertEquals('', $templateField->parent_option_name); // Should remain default
    }

    /**
     * Test delField method success
     */
    public function testDelFieldSuccess()
    {
        // Create test template field
        $templateFieldId = BusinessTemplateFieldModel::query()->insertGetId([
            'template_id' => 1,
            'parent_id' => 0,
            'parent_option_name' => '',
            'field_id' => 1,
            'field_key' => 'test_field',
            'output_type' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'sort' => 1,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $result = $this->templateLogic->delField($templateFieldId);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template field was marked as deleted
        $templateField = BusinessTemplateFieldModel::query()->find($templateFieldId);
        $this->assertEquals(CommonConst::DELETED, $templateField->is_deleted);
    }

    /**
     * Test delField method with non-existent template field
     */
    public function testDelFieldWithNonExistentTemplateField()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->delField(999999);
    }

    /**
     * Test create method with boundary conditions
     */
    public function testCreateBoundaryConditions()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Test with empty template name
        $params = [
            'businessId' => $businessId,
            'templateName' => '',
            'bizType' => 'input'
        ];

        $result = $this->templateLogic->create($params);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Test with very long template name (within database limits)
        $longName = str_repeat('A', 100); // Reduced to fit database constraints
        $params = [
            'businessId' => $businessId,
            'templateName' => $longName,
            'bizType' => 'output'
        ];

        $result = $this->templateLogic->create($params);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Test with special characters in template name
        $params = [
            'businessId' => $businessId,
            'templateName' => 'Template!@#$%^&*()_+-={}[]|\\:";\'<>?,./~`',
            'bizType' => 'input'
        ];

        $result = $this->templateLogic->create($params);
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Test edit method with boundary conditions
     */
    public function testEditBoundaryConditions()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Original Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Test with empty template name
        $params = [
            'id' => $templateId,
            'templateName' => ''
        ];

        $result = $this->templateLogic->edit($params);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Test with very long template name
        $longName = str_repeat('B', 10);
        $params = [
            'id' => $templateId,
            'templateName' => $longName
        ];

        $result = $this->templateLogic->edit($params);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template was updated
        $template = BusinessTemplateModel::query()->find($templateId);
        $this->assertEquals($longName, $template->template_name);
    }

    /**
     * Test list method with large page numbers
     */
    public function testListWithLargePageNumbers()
    {
        $params = [
            'page' => 999999,
            'pageSize' => 10
        ];

        $result = $this->templateLogic->list($params);

        $this->assertIsArray($result);
        $this->assertEquals(1000000, $result['page']); // page + 1
        $this->assertEmpty($result['list']);
        $this->assertTrue($result['isEnd']);
    }

    /**
     * Test list method with very large page size
     */
    public function testListWithLargePageSize()
    {
        $params = [
            'page' => 1,
            'pageSize' => 999999
        ];

        $result = $this->templateLogic->list($params);

        $this->assertIsArray($result);
        $this->assertEquals(999999, $result['pageSize']);
        $this->assertTrue($result['isEnd']);
    }

    /**
     * Test enable method with negative state values
     */
    public function testEnableWithNegativeState()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->enable($templateId, -1);
    }

    /**
     * Test addField method with boundary output types
     */
    public function testAddFieldWithBoundaryOutputTypes()
    {
        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => 1,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Create test field
        $fieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => 1,
            'name' => 'Test Field',
            'field_type' => 'text',
            'field_key' => 'test_field',
            'placeholder' => 'Enter test value',
            'max_length' => 100,
            'biz_type' => 'input',
            'is_required' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Test with outputType = 0
        $params = [
            'templateId' => $templateId,
            'fieldId' => $fieldId,
            'outputType' => 0
        ];

        $result = $this->templateLogic->addField($params);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Test with very large outputType
        $params = [
            'templateId' => $templateId,
            'fieldId' => $fieldId,
            'outputType' => 999999
        ];

        $result = $this->templateLogic->addField($params);
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Test list method with all filters combined
     */
    public function testListWithAllFiltersCombined()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create test templates with different combinations
        BusinessTemplateModel::query()->insert([
            [
                'business_id' => $businessId,
                'template_name' => 'Active Input Template',
                'biz_type' => 'input',
                'state' => CommonConst::STATE_ACTIVE,
                'is_deleted' => CommonConst::NOT_DELETED,
                'create_time' => time()
            ],
            [
                'business_id' => $businessId,
                'template_name' => 'Disabled Input Template',
                'biz_type' => 'input',
                'state' => CommonConst::STATE_DISABLE,
                'is_deleted' => CommonConst::NOT_DELETED,
                'create_time' => time()
            ],
            [
                'business_id' => $businessId,
                'template_name' => 'Active Output Template',
                'biz_type' => 'output',
                'state' => CommonConst::STATE_ACTIVE,
                'is_deleted' => CommonConst::NOT_DELETED,
                'create_time' => time()
            ]
        ]);

        // Test with all filters
        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId,
            'bizType' => 'input',
            'state' => CommonConst::STATE_ACTIVE
        ];

        $result = $this->templateLogic->list($params);
        $this->assertCount(1, $result['list']);
        $this->assertEquals('Active Input Template', $result['list'][0]['templateName']);
        $this->assertEquals('input', $result['list'][0]['bizType']);
        $this->assertEquals(CommonConst::STATE_ACTIVE, $result['list'][0]['state']);
    }

    /**
     * Test list method excludes deleted templates
     */
    public function testListExcludesDeletedTemplates()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create active and deleted templates
        BusinessTemplateModel::query()->insert([
            [
                'business_id' => $businessId,
                'template_name' => 'Active Template',
                'biz_type' => 'input',
                'state' => CommonConst::STATE_ACTIVE,
                'is_deleted' => CommonConst::NOT_DELETED,
                'create_time' => time()
            ],
            [
                'business_id' => $businessId,
                'template_name' => 'Deleted Template',
                'biz_type' => 'input',
                'state' => CommonConst::STATE_ACTIVE,
                'is_deleted' => CommonConst::DELETED,
                'create_time' => time()
            ]
        ]);

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId
        ];

        $result = $this->templateLogic->list($params);
        $this->assertCount(1, $result['list']);
        $this->assertEquals('Active Template', $result['list'][0]['templateName']);
    }

    /**
     * Test detail method with template containing fields
     */
    public function testDetailWithTemplateFields()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create test template
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => $businessId,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Create test field
        $fieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => $businessId,
            'name' => 'Test Field',
            'field_type' => 'text',
            'field_key' => 'test_field',
            'placeholder' => 'Enter test value',
            'max_length' => 100,
            'biz_type' => 'input',
            'is_required' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        // Create template field
        BusinessTemplateFieldModel::query()->insert([
            'template_id' => $templateId,
            'parent_id' => 0,
            'parent_option_name' => '',
            'field_id' => $fieldId,
            'field_key' => 'test_field',
            'output_type' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'sort' => 1,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $result = $this->templateLogic->detail($templateId);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('fields', $result);
        $this->assertCount(1, $result['fields']);
        $this->assertEquals($fieldId, $result['fields'][0]['fieldId']);
        $this->assertEquals('test_field', $result['fields'][0]['fieldKey']);
        $this->assertEquals(1, $result['fields'][0]['outputType']);
    }

    /**
     * Test editField with string ID conversion
     */
    public function testEditFieldWithStringId()
    {
        // Create test template field
        $templateFieldId = BusinessTemplateFieldModel::query()->insertGetId([
            'template_id' => 1,
            'parent_id' => 0,
            'parent_option_name' => '',
            'field_id' => 1,
            'field_key' => 'test_field',
            'output_type' => 1,
            'state' => CommonConst::STATE_ACTIVE,
            'sort' => 1,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'id' => (string)$templateFieldId, // String ID
            'outputType' => 2
        ];

        $result = $this->templateLogic->editField($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify template field was updated
        $templateField = BusinessTemplateFieldModel::query()->find($templateFieldId);
        $this->assertEquals(2, $templateField->output_type);
    }

    /**
     * Test list method format output structure
     */
    public function testListFormatOutputStructure()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create test template
        BusinessTemplateModel::query()->insert([
            'business_id' => $businessId,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId
        ];

        $result = $this->templateLogic->list($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('page', $result);
        $this->assertArrayHasKey('pageSize', $result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('isEnd', $result);

        // Check list item structure
        $this->assertCount(1, $result['list']);
        $item = $result['list'][0];
        $this->assertArrayHasKey('id', $item);
        $this->assertArrayHasKey('businessId', $item);
        $this->assertArrayHasKey('businessName', $item);
        $this->assertArrayHasKey('templateName', $item);
        $this->assertArrayHasKey('bizType', $item);
        $this->assertArrayHasKey('state', $item);
        $this->assertArrayHasKey('createTime', $item);

        $this->assertEquals($businessId, $item['businessId']);
        $this->assertEquals('Test Business', $item['businessName']);
        $this->assertEquals('Test Template', $item['templateName']);
        $this->assertEquals('input', $item['bizType']);
        $this->assertEquals(CommonConst::STATE_ACTIVE, $item['state']);
    }

    /**
     * Test list method with non-existent business (orphaned templates)
     */
    public function testListWithOrphanedTemplates()
    {
        // Create template with non-existent business_id
        BusinessTemplateModel::query()->insert([
            'business_id' => 999999, // Non-existent business
            'template_name' => 'Orphaned Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => 999999
        ];

        $result = $this->templateLogic->list($params);

        $this->assertIsArray($result);
        $this->assertCount(1, $result['list']);

        $item = $result['list'][0];
        $this->assertEquals('Orphaned Template', $item['templateName']);
        $this->assertEquals('', $item['businessName']); // Should be empty for non-existent business
        $this->assertEquals(999999, $item['businessId']);
    }

    /**
     * Test list method with non-numeric state filter
     */
    public function testListWithNonNumericState()
    {
        // Create test business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Create test template
        BusinessTemplateModel::query()->insert([
            'business_id' => $businessId,
            'template_name' => 'Test Template',
            'biz_type' => 'input',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time()
        ]);

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $businessId,
            'state' => 'invalid_state' // Non-numeric state
        ];

        $result = $this->templateLogic->list($params);

        // Should return all templates since non-numeric state is ignored
        $this->assertIsArray($result);
        $this->assertCount(1, $result['list']);
    }

    /**
     * Test detail method with zero ID
     */
    public function testDetailWithZeroId()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->detail(0);
    }

    /**
     * Test enable method with zero ID
     */
    public function testEnableWithZeroId()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->enable(0, CommonConst::STATE_ACTIVE);
    }

    /**
     * Test delField method with zero ID
     */
    public function testDelFieldWithZeroId()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->delField(0);
    }
}
