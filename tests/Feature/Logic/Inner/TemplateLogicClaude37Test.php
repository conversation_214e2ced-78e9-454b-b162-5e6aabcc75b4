<?php

namespace Tests\Feature\Logic\Inner;

use App\Constants\CommonConst;
use App\ErrCode\BaseErr;
use App\ErrCode\FieldErr;
use App\ErrCode\TemplateErr;
use App\Exceptions\ErrException;
use App\Logic\Inner\TemplateLogic;
use App\Models\BusinessFieldModel;
use App\Models\BusinessFieldOptionsModel;
use App\Models\BusinessModel;
use App\Models\BusinessTemplateFieldModel;
use App\Models\BusinessTemplateModel;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;

class TemplateLogicClaude37Test extends TestCase
{
    use DatabaseTransactions;

    protected $templateLogic;

    protected function setUp(): void
    {
        parent::setUp();
        $this->templateLogic = TemplateLogic::getInstance();
    }

    /**
     * Test template list with valid parameters
     */
    public function testListSuccess()
    {
        // Insert test data
        $testData = $this->insertTestData();

        $params = [
            'businessId' => $testData['businessId'],
            'page' => 1,
            'pageSize' => 10,
        ];

        $result = $this->templateLogic->list($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('page', $result);
        $this->assertArrayHasKey('pageSize', $result);
        $this->assertArrayHasKey('isEnd', $result);
        $this->assertGreaterThan(0, count($result['list']));
        $this->assertEquals(2, $result['page']);
        $this->assertEquals(10, $result['pageSize']);
    }

    /**
     * Test template list with state filter
     */
    public function testListWithStateFilter()
    {
        $testData = $this->insertTestData();

        // Create a disabled template
        BusinessTemplateModel::query()->insert([
            'business_id' => $testData['businessId'],
            'template_name' => 'Disabled Template',
            'biz_type' => 'test_type',
            'state' => CommonConst::STATE_DISABLE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time(),
        ]);

        $params = [
            'businessId' => $testData['businessId'],
            'state' => CommonConst::STATE_ACTIVE,
            'page' => 1,
            'pageSize' => 10,
        ];

        $result = $this->templateLogic->list($params);

        foreach ($result['list'] as $item) {
            $this->assertEquals(CommonConst::STATE_ACTIVE, $item['state']);
        }
    }

    /**
     * Test template list with business ID filter
     */
    public function testListWithBusinessFilter()
    {
        $testData = $this->insertTestData();

        // Create another business and template
        $business2Id = BusinessModel::query()->insertGetId([
            'name' => 'Another Business',
            'notify_url' => 'http://example.com/notify2',
            'create_time' => time(),
        ]);

        BusinessTemplateModel::query()->insert([
            'business_id' => $business2Id,
            'template_name' => 'Another Template',
            'biz_type' => 'test_type',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time(),
        ]);

        $params = [
            'businessId' => $testData['businessId'],
            'page' => 1,
            'pageSize' => 10,
        ];

        $result = $this->templateLogic->list($params);

        foreach ($result['list'] as $item) {
            $this->assertEquals($testData['businessId'], $item['businessId']);
        }
    }

    /**
     * Test template list with biz type filter
     */
    public function testListWithBizTypeFilter()
    {
        $testData = $this->insertTestData();

        // Create another template with different biz_type
        BusinessTemplateModel::query()->insert([
            'business_id' => $testData['businessId'],
            'template_name' => 'Another Template',
            'biz_type' => 'different_type',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time(),
        ]);

        $params = [
            'businessId' => $testData['businessId'],
            'bizType' => 'test_type',
            'page' => 1,
            'pageSize' => 10,
        ];

        $result = $this->templateLogic->list($params);

        foreach ($result['list'] as $item) {
            $this->assertEquals('test_type', $item['bizType']);
        }
    }

    /**
     * Test create template successfully
     */
    public function testCreateSuccess()
    {
        $testData = $this->insertTestData();

        $params = [
            'businessId' => $testData['businessId'],
            'templateName' => 'New Template',
            'bizType' => 'new_type',
        ];

        $result = $this->templateLogic->create($params);

        $this->assertIsArray($result);

        // Verify template was created in database
        $template = BusinessTemplateModel::query()
            ->where('business_id', $testData['businessId'])
            ->where('template_name', 'New Template')
            ->where('biz_type', 'new_type')
            ->first();

        $this->assertNotNull($template);
        $this->assertEquals('New Template', $template->template_name);
        $this->assertEquals('new_type', $template->biz_type);
        $this->assertEquals(CommonConst::STATE_ACTIVE, $template->state);
    }

    /**
     * Test create template with invalid business ID
     */
    public function testCreateWithInvalidBusinessId()
    {
        $params = [
            'businessId' => 99999,  // Non-existent business ID
            'templateName' => 'New Template',
            'bizType' => 'new_type',
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->create($params);
    }

    /**
     * Test create template with duplicate name
     */
    public function testCreateWithDuplicateName()
    {
        $testData = $this->insertTestData();

        $params = [
            'businessId' => $testData['businessId'],
            'templateName' => $testData['templateName'],  // Same as existing template
            'bizType' => 'new_type',
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(TemplateErr::TEMPLATE_EXISTS[0]);
        $this->expectExceptionMessage('模板已存在');

        $this->templateLogic->create($params);
    }

    /**
     * Test edit template successfully
     */
    public function testEditSuccess()
    {
        $testData = $this->insertTestData();

        $params = [
            'id' => $testData['templateId'],
            'templateName' => 'Updated Template Name',
        ];

        $result = $this->templateLogic->edit($params);

        $this->assertIsArray($result);

        // Verify template was updated in database
        $template = BusinessTemplateModel::query()->find($testData['templateId']);
        $this->assertEquals('Updated Template Name', $template->template_name);
    }

    /**
     * Test edit template with invalid template ID
     */
    public function testEditWithInvalidTemplateId()
    {
        $params = [
            'id' => 99999,  // Non-existent template ID
            'templateName' => 'Updated Template Name',
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->edit($params);
    }

    /**
     * Test get template detail successfully
     */
    public function testDetailSuccess()
    {
        $testData = $this->insertTestData();

        $result = $this->templateLogic->detail($testData['templateId']);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('templateName', $result);
        $this->assertArrayHasKey('bizType', $result);
        $this->assertArrayHasKey('state', $result);
        $this->assertArrayHasKey('fields', $result);
        $this->assertEquals($testData['templateId'], $result['id']);
        $this->assertEquals($testData['templateName'], $result['templateName']);
    }

    /**
     * Test get template detail with invalid template ID
     */
    public function testDetailWithInvalidTemplateId()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->detail(99999);  // Non-existent template ID
    }

    /**
     * Test enable/disable template successfully
     */
    public function testEnableSuccess()
    {
        $testData = $this->insertTestData();

        // Disable template
        $result = $this->templateLogic->enable($testData['templateId'], CommonConst::STATE_DISABLE);

        $this->assertIsArray($result);

        // Verify template was disabled in database
        $template = BusinessTemplateModel::query()->find($testData['templateId']);
        $this->assertEquals(CommonConst::STATE_DISABLE, $template->state);

        // Enable template
        $result = $this->templateLogic->enable($testData['templateId'], CommonConst::STATE_ACTIVE);

        $this->assertIsArray($result);

        // Verify template was enabled in database
        $template = BusinessTemplateModel::query()->find($testData['templateId']);
        $this->assertEquals(CommonConst::STATE_ACTIVE, $template->state);
    }

    /**
     * Test enable/disable template with invalid template ID
     */
    public function testEnableWithInvalidTemplateId()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->enable(99999, CommonConst::STATE_ACTIVE);  // Non-existent template ID
    }

    /**
     * Test enable/disable template with invalid state
     */
    public function testEnableWithInvalidState()
    {
        $testData = $this->insertTestData();

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->enable($testData['templateId'], 2);  // Invalid state value
    }

    /**
     * Test add field to template successfully
     */
    public function testAddFieldSuccess()
    {
        $testData = $this->insertTestData();

        $params = [
            'templateId' => $testData['templateId'],
            'fieldId' => $testData['fieldId'],
            'outputType' => 1,
        ];

        $result = $this->templateLogic->addField($params);

        $this->assertIsArray($result);

        // Verify field was added to template in database
        $templateField = BusinessTemplateFieldModel::query()
            ->where('template_id', $testData['templateId'])
            ->where('field_id', $testData['fieldId'])
            ->first();

        $this->assertNotNull($templateField);
        $this->assertEquals(1, $templateField->output_type);
        $this->assertEquals(0, $templateField->parent_id);
        $this->assertEquals('', $templateField->parent_option_name);
    }

    /**
     * Test add field with parent ID
     */
    public function testAddFieldWithParent()
    {
        $testData = $this->insertTestData();

        $params = [
            'templateId' => $testData['templateId'],
            'fieldId' => $testData['fieldId'],
            'outputType' => 1,
            'parentId' => 5,
            'parentOptionName' => 'Parent Option',
        ];

        $result = $this->templateLogic->addField($params);

        $this->assertIsArray($result);

        // Verify field was added to template in database
        $templateField = BusinessTemplateFieldModel::query()
            ->where('template_id', $testData['templateId'])
            ->where('field_id', $testData['fieldId'])
            ->first();

        $this->assertNotNull($templateField);
        $this->assertEquals(5, $templateField->parent_id);
        $this->assertEquals('Parent Option', $templateField->parent_option_name);
    }

    /**
     * Test add field with invalid template ID
     */
    public function testAddFieldWithInvalidTemplateId()
    {
        $testData = $this->insertTestData();

        $params = [
            'templateId' => 99999,  // Non-existent template ID
            'fieldId' => $testData['fieldId'],
            'outputType' => 1,
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->addField($params);
    }

    /**
     * Test add field with invalid field ID
     */
    public function testAddFieldWithInvalidFieldId()
    {
        $testData = $this->insertTestData();

        $params = [
            'templateId' => $testData['templateId'],
            'fieldId' => 99999,  // Non-existent field ID
            'outputType' => 1,
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(FieldErr::FIELD_NOT_EXISTS[0]);
        $this->expectExceptionMessage('field字段不存在');

        $this->templateLogic->addField($params);
    }

    /**
     * Test edit template field successfully
     */
    public function testEditFieldSuccess()
    {
        $testData = $this->insertTestData();

        // Add a field first
        $fieldId = BusinessTemplateFieldModel::query()->insertGetId([
            'template_id' => $testData['templateId'],
            'field_id' => $testData['fieldId'],
            'field_key' => 'test_key',
            'output_type' => 1,
            'parent_id' => 0,
            'parent_option_name' => '',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time(),
        ]);

        $params = [
            'id' => $fieldId,
            'outputType' => 2,
            'parentId' => 10,
            'parentOptionName' => 'Updated Parent Option',
        ];

        $result = $this->templateLogic->editField($params);

        $this->assertIsArray($result);

        // Verify field was updated in database
        $templateField = BusinessTemplateFieldModel::query()->find($fieldId);
        $this->assertEquals(2, $templateField->output_type);
        $this->assertEquals(10, $templateField->parent_id);
        $this->assertEquals('Updated Parent Option', $templateField->parent_option_name);
    }

    /**
     * Test edit template field with invalid field ID
     */
    public function testEditFieldWithInvalidFieldId()
    {
        $params = [
            'id' => 99999,  // Non-existent field ID
            'outputType' => 2,
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->editField($params);
    }

    /**
     * Test delete template field successfully
     */
    public function testDelFieldSuccess()
    {
        $testData = $this->insertTestData();

        // Add a field first
        $fieldId = BusinessTemplateFieldModel::query()->insertGetId([
            'template_id' => $testData['templateId'],
            'field_id' => $testData['fieldId'],
            'field_key' => 'test_key',
            'output_type' => 1,
            'parent_id' => 0,
            'parent_option_name' => '',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time(),
        ]);

        $result = $this->templateLogic->delField($fieldId);

        $this->assertIsArray($result);

        // Verify field was marked as deleted in database
        $templateField = BusinessTemplateFieldModel::query()->find($fieldId);
        $this->assertEquals(CommonConst::DELETED, $templateField->is_deleted);
    }

    /**
     * Test delete template field with invalid field ID
     */
    public function testDelFieldWithInvalidFieldId()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->templateLogic->delField(99999);  // Non-existent field ID
    }

    /**
     * Insert test data for testing
     *
     * @return array
     */
    private function insertTestData()
    {
        // Create a business
        $businessId = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com/notify',
            'create_time' => time(),
        ]);

        // Create a template
        $templateName = 'Test Template';
        $templateId = BusinessTemplateModel::query()->insertGetId([
            'business_id' => $businessId,
            'template_name' => $templateName,
            'biz_type' => 'test_type',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time(),
        ]);

        // Create a field
        $fieldId = BusinessFieldModel::query()->insertGetId([
            'name' => 'Test Field',
            'field_key' => 'test_key',
            'field_type' => 'text',
            'placeholder' => 'Test Placeholder',
            'min_length' => 0,
            'max_length' => 100,
            'is_required' => 1,
            'biz_type' => 'test_type',
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time(),
        ]);

        // Create field options
        BusinessFieldOptionsModel::query()->insert([
            'field_id' => $fieldId,
            'option_name' => 'Option 1',
            'option_img' => '',
            'create_time' => time(),
        ]);

        return [
            'businessId' => $businessId,
            'templateId' => $templateId,
            'templateName' => $templateName,
            'fieldId' => $fieldId,
        ];
    }
}
