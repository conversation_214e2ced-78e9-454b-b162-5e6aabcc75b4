<?php

namespace Tests\Feature\Logic\Inner;

use App\Constants\AppraiserConst;
use App\Constants\BusinessConst;
use App\Constants\OrderConst;
use App\Constants\TemplateConst;
use App\ErrCode\AppraiserErr;
use App\ErrCode\BaseErr;
use App\ErrCode\FieldErr;
use App\ErrCode\OrderErr;
use App\ErrCode\TemplateErr;
use App\Exceptions\ErrException;
use App\Logic\Inner\OrderLogic;
use App\Models\BusinessFieldModel;
use App\Models\BusinessTemplateFieldModel;
use App\Models\OrderItemFieldModel;
use App\Models\OrderItemModel;
use App\Models\OrderModel;
use App\Params\Order\OrderParams;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;

class OrderLogicAugmentTest extends TestCase
{
    use DatabaseTransactions;

    protected $orderLogic;

    protected function setUp(): void
    {
        parent::setUp();
        $this->orderLogic = OrderLogic::getInstance();
    }

    /**
     * Test create order successfully
     */
    public function testCreateSuccess()
    {
        // Insert test data
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 1001;
        $params->businessNo = 'BN001';
        $params->businessMasterNo = 'BMN001';
        $params->orderAmount = 100;
        $params->subType = 1;
        $params->items = [
            [
                'imgs' => ['img1.jpg', 'img2.jpg'],
                'video' => [['videoUrl' => 'video1.mp4']],
                'remark' => 'test remark',
                'testField' => 'test value'
            ]
        ];
        $params->order = ['orderField' => 'order value'];

        $result = $this->orderLogic->create($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertArrayHasKey('appraiserId', $result);
        $this->assertArrayHasKey('appraiserName', $result);
        $this->assertEquals(1001, $result['appraiserId']);

        // Verify order was created in database
        $order = OrderModel::query()->where('uri', $result['uri'])->first();
        $this->assertNotNull($order);
        $this->assertEquals($testData['businessId'], $order->business_id);
        $this->assertEquals('test_category', $order->category_identifier);
    }

    /**
     * Test create order with missing business ID
     */
    public function testCreateMissingBusinessId()
    {
        $params = new OrderParams();
        $params->categoryIdentifier = 'test_category';
        $params->items = [['imgs' => ['img1.jpg']]];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->orderLogic->create($params);
    }

    /**
     * Test create order with missing category identifier
     */
    public function testCreateMissingCategoryIdentifier()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->items = [['imgs' => ['img1.jpg']]];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::CATEGORY_NOT_CONFIGURED_CORRECTLY[0]);
        $this->expectExceptionMessage('类目未传递或者未找到相关配置');

        $this->orderLogic->create($params);
    }

    /**
     * Test create order with missing items
     */
    public function testCreateMissingItems()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BaseErr::PARAMETER_ERROR[0]);
        $this->expectExceptionMessage('参数错误');

        $this->orderLogic->create($params);
    }

    /**
     * Test create order with invalid item images
     */
    public function testCreateInvalidItemImages()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 1001; // Set appraiser to avoid dispatch error
        $params->items = [['imgs' => 'invalid_not_array']];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_ITEM_IMG_ERR[0]);
        $this->expectExceptionMessage('订单物品图片必须且为数组');

        $this->orderLogic->create($params);
    }

    /**
     * Test create order with invalid item video
     */
    public function testCreateInvalidItemVideo()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 1001; // Set appraiser to avoid dispatch error
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'video' => 'invalid_not_array'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_ITEM_VIDEO_ERR[0]);
        $this->expectExceptionMessage('订单物品视频必须且为数组');

        $this->orderLogic->create($params);
    }

    /**
     * Test create order with remark too long
     */
    public function testCreateRemarkTooLong()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 1001; // Set appraiser to avoid dispatch error
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'remark' => str_repeat('a', 501) // 501 characters
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_SUBMIT_PARAMS_ERR[0]);
        $this->expectExceptionMessage('remark不能超过500字符');

        $this->orderLogic->create($params);
    }

    /**
     * Test getParams successfully
     */
    public function testGetParamsSuccess()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        $result = $this->orderLogic->getParams($order->uri);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('inputParams', $result);
    }

    /**
     * Test getParams with non-existent order
     */
    public function testGetParamsOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');

        $this->orderLogic->getParams('non_existent_uri');
    }

    /**
     * Test detail successfully
     */
    public function testDetailSuccess()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        $result = $this->orderLogic->detail($order->uri);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertArrayHasKey('businessId', $result);
        $this->assertArrayHasKey('state', $result);
        $this->assertEquals($order->uri, $result['uri']);
        $this->assertEquals($order->business_id, $result['businessId']);
    }

    /**
     * Test detail with non-existent order
     */
    public function testDetailOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');

        $this->orderLogic->detail('non_existent_uri');
    }

    /**
     * Test simpleDetail successfully
     */
    public function testSimpleDetailSuccess()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        $result = $this->orderLogic->simpleDetail($order->uri);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertArrayHasKey('output', $result);
        $this->assertArrayNotHasKey('input', $result);
    }

    /**
     * Test batchSimpleDetail successfully
     */
    public function testBatchSimpleDetailSuccess()
    {
        $orderId1 = $this->insertOrderData();
        $orderId2 = $this->insertOrderData();
        $order1 = OrderModel::query()->find($orderId1);
        $order2 = OrderModel::query()->find($orderId2);

        $result = $this->orderLogic->batchSimpleDetail([$order1->uri, $order2->uri]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertCount(2, $result['list']);
    }

    /**
     * Test batchSimpleDetail with invalid URIs
     */
    public function testBatchSimpleDetailWithInvalidUris()
    {
        $result = $this->orderLogic->batchSimpleDetail(['invalid_uri1', 'invalid_uri2']);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertEmpty($result['list']);
    }

    /**
     * Insert test data for order creation
     */
    private function insertTestData()
    {
        // Insert business first
        $businessId = \App\Models\BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://test.com',
            'state' => 1,
            'create_time' => time()
        ]);

        // Insert business templates
        $inputTemplateId = \App\Models\BusinessTemplateModel::query()->insertGetId([
            'business_id' => $businessId,
            'template_name' => 'Input Template',
            'biz_type' => 'input',
            'state' => 1,
            'is_deleted' => 0,
            'create_time' => time()
        ]);

        $outputTemplateId = \App\Models\BusinessTemplateModel::query()->insertGetId([
            'business_id' => $businessId,
            'template_name' => 'Output Template',
            'biz_type' => 'output',
            'state' => 1,
            'is_deleted' => 0,
            'create_time' => time()
        ]);

        // Insert business category
        $categoryId = \App\Models\BusinessCategoryModel::query()->insertGetId([
            'business_id' => $businessId,
            'category_name' => 'Test Category',
            'category_identifier' => 'test_category',
            'input_template_id' => $inputTemplateId,
            'output_template_id' => $outputTemplateId,
            'state' => 1,
            'create_time' => time()
        ]);

        // Insert business field
        $fieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => $businessId,
            'name' => 'Test Field',
            'field_type' => 'input',
            'field_key' => 'testField',
            'biz_type' => 'input',
            'is_required' => 0,
            'state' => 1,
            'is_deleted' => 0,
            'create_time' => time()
        ]);

        // Insert template field
        BusinessTemplateFieldModel::query()->insert([
            'template_id' => $inputTemplateId,
            'field_id' => $fieldId,
            'field_key' => 'testField',
            'output_type' => OrderConst::OUTPUT_TYPE_ORDER_ITEM,
            'state' => 1,
            'is_deleted' => 0,
            'create_time' => time()
        ]);

        // Insert appraiser
        \App\Models\AppraiserModel::query()->insert([
            'userinfo_id' => 1001,
            'nickname' => 'Test Appraiser',
            'avatar' => 'avatar.jpg',
            'work_type' => 1,
            'state' => 1,
            'create_time' => time()
        ]);

        return [
            'businessId' => $businessId,
            'categoryId' => $categoryId,
            'inputTemplateId' => $inputTemplateId,
            'outputTemplateId' => $outputTemplateId,
            'fieldId' => $fieldId
        ];
    }

    /**
     * Test assignAppraiser successfully
     */
    public function testAssignAppraiserSuccess()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        // Update order state to allow assignment
        OrderModel::query()->where('id', $orderId)->update([
            'state' => OrderConst::ORDER_STATE_WAIT_DISTRIBUTION
        ]);

        $result = $this->orderLogic->assignAppraiser($order->uri, 1001);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('state', $result);
        $this->assertEquals(1, $result['state']);

        // Verify order was updated
        $updatedOrder = OrderModel::query()->find($orderId);
        $this->assertEquals(1001, $updatedOrder->userinfo_id);
        $this->assertEquals(OrderConst::ORDER_STATE_WAIT_IDENTIFY, $updatedOrder->state);
    }

    /**
     * Test assignAppraiser with non-existent order
     */
    public function testAssignAppraiserOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');

        $this->orderLogic->assignAppraiser('non_existent_uri', 1001);
    }

    /**
     * Test assignAppraiser with invalid order state
     */
    public function testAssignAppraiserInvalidState()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        // Set order to completed state
        OrderModel::query()->where('id', $orderId)->update([
            'state' => OrderConst::ORDER_STATE_COMPLETE
        ]);

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_STATUS_CANNOT_ASSIGN_OR_MODIFY[0]);
        $this->expectExceptionMessage('订单状态不允许指派或修改鉴定师');

        $this->orderLogic->assignAppraiser($order->uri, 1001);
    }

    /**
     * Test assignAppraiser with non-existent appraiser
     */
    public function testAssignAppraiserNotExist()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        // Update order state to allow assignment
        OrderModel::query()->where('id', $orderId)->update([
            'state' => OrderConst::ORDER_STATE_WAIT_DISTRIBUTION
        ]);

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(AppraiserErr::APPRAISER_NO_EXIST[0]);
        $this->expectExceptionMessage('当前鉴定师不存在');

        $this->orderLogic->assignAppraiser($order->uri, 9999);
    }

    /**
     * Test cancel order successfully
     */
    public function testCancelSuccess()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        $result = $this->orderLogic->cancel($order->uri);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify order was cancelled
        $updatedOrder = OrderModel::query()->find($orderId);
        $this->assertEquals(OrderConst::ORDER_STATE_CANCEL, $updatedOrder->state);
    }

    /**
     * Test cancel with non-existent order
     */
    public function testCancelOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');

        $this->orderLogic->cancel('non_existent_uri');
    }

    /**
     * Test cancel with invalid order state
     */
    public function testCancelInvalidState()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        // Set order to completed state
        OrderModel::query()->where('id', $orderId)->update([
            'state' => OrderConst::ORDER_STATE_COMPLETE
        ]);

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_STATUS_CANNOT_CANCEL[0]);
        $this->expectExceptionMessage('订单状态不能取消');

        $this->orderLogic->cancel($order->uri);
    }

    /**
     * Test reject order successfully
     */
    public function testRejectSuccess()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        // Set order to completed state
        OrderModel::query()->where('id', $orderId)->update([
            'state' => OrderConst::ORDER_STATE_COMPLETE
        ]);

        // Insert order item
        $orderItemId = OrderItemModel::query()->insertGetId([
            'order_id' => $orderId,
            'order_uri' => $order->uri,
            'imgs' => '["img1.jpg"]',
            'video' => '[]',
            'remark' => 'test',
            'create_time' => time()
        ]);

        // Get order details to get the correct business_id and template_id
        $order = OrderModel::query()->find($orderId);

        // Insert reject reason field
        $rejectFieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => $order->business_id,
            'name' => 'Reject Reason',
            'field_type' => 'textarea',
            'field_key' => 'rejectReason',
            'biz_type' => 'input',
            'is_required' => 1,
            'state' => 1,
            'is_deleted' => 0,
            'create_time' => time()
        ]);

        BusinessTemplateFieldModel::query()->insert([
            'template_id' => $order->input_template_id,
            'field_id' => $rejectFieldId,
            'field_key' => 'rejectReason',
            'output_type' => OrderConst::OUTPUT_TYPE_ORDER_ITEM,
            'state' => 1,
            'is_deleted' => 0,
            'create_time' => time()
        ]);

        $result = $this->orderLogic->reject($order->uri, 1001, 'Test reject reason');

        $this->assertIsArray($result);
        $this->assertArrayHasKey('result', $result);

        // Verify order was rejected
        $updatedOrder = OrderModel::query()->find($orderId);
        $this->assertEquals(OrderConst::ORDER_STATE_WAIT_IDENTIFY, $updatedOrder->state);
        $this->assertEquals(1001, $updatedOrder->userinfo_id);
    }

    /**
     * Test reject with non-existent order
     */
    public function testRejectOrderNotExist()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');

        $this->orderLogic->reject('non_existent_uri', 1001, 'reason');
    }

    /**
     * Test reject with invalid order state
     */
    public function testRejectInvalidState()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_STATUS_CANNOT_REJECT[0]);
        $this->expectExceptionMessage('订单状态不能打回');

        $this->orderLogic->reject($order->uri, 1001, 'reason');
    }

    /**
     * Test reject with non-existent appraiser
     */
    public function testRejectAppraiserNotExist()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        // Set order to completed state
        OrderModel::query()->where('id', $orderId)->update([
            'state' => OrderConst::ORDER_STATE_COMPLETE
        ]);

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(AppraiserErr::APPRAISER_NO_EXIST[0]);
        $this->expectExceptionMessage('当前鉴定师不存在');

        $this->orderLogic->reject($order->uri, 9999, 'reason');
    }

    /**
     * Test list orders successfully
     */
    public function testListSuccess()
    {
        $orderId1 = $this->insertOrderData();
        $orderId2 = $this->insertOrderData();

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'userinfoId' => 1001
        ];

        $result = $this->orderLogic->list($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('page', $result);
        $this->assertArrayHasKey('pageSize', $result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('isEnd', $result);
        $this->assertEquals(2, $result['page']);
        $this->assertEquals(10, $result['pageSize']);
        $this->assertCount(2, $result['list']);
    }

    /**
     * Test list with empty result
     */
    public function testListEmpty()
    {
        $params = [
            'page' => 1,
            'pageSize' => 10,
            'userinfoId' => 9999
        ];

        $result = $this->orderLogic->list($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertEmpty($result['list']);
        $this->assertTrue($result['isEnd']);
    }

    /**
     * Test list with business filter
     */
    public function testListWithBusinessFilter()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'businessId' => $order->business_id
        ];

        $result = $this->orderLogic->list($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertGreaterThanOrEqual(1, count($result['list']));

        // Verify that all returned orders have the correct business_id
        foreach ($result['list'] as $item) {
            $this->assertEquals($order->business_id, $item['businessId']);
        }
    }

    /**
     * Test list with state filter
     */
    public function testListWithStateFilter()
    {
        $orderId = $this->insertOrderData();

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY
        ];

        $result = $this->orderLogic->list($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertGreaterThanOrEqual(1, count($result['list']));

        // Verify that all returned orders have the correct state
        foreach ($result['list'] as $item) {
            $this->assertEquals(OrderConst::ORDER_STATE_WAIT_IDENTIFY, $item['state']);
        }
    }

    /**
     * Test list with identTruth filter
     */
    public function testListWithIdentTruthFilter()
    {
        $this->insertOrderData();

        $params = [
            'page' => 1,
            'pageSize' => 10,
            'identTruth' => 1
        ];

        $result = $this->orderLogic->list($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
    }

    /**
     * Test submit order successfully
     */
    public function testSubmitSuccess()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        // Insert order item
        $orderItemId = OrderItemModel::query()->insertGetId([
            'order_id' => $orderId,
            'order_uri' => $order->uri,
            'imgs' => '["img1.jpg"]',
            'video' => '[]',
            'remark' => 'test',
            'create_time' => time()
        ]);

        // Get order details to get the correct business_id and template_id
        $order = OrderModel::query()->find($orderId);

        // Insert output template field
        $outputFieldId = BusinessFieldModel::query()->insertGetId([
            'business_id' => $order->business_id,
            'name' => 'Output Field',
            'field_type' => 'input',
            'field_key' => 'outputField',
            'biz_type' => 'output',
            'is_required' => 0,
            'state' => 1,
            'is_deleted' => 0,
            'create_time' => time()
        ]);

        BusinessTemplateFieldModel::query()->insert([
            'template_id' => $order->output_template_id,
            'field_id' => $outputFieldId,
            'field_key' => 'outputField',
            'output_type' => OrderConst::OUTPUT_TYPE_ORDER_ITEM,
            'state' => 1,
            'is_deleted' => 0,
            'create_time' => time()
        ]);

        $params = [
            'uri' => $order->uri,
            'identResult' => 'test result',
            'identTruth' => 1,
            'items' => [
                ['outputField' => 'test output value']
            ]
        ];

        $result = $this->orderLogic->submit($params);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // Verify order was updated
        $updatedOrder = OrderModel::query()->find($orderId);
        $this->assertEquals(OrderConst::ORDER_STATE_COMPLETE, $updatedOrder->state);
        $this->assertEquals('test result', $updatedOrder->ident_result);
        $this->assertEquals(1, $updatedOrder->ident_truth);
    }

    /**
     * Test submit with non-existent order
     */
    public function testSubmitOrderNotExist()
    {
        $params = [
            'uri' => 'non_existent_uri',
            'identResult' => 'test result'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_DOES_NOT_EXIST[0]);
        $this->expectExceptionMessage('订单不存在');

        $this->orderLogic->submit($params);
    }

    /**
     * Test submit with invalid order state
     */
    public function testSubmitInvalidState()
    {
        $orderId = $this->insertOrderData();
        $order = OrderModel::query()->find($orderId);

        // Set order to completed state
        OrderModel::query()->where('id', $orderId)->update([
            'state' => OrderConst::ORDER_STATE_COMPLETE
        ]);

        $params = [
            'uri' => $order->uri,
            'identResult' => 'test result'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_STATE_ABNORMAL[0]);
        $this->expectExceptionMessage('订单状态异常');

        $this->orderLogic->submit($params);
    }

    /**
     * Test create order with overseas business (businessId = 4)
     */
    public function testCreateOverseasBusiness()
    {
        $testData = $this->insertTestData();

        // Insert overseas business category
        \App\Models\BusinessCategoryModel::query()->insert([
            'business_id' => 4,
            'category_name' => 'Overseas Category',
            'category_identifier' => 'overseas_category',
            'input_template_id' => $testData['inputTemplateId'],
            'output_template_id' => $testData['outputTemplateId'],
            'state' => 1,
            'create_time' => time()
        ]);

        $params = new OrderParams();
        $params->businessId = 4;
        $params->categoryIdentifier = 'overseas_category';
        $params->appraiserId = 1001;
        $params->items = [
            [
                'imgs' => ['img1.jpg', 'img2.jpg'],
                'remark' => 'overseas test'
            ]
        ];

        $result = $this->orderLogic->create($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertEquals(1001, $result['appraiserId']);
    }

    /**
     * Test create order with no appraiser (auto dispatch)
     */
    public function testCreateWithAutoDispatch()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'remark' => 'auto dispatch test'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(AppraiserErr::APPRAISER_NO_DISPATCH[0]);
        $this->expectExceptionMessage('无可派单鉴定师');

        $this->orderLogic->create($params);
    }

    /**
     * Test create order with consign evaluate business
     */
    public function testCreateConsignEvaluate()
    {
        $testData = $this->insertTestData();

        // Insert consign evaluate business category
        \App\Models\BusinessCategoryModel::query()->insert([
            'business_id' => BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE,
            'category_name' => 'Consign Evaluate',
            'category_identifier' => 'consign_evaluate',
            'input_template_id' => $testData['inputTemplateId'],
            'output_template_id' => $testData['outputTemplateId'],
            'state' => 1,
            'create_time' => time()
        ]);

        $params = new OrderParams();
        $params->businessId = BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE;
        $params->categoryIdentifier = 'consign_evaluate';
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'remark' => 'consign evaluate test'
            ]
        ];

        $result = $this->orderLogic->create($params);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('uri', $result);
        $this->assertEquals(100320642, $result['appraiserId']); // Default appraiser for consign evaluate
    }

    /**
     * Test create order with video URL validation
     */
    public function testCreateWithInvalidVideoUrl()
    {
        $testData = $this->insertTestData();

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 1001; // Set appraiser to avoid dispatch error
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'video' => [['videoUrl' => '']], // Empty video URL
                'remark' => 'test'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_SUBMIT_PARAMS_ERR[0]);
        $this->expectExceptionMessage('videoUrl不能为空');

        $this->orderLogic->create($params);
    }

    /**
     * Test create order with images JSON too long
     */
    public function testCreateImgsJsonTooLong()
    {
        $testData = $this->insertTestData();

        $longImgArray = array_fill(0, 100, str_repeat('a', 50)); // Create very long image array

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 1001; // Set appraiser to avoid dispatch error
        $params->items = [
            [
                'imgs' => $longImgArray,
                'remark' => 'test'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_SUBMIT_PARAMS_ERR[0]);
        $this->expectExceptionMessage('imgs不能超过1500字符');

        $this->orderLogic->create($params);
    }

    /**
     * Test create order with video JSON too long
     */
    public function testCreateVideoJsonTooLong()
    {
        $testData = $this->insertTestData();

        $longVideoArray = array_fill(0, 50, ['videoUrl' => str_repeat('a', 50)]); // Create very long video array

        $params = new OrderParams();
        $params->businessId = $testData['businessId'];
        $params->categoryIdentifier = 'test_category';
        $params->appraiserId = 1001; // Set appraiser to avoid dispatch error
        $params->items = [
            [
                'imgs' => ['img1.jpg'],
                'video' => $longVideoArray,
                'remark' => 'test'
            ]
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(OrderErr::ORDER_SUBMIT_PARAMS_ERR[0]);
        $this->expectExceptionMessage('video不能超过800字符');

        $this->orderLogic->create($params);
    }

    /**
     * Insert order data for testing
     */
    private function insertOrderData()
    {
        $testData = $this->insertTestData();

        return OrderModel::query()->insertGetId([
            'uri' => 'test_uri_' . time() . '_' . rand(1000, 9999),
            'userinfo_id' => 1001,
            'business_id' => $testData['businessId'],
            'category_id' => $testData['categoryId'],
            'category_identifier' => 'test_category',
            'input_template_id' => $testData['inputTemplateId'],
            'output_template_id' => $testData['outputTemplateId'],
            'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY,
            'cover' => 'cover.jpg',
            'detail_json' => '{}',
            'create_time' => time(),
            'accept_time' => time()
        ]);
    }
}
