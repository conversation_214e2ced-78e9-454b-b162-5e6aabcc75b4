<?php

namespace Tests\Feature\Logic;

use App\ErrCode\BusinessErr;
use App\Exceptions\ErrException;
use App\Logic\BusinessLogic;
use App\Models\BusinessModel;
use Laravel\Lumen\Testing\DatabaseMigrations;
use <PERSON>vel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;

class BusinessLogicAugmentTest extends TestCase
{
    use DatabaseTransactions;

    protected $businessLogic;

    protected function setUp(): void
    {
        parent::setUp();
        $this->businessLogic = BusinessLogic::getInstance();
    }

    /**
     * Test adding a new business successfully
     */
    public function testAddSuccess()
    {
        $name = 'Test Business';
        $notifyUrl = 'http://example.com/notify';

        $result = $this->businessLogic->add($name, $notifyUrl);
        $this->assertTrue($result);

        $business = BusinessModel::query()->where('name', $name)->first();
        $this->assertNotNull($business);
        $this->assertEquals($name, $business->name);
        $this->assertEquals($notifyUrl, $business->notify_url);
    }

    /**
     * Test adding a business with duplicate name
     */
    public function testAddDuplicateName()
    {
        $name = 'Test Business';
        $notifyUrl = 'http://example.com/notify';

        // First insert
        BusinessModel::query()->insert([
            'name' => $name,
            'notify_url' => $notifyUrl,
            'state' => 1,
            'create_time' => time()
        ]);

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BusinessErr::BUSINESS_EXIST[0]);
        $this->expectExceptionMessage('当前业务名称已经存在');

        $this->businessLogic->add($name, $notifyUrl);
    }

    /**
     * Test listing businesses with pagination
     */
    public function testList()
    {
        // Insert test data
        $businesses = [
            ['name' => 'Business 1', 'notify_url' => 'http://example.com/1', 'state' => 1, 'create_time' => time()],
            ['name' => 'Business 2', 'notify_url' => 'http://example.com/2', 'state' => 1, 'create_time' => time()],
            ['name' => 'Business 3', 'notify_url' => 'http://example.com/3', 'state' => 1, 'create_time' => time()],
        ];
        BusinessModel::query()->insert($businesses);

        $params = [
            'page' => 1,
            'pageSize' => 2
        ];

        $result = $this->businessLogic->list($params);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertCount(2, $result['list']);
        $this->assertGreaterThanOrEqual(3, $result['total']);
    }

    /**
     * Test editing a business successfully
     */
    public function testEditSuccess()
    {
        // Insert test data
        $id = BusinessModel::query()->insertGetId([
            'name' => 'Original Name',
            'notify_url' => 'http://example.com/original',
            'state' => 1,
            'create_time' => time()
        ]);

        $params = [
            'name' => 'Updated Name',
            'notifyUrl' => 'http://example.com/updated'
        ];

        $result = $this->businessLogic->edit($id, $params);
        $this->assertEquals(1, $result);

        $business = BusinessModel::query()->find($id);
        $this->assertEquals('Updated Name', $business->name);
        $this->assertEquals('http://example.com/updated', $business->notify_url);
    }

    /**
     * Test editing non-existent business
     */
    public function testEditNonExistentBusiness()
    {
        $params = [
            'name' => 'Updated Name',
            'notifyUrl' => 'http://example.com/updated'
        ];

        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BusinessErr::BUSINESS_NO_EXIST[0]);
        $this->expectExceptionMessage('当前业务不存在');

        $this->businessLogic->edit(999, $params);
    }

    /**
     * Test enabling a business
     */
    public function testEnableSuccess()
    {
        // Insert test data
        $id = BusinessModel::query()->insertGetId([
            'name' => 'Test Business',
            'notify_url' => 'http://example.com/notify',
            'state' => 0,
            'create_time' => time()
        ]);

        $result = $this->businessLogic->enable($id, 1);
        $this->assertEquals(1, $result);

        $business = BusinessModel::query()->find($id);
        $this->assertEquals(1, $business->state);
    }

    /**
     * Test enabling non-existent business
     */
    public function testEnableNonExistentBusiness()
    {
        $this->expectException(ErrException::class);
        $this->expectExceptionCode(BusinessErr::BUSINESS_NO_EXIST[0]);
        $this->expectExceptionMessage('当前业务不存在');

        $this->businessLogic->enable(999, 1);
    }

    /**
     * Test listing with empty result
     */
    public function testListEmpty()
    {
        $params = [
            'page' => 1,
            'pageSize' => 10
        ];

        $result = $this->businessLogic->list($params);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertIsArray($result['list']);
    }
}
