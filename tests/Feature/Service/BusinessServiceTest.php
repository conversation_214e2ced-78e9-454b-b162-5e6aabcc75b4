<?php

namespace Tests\Feature\Service;

use App\Models\BusinessModel;
use App\Service\BusinessService;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;

class BusinessServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected $businessService;
    protected $businessId;

    protected function setUp(): void
    {
        parent::setUp();
        $this->businessService = BusinessService::getInstance();

        // 创建测试业务
        $model = new BusinessModel();
        $model->name = 'Test Business Service ' . time();
        $model->notify_url = 'https://example.com/notify-service';
        $model->state = 1;
        $model->create_time = time();
        $model->save();
        $this->businessId = $model->id;
    }

    /**
     * 测试添加业务
     */
    public function testAdd_WithValidData_ShouldCreateBusiness()
    {
        // 准备测试数据
        $name = 'New Service Business ' . time(); // 确保名称唯一
        $notifyUrl = 'https://example.com/notify-service-new';

        // 执行测试
        $result = $this->businessService->add($name, $notifyUrl);

        // 验证结果
        $this->assertTrue($result);

        // 验证数据库中是否创建成功
        $business = BusinessModel::query()
            ->where('name', $name)
            ->first();
        $this->assertNotNull($business);
        $this->assertEquals($notifyUrl, $business->notify_url);
    }

    /**
     * 测试根据ID获取业务
     */
    public function testGetOneById_WithExistingId_ShouldReturnBusiness()
    {
        // 执行测试
        $result = $this->businessService->getOneById($this->businessId);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertEquals($this->businessId, $result['id']);
    }

    /**
     * 测试根据ID获取业务 - 业务不存在场景
     */
    public function testGetOneById_WithNonExistingId_ShouldReturnEmptyArray()
    {
        // 准备测试数据
        $nonExistingId = 9999; // 不存在的业务ID

        // 执行测试
        $result = $this->businessService->getOneById($nonExistingId);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * 测试根据名称获取业务
     */
    public function testGetOneByName_WithExistingName_ShouldReturnBusiness()
    {
        // 准备测试数据
        $existingBusiness = BusinessModel::query()->find($this->businessId);
        $name = $existingBusiness->name;

        // 执行测试
        $result = $this->businessService->getOneByName($name);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertEquals($name, $result['name']);
    }

    /**
     * 测试根据名称获取业务 - 业务不存在场景
     */
    public function testGetOneByName_WithNonExistingName_ShouldReturnEmptyArray()
    {
        // 准备测试数据
        $nonExistingName = 'Non Existing Business ' . time();

        // 执行测试
        $result = $this->businessService->getOneByName($nonExistingName);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * 测试更新业务
     */
    public function testUpdateById_WithValidData_ShouldUpdateBusiness()
    {
        // 准备测试数据
        $newName = 'Updated Service Business ' . time();
        $newNotifyUrl = 'https://example.com/notify-service-updated';
        $params = [
            'name' => $newName,
            'notifyUrl' => $newNotifyUrl
        ];

        // 执行测试
        $result = $this->businessService->updateById($this->businessId, $params);

        // 验证结果
        $this->assertGreaterThan(0, $result);

        // 验证数据库中是否更新成功
        $business = BusinessModel::query()->find($this->businessId);
        $this->assertEquals($newName, $business->name);
        $this->assertEquals($newNotifyUrl, $business->notify_url);
    }

    /**
     * 测试更新业务状态
     */
    public function testUpdateById_WithStateChange_ShouldUpdateBusinessState()
    {
        // 准备测试数据
        $params = [
            'state' => 0
        ];

        // 执行测试
        $result = $this->businessService->updateById($this->businessId, $params);

        // 验证结果
        $this->assertGreaterThan(0, $result);

        // 验证数据库中是否更新成功
        $business = BusinessModel::query()->find($this->businessId);
        $this->assertEquals(0, $business->state);
    }

    /**
     * 测试获取业务列表
     */
    public function testList_WithValidParams_ShouldReturnBusinessList()
    {
        // 准备测试数据 - 创建多个业务
        for ($i = 0; $i < 3; $i++) {
            $model = new BusinessModel();
            $model->name = 'List Service Test Business ' . $i . '_' . time();
            $model->notify_url = 'https://example.com/notify-service-' . $i;
            $model->state = 1;
            $model->create_time = time();
            $model->save();
        }

        // 准备参数
        $where = [
            'state' => 1
        ];
        $page = 1;
        $pageSize = 10;

        // 执行测试
        $result = $this->businessService->list($where, $page, $pageSize);

        // 验证结果
        $this->assertIsArray($result);
        $this->assertArrayHasKey('list', $result);
        $this->assertArrayHasKey('total', $result);
        $this->assertArrayHasKey('page', $result);
        $this->assertArrayHasKey('pageSize', $result);
        $this->assertArrayHasKey('isEnd', $result);
        
        // 验证列表数据
        $this->assertNotEmpty($result['list']);
        $this->assertGreaterThanOrEqual(4, count($result['list'])); // 至少有4条记录（1个setUp创建 + 3个测试创建）
    }

    /**
     * 测试获取所有业务
     */
    public function testGetAll_ShouldReturnAllBusinesses()
    {
        // 执行测试
        $result = $this->businessService->getAll();

        // 验证结果
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        
        // 验证返回的字段
        $this->assertArrayHasKey('id', $result[0]);
        $this->assertArrayHasKey('name', $result[0]);
    }
}
