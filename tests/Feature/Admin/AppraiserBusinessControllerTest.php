<?php

namespace Tests\Feature\Admin;

use App\ErrCode\BaseErr;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;


class AppraiserBusinessControllerTest extends TestCase
{
    use DatabaseTransactions;
    
    /**
     * 测试获取鉴定师业务
     */
    public function testQueryAppraiserBusiness()
    {
        // 使用已存在的鉴定师ID进行测试
        // 注：此ID应为系统中确实存在的鉴定师ID
        $existingUserinfoId = 1; // 假设ID为1的鉴定师存在且有关联业务

        // 测试用例1：正常获取鉴定师业务
        $response = $this->call('POST', 'admin/appraiser-business/query-appraiser-business', [
            'userinfoId' => $existingUserinfoId
        ]);
        $content = json_decode($response->getContent(), true);
        // 验证响应状态码
        $this->assertEquals(0, $content['code']);
        $this->assertArrayHasKey('list', $content['data']);
        // 不验证具体数量和内容，只验证结构正确
        $this->assertIsArray($content['data']['list']);

        // 如果返回了业务列表，验证每个业务的结构
        if (!empty($content['data']['list'])) {
            $firstBusiness = $content['data']['list'][0];
            $this->assertArrayHasKey('businessId', $firstBusiness);
            $this->assertArrayHasKey('businessName', $firstBusiness);
        }

        // 测试用例2：获取不存在鉴定师的业务
        $response = $this->call('POST', 'admin/appraiser-business/query-appraiser-business', [
            'userinfoId' => 99999999 // 很大的ID，假设不存在
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(0, $content['code']);
        $this->assertArrayHasKey('list', $content['data']);
        $this->assertEmpty($content['data']['list']);

        // 测试用例3：参数缺失
        $response = $this->call('POST', 'admin/appraiser-business/query-appraiser-business', []);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：无效参数
        $response = $this->call('POST', 'admin/appraiser-business/query-appraiser-business', [
            'userinfoId' => 'invalid'
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);
    }
} 