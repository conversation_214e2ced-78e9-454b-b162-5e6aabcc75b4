<?php

namespace Tests\Feature\Admin;

use App\ErrCode\BaseErr;
use App\Models\BusinessCategoryModel;
use App\Models\BusinessModel;
use Illuminate\Support\Str;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;

class BusinessCategoryControllerTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * 测试添加业务类目
     */
    public function testAddCategory()
    {
        // 准备测试数据：先创建一个业务
        $businessName = '测试业务' . Str::random(5);
        $businessData = [
            'name' => $businessName,
            'notifyUrl' => 'https://example.com/notify/' . Str::random(5)
        ];
        $response = $this->call('POST', 'admin/business/add', $businessData);
        $content = json_decode($response->getContent(), true);
        
        // 断言业务创建成功
        $this->assertEquals(0, $content['code']);
        $this->assertTrue($content['data']);
        
        // 从数据库中查询刚创建的业务
        $business = BusinessModel::query()
            ->where('name', $businessName)
            ->first();
        $this->assertNotNull($business);
        $businessId = $business->id;

        // 准备类目测试数据
        $categoryData = [
            'businessId' => $businessId,
            'categoryName' => '测试类目' . Str::random(5),
            'categoryIdentifier' => 'test_category_' . Str::random(8),
        ];

        // 测试用例1：正常添加类目
        $response = $this->call('POST', 'admin/business-category/add', $categoryData);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应状态码
        $this->assertEquals(0, $content['code']);
        $this->assertTrue($content['data']);
        
        // 验证数据库中是否正确创建
        $category = BusinessCategoryModel::query()
            ->where('business_id', $businessId)
            ->where('category_name', $categoryData['categoryName'])
            ->first();
        
        $this->assertNotNull($category);
        $this->assertEquals($categoryData['categoryName'], $category->category_name);
        $this->assertEquals($categoryData['categoryIdentifier'], $category->category_identifier);
        $this->assertEquals($businessId, $category->business_id);

        // 测试用例2：参数验证 - 业务ID缺失
        $invalidData = $categoryData;
        unset($invalidData['businessId']);
        $response = $this->call('POST', 'admin/business-category/add', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例3：参数验证 - 类目名称缺失
        $invalidData = $categoryData;
        unset($invalidData['categoryName']);
        $response = $this->call('POST', 'admin/business-category/add', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：参数验证 - 类目标识缺失
        $invalidData = $categoryData;
        unset($invalidData['categoryIdentifier']);
        $response = $this->call('POST', 'admin/business-category/add', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例5：参数验证 - 类目名称过长
        $invalidData = array_merge($categoryData, ['categoryName' => Str::random(51)]); // 控制器中限制为max:50
        $response = $this->call('POST', 'admin/business-category/add', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例6：参数验证 - 类目标识过长
        $invalidData = array_merge($categoryData, ['categoryIdentifier' => Str::random(51)]); // 控制器中限制为max:50
        $response = $this->call('POST', 'admin/business-category/add', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例7：添加到不存在的业务
        $invalidData = array_merge($categoryData, ['businessId' => 99999]);
        $response = $this->call('POST', 'admin/business-category/add', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertNotEquals(0, $content['code']); // 应该返回错误
    }

    /**
     * 测试业务下的类目列表接口
     */
    public function testListByBusiness()
    {
        // 准备测试数据：先创建一个业务
        $businessName = '测试业务' . Str::random(5);
        $businessData = [
            'name' => $businessName,
            'notifyUrl' => 'https://example.com/notify/' . Str::random(5)
        ];
        $response = $this->call('POST', 'admin/business/add', $businessData);
        $content = json_decode($response->getContent(), true);
        
        // 断言业务创建成功
        $this->assertEquals(0, $content['code']);
        $this->assertTrue($content['data']);
        
        // 从数据库中查询刚创建的业务
        $business = BusinessModel::query()
            ->where('name', $businessName)
            ->first();
        $this->assertNotNull($business);
        $businessId = $business->id;

        // 创建多个类目
        $categories = [
            [
                'businessId' => $businessId,
                'categoryName' => '测试类目1' . Str::random(3),
                'categoryIdentifier' => 'test_category_1_' . Str::random(5),
            ],
            [
                'businessId' => $businessId,
                'categoryName' => '测试类目2' . Str::random(3),
                'categoryIdentifier' => 'test_category_2_' . Str::random(5),
            ],
        ];

        foreach ($categories as $category) {
            $this->call('POST', 'admin/business-category/add', $category);
        }

        // 测试用例1：获取指定业务下的类目列表
        $response = $this->call('POST', 'admin/business-category/list-by-business', [
            'businessId' => $businessId
        ]);
        $content = json_decode($response->getContent(), true);
        
        $this->assertEquals(0, $content['code']);
        $this->assertArrayHasKey('list', $content['data']);
        $this->assertIsArray($content['data']['list']);
        $this->assertGreaterThanOrEqual(2, count($content['data']['list'])); // 至少包含我们创建的2个类目

        // 验证返回的类目数据
        if (!empty($content['data']['list'])) {
            $firstCategory = $content['data']['list'][0];
            $this->assertArrayHasKey('categoryName', $firstCategory);
            $this->assertArrayHasKey('categoryIdentifier', $firstCategory);
            $this->assertArrayHasKey('id', $firstCategory);
            $this->assertEquals($businessId, $firstCategory['businessId']);
        }

        // 测试用例2：获取不存在业务的类目列表
        $response = $this->call('POST', 'admin/business-category/list-by-business', [
            'businessId' => 99999
        ]);
        $content = json_decode($response->getContent(), true);
        
        // 修正断言：BusinessCategoryLogic.php中，当业务不存在时会抛出BusinessErr::BUSINESS_NO_EXIST异常
        $this->assertNotEquals(0, $content['code']);
        // 不应该有empty断言，因为当业务不存在时会直接抛出异常，不会返回空列表

        // 测试用例3：参数验证 - 业务ID缺失
        $response = $this->call('POST', 'admin/business-category/list-by-business', []);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：参数验证 - 业务ID无效值
        $response = $this->call('POST', 'admin/business-category/list-by-business', [
            'businessId' => 0
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);
    }

    /**
     * 测试所有业务类目列表接口
     */
    public function testListAllCategories()
    {
        // 准备测试数据：先创建两个业务
        $businessData1 = [
            'name' => '测试业务1' . Str::random(5),
            'notifyUrl' => 'https://example.com/notify/biz1/' . Str::random(5)
        ];
        $businessData2 = [
            'name' => '测试业务2' . Str::random(5),
            'notifyUrl' => 'https://example.com/notify/biz2/' . Str::random(5)
        ];
        
        $response1 = $this->call('POST', 'admin/business/add', $businessData1);
        $content1 = json_decode($response1->getContent(), true);
        
        // 断言第一个业务创建成功
        $this->assertEquals(0, $content1['code']);
        $this->assertTrue($content1['data']);
        
        $response2 = $this->call('POST', 'admin/business/add', $businessData2);
        $content2 = json_decode($response2->getContent(), true);
        
        // 断言第二个业务创建成功
        $this->assertEquals(0, $content2['code']);
        $this->assertTrue($content2['data']);
        
        // 从数据库中查询刚创建的业务
        $business1 = BusinessModel::query()
            ->where('name', $businessData1['name'])
            ->first();
        $business2 = BusinessModel::query()
            ->where('name', $businessData2['name'])
            ->first();
        
        $this->assertNotNull($business1);
        $this->assertNotNull($business2);
        
        $business1Id = $business1->id;
        $business2Id = $business2->id;

        // 创建多个类目
        $categories = [
            [
                'businessId' => $business1Id,
                'categoryName' => '业务1类目' . Str::random(3),
                'categoryIdentifier' => 'biz1_category_' . Str::random(5),
            ],
            [
                'businessId' => $business2Id,
                'categoryName' => '业务2类目' . Str::random(3),
                'categoryIdentifier' => 'biz2_category_' . Str::random(5),
            ],
        ];

        foreach ($categories as $category) {
            $this->call('POST', 'admin/business-category/add', $category);
        }

        // 测试用例：获取所有业务的类目列表
        $response = $this->call('POST', 'admin/business-category/list');
        $content = json_decode($response->getContent(), true);
        
        $this->assertEquals(0, $content['code']);
        $this->assertArrayHasKey('list', $content['data']);
        $this->assertIsArray($content['data']['list']);
        $this->assertGreaterThanOrEqual(2, count($content['data']['list'])); // 至少包含我们创建的类目

        // 验证返回的类目数据包含不同业务的类目
        $foundBusiness1 = false;
        $foundBusiness2 = false;
        
        foreach ($content['data']['list'] as $category) {
            if ($category['businessId'] == $business1Id) {
                $foundBusiness1 = true;
            }
            if ($category['businessId'] == $business2Id) {
                $foundBusiness2 = true;
            }
            
            $this->assertArrayHasKey('categoryName', $category);
            $this->assertArrayHasKey('categoryIdentifier', $category);
            $this->assertArrayHasKey('id', $category);
        }
        
        // 验证两个业务的类目都被找到
        $this->assertTrue($foundBusiness1 || $foundBusiness2);
    }

    /**
     * 测试编辑业务类目接口
     */
    public function testEditCategory()
    {
        // 准备测试数据：先创建一个业务
        $businessName = '测试业务' . Str::random(5);
        $businessData = [
            'name' => $businessName,
            'notifyUrl' => 'https://example.com/notify/edit/' . Str::random(5)
        ];
        $response = $this->call('POST', 'admin/business/add', $businessData);
        $content = json_decode($response->getContent(), true);
        
        // 断言业务创建成功
        $this->assertEquals(0, $content['code']);
        $this->assertTrue($content['data']);
        
        // 从数据库中查询刚创建的业务
        $business = BusinessModel::query()
            ->where('name', $businessName)
            ->first();
        $this->assertNotNull($business);
        $businessId = $business->id;

        // 创建测试类目
        $categoryName = '待编辑类目' . Str::random(5);
        $categoryData = [
            'businessId' => $businessId,
            'categoryName' => $categoryName,
            'categoryIdentifier' => 'edit_category_' . Str::random(5),
        ];
        
        $this->call('POST', 'admin/business-category/add', $categoryData);
        
        // 从数据库中查询刚创建的类目
        $category = BusinessCategoryModel::query()
            ->where('business_id', $businessId)
            ->where('category_name', $categoryName)
            ->first();
        
        $this->assertNotNull($category);
        $categoryId = $category->id;

        // 测试用例1：正常编辑类目
        $editData = [
            'id' => $categoryId,
            'categoryName' => '已编辑类目' . Str::random(5),
            'categoryIdentifier' => 'edited_category_' . Str::random(5),
            'state' => 1 // 启用状态
        ];
        
        $response = $this->call('POST', 'admin/business-category/edit', $editData);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应
        $this->assertEquals(0, $content['code']);
        $this->assertTrue($content['data']);
        
        // 验证数据库中的数据是否正确更新
        $updatedCategory = BusinessCategoryModel::query()->find($categoryId);
        
        $this->assertNotNull($updatedCategory);
        $this->assertEquals($editData['categoryName'], $updatedCategory->category_name);
        $this->assertEquals($editData['categoryIdentifier'], $updatedCategory->category_identifier);
        $this->assertEquals($editData['state'], $updatedCategory->state);

        // 测试用例2：编辑不存在的类目
        $nonExistData = array_merge($editData, ['id' => 99999]);
        $response = $this->call('POST', 'admin/business-category/edit', $nonExistData);
        $content = json_decode($response->getContent(), true);
        $this->assertNotEquals(0, $content['code']); // 应该返回错误

        // 测试用例3：必填参数缺失 - id
        $invalidData = $editData;
        unset($invalidData['id']);
        $response = $this->call('POST', 'admin/business-category/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：必填参数缺失 - categoryName
        $invalidData = $editData;
        unset($invalidData['categoryName']);
        $response = $this->call('POST', 'admin/business-category/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例5：必填参数缺失 - categoryIdentifier
        $invalidData = $editData;
        unset($invalidData['categoryIdentifier']);
        $response = $this->call('POST', 'admin/business-category/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例6：参数验证 - 类目名称过长
        $invalidData = array_merge($editData, ['categoryName' => Str::random(51)]); // 控制器中限制为max:50
        $response = $this->call('POST', 'admin/business-category/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例7：参数验证 - 类目标识过长
        $invalidData = array_merge($editData, ['categoryIdentifier' => Str::random(51)]); // 控制器中限制为max:50
        $response = $this->call('POST', 'admin/business-category/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例8：参数验证 - state无效值
        $invalidData = array_merge($editData, ['state' => 2]); // 只能是0或1
        $response = $this->call('POST', 'admin/business-category/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);
    }
} 