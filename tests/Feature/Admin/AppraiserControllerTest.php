<?php

namespace Tests\Feature\Admin;

use App\ErrCode\AppraiserErr;
use App\ErrCode\BaseErr;
use App\Models\AppraiserModel;
use Illuminate\Support\Str;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;


class AppraiserControllerTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();
        // 清理测试数据
        AppraiserModel::query()->where('userinfo_id', '>', 8800000000)->delete();
    }

    /**
     * 测试成功创建鉴定师
     */
    public function testCreateSuccess()
    {
        $data = [
            'userinfoId' => 8800000001,
            'nickname' => '测试鉴定师',
            'avatar' => 'https://example.com/avatar.jpg',
            'workType' => 1,
            'isTrainee' => 2,
            'description' => '资深鉴定师，从业10年',
            'certificate' => 'https://example.com/cert.jpg',
            'signaturePicture' => 'https://example.com/signature.jpg'
        ];

        $response = $this->call('POST', 'admin/appraiser/create', $data);
        $this->assertResponseOk();

        // 获取响应内容
        $content = json_decode($response->getContent(), true);
        // 检查业务状态码为0（成功）
        $this->assertEquals(0, $content['code']);
        $this->assertEquals('ok', $content['msg']);
        $this->assertTrue($content['data']);

        // 验证数据库中是否正确创建
        $appraiser = AppraiserModel::query()
            ->where('userinfo_id', $data['userinfoId'])
            ->first();

        $this->assertNotNull($appraiser);
        $this->assertEquals($data['nickname'], $appraiser->nickname);
        $this->assertEquals($data['avatar'], $appraiser->avatar);
        $this->assertEquals($data['workType'], $appraiser->work_type);
        $this->assertEquals($data['isTrainee'], $appraiser->is_trainee);
        $this->assertEquals($data['description'], $appraiser->description);
        $this->assertEquals($data['certificate'], $appraiser->certificate);
        $this->assertEquals($data['signaturePicture'], $appraiser->signature_picture);
    }

    /**
     * 测试创建重复鉴定师
     */
    public function testCreateDuplicate()
    {
        $data = [
            'userinfoId' => 8800000002,
            'nickname' => '测试鉴定师',
            'avatar' => 'https://example.com/avatar.jpg',
            'workType' => 1,
            'isTrainee' => 2,
            'description' => '资深鉴定师，从业10年',
            'certificate' => 'https://example.com/cert.jpg',
            'signaturePicture' => 'https://example.com/signature.jpg'
        ];

        // 第一次创建
        $this->call('POST', 'admin/appraiser/create', $data);
        $this->assertResponseOk();
        // 第二次创建同一个userinfoId
        $response = $this->call('POST', 'admin/appraiser/create', $data);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(AppraiserErr::APPRAISER_EXIST[0], $content['code']);
        //$this->assertEquals(AppraiserErr::APPRAISER_EXIST[1], $content['msg']);

        $this->assertResponseOk();
    }

    /**
     * 测试参数验证
     */
    public function testValidation()
    {
        $testCases = [
            // 测试userinfoId必填
            [
                'data' => [
                    'nickname' => '测试鉴定师',
                    'avatar' => 'https://example.com/avatar.jpg',
                    'workType' => 1,
                    'isTrainee' => 2,
                    'description' => '资深鉴定师',
                    'signaturePicture' => 'https://example.com/signature.jpg'
                ],
                'field' => 'userinfoId'
            ],
            // 测试workType枚举值
            [
                'data' => [
                    'userinfoId' => 8800000003,
                    'nickname' => '测试鉴定师',
                    'avatar' => 'https://example.com/avatar.jpg',
                    'workType' => 3, // 无效的枚举值
                    'isTrainee' => 2,
                    'description' => '资深鉴定师',
                    'signaturePicture' => 'https://example.com/signature.jpg'
                ],
                'field' => 'workType'
            ],
            // 测试description长度限制
            [
                'data' => [
                    'userinfoId' => 8800000004,
                    'nickname' => '测试鉴定师',
                    'avatar' => 'https://example.com/avatar.jpg',
                    'workType' => 1,
                    'isTrainee' => 2,
                    'description' => str_repeat('a', 201), // 超过200字符
                    'signaturePicture' => 'https://example.com/signature.jpg'
                ],
                'field' => 'description'
            ]
        ];

        foreach ($testCases as $case) {
            $response = $this->call('POST', 'admin/appraiser/create', $case['data']);
            $content = json_decode($response->getContent(), true);
            $this->assertResponseOk();
            $this->assertEquals(100, $content['code']);

            //$this->assertArrayHasKey('errors', $content);
            $this->assertStringContainsString(mb_strtolower(Str::headline($case['field'])), $content['msg']);
        }
    }

    /**
     * 测试鉴定师详情接口
     */
    public function testDetail()
    {
        // 测试用例1：正常获取鉴定师详情
        $userinfoId = 8800000001;
        // 创建测试数据
        AppraiserModel::query()->insert([
            'userinfo_id' => $userinfoId,
            'nickname' => '测试鉴定师',
            'avatar' => 'https://example.com/avatar.jpg',
            'work_type' => 1,
            'is_trainee' => 2,
            'description' => '资深鉴定师',
            'certificate' => 'https://example.com/cert.jpg',
            'signature_picture' => 'https://example.com/signature.jpg',
            'state' => 1,
            //'create_time' => time(),
        ]);

        $response = $this->call('get', 'admin/appraiser/detail', ['userinfoId' => $userinfoId]);
        //dd($response->getContent());
        $content = json_decode($response->getContent(), true);
        // 验证响应状态码
        $this->assertEquals(0, $content['code']);
        // 验证返回的数据结构
        $this->assertArrayHasKey('userinfoId', $content['data']);
        $this->assertArrayHasKey('nickname', $content['data']);
        $this->assertArrayHasKey('avatar', $content['data']);
        $this->assertEquals($userinfoId, $content['data']['userinfoId']);

        // 测试用例2：测试不存在的鉴定师ID
        $response = $this->call('POST', 'admin/appraiser/detail', ['userinfoId' => 99999]);
        $content = json_decode($response->getContent(), true);
        $this->assertEmpty($content['data']);

        // 测试用例3：测试参数缺失
        $response = $this->call('POST', 'admin/appraiser/detail', []);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：测试无效的userinfoId
        $response = $this->call('POST', 'admin/appraiser/detail', ['userinfoId' => 0]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);
    }

    /**
     * 测试鉴定师列表接口
     */
    public function testList()
    {
        // 准备测试数据
        $testData = [
            [
                'userinfoId' => 8800000001,
                'nickname' => '测试鉴定师1',
                'avatar' => 'https://example.com/avatar1.jpg',
                'workType' => 1,
                'isTrainee' => 2,
                'description' => '资深鉴定师1',
                'certificate' => 'https://example.com/cert1.jpg',
                'signaturePicture' => 'https://example.com/signature1.jpg'
            ],
            [
                'userinfoId' => 8800000002,
                'nickname' => '测试鉴定师2',
                'avatar' => 'https://example.com/avatar2.jpg',
                'workType' => 2,
                'isTrainee' => 1,
                'description' => '资深鉴定师2',
                'certificate' => 'https://example.com/cert2.jpg',
                'signaturePicture' => 'https://example.com/signature2.jpg'
            ]
        ];

        // 创建测试数据
        foreach ($testData as $data) {
            $this->call('POST', 'admin/appraiser/create', $data);
        }

        // 测试用例1：获取所有鉴定师列表
        $response = $this->call('POST', 'admin/appraiser/list', ['page' => 1]);
        $content = json_decode($response->getContent(), true);
        
        $this->assertEquals(0, $content['code']);
        $this->assertArrayHasKey('list', $content['data']);
        $this->assertArrayHasKey('total', $content['data']);
        $this->assertGreaterThanOrEqual(2, count($content['data']['list']));

        // 测试用例2：按workType筛选
        $response = $this->call('POST', 'admin/appraiser/list', [
            'page' => 1,
            'workType' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(0, $content['code']);
        foreach ($content['data']['list'] as $item) {
            $this->assertEquals(1, $item['workType']);
        }

        // 测试用例3：按isTrainee筛选
        $response = $this->call('POST', 'admin/appraiser/list', [
            'page' => 1,
            'isTrainee' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(0, $content['code']);
        foreach ($content['data']['list'] as $item) {
            $this->assertEquals(1, $item['isTrainee']);
        }

        // 测试用例4：按userinfoId筛选
        $response = $this->call('POST', 'admin/appraiser/list', [
            'page' => 1,
            'userinfoId' => 8800000001
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(0, $content['code']);
        $this->assertCount(1, $content['data']['list']);
        $this->assertEquals(8800000001, $content['data']['list'][0]['userinfoId']);

        // 测试用例6：分页参数验证
        $response = $this->call('POST', 'admin/appraiser/list', [
            'page' => 0  // 无效的页码
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例7：自定义每页数量
        $response = $this->call('POST', 'admin/appraiser/list', [
            'page' => 1,
            'pageSize' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(0, $content['code']);
        $this->assertCount(1, $content['data']['list']);
    }

    /**
     * 测试鉴定师编辑接口
     */
    public function testEdit()
    {
        // 准备测试数据：先创建一个鉴定师
        $createData = [
            'userinfoId' => 8800000001,
            'nickname' => '测试鉴定师',
            'avatar' => 'https://example.com/avatar.jpg',
            'workType' => 1,
            'isTrainee' => 2,
            'description' => '资深鉴定师',
            'certificate' => 'https://example.com/cert.jpg',
            'signaturePicture' => 'https://example.com/signature.jpg'
        ];
        $this->call('POST', 'admin/appraiser/create', $createData);

        // 测试用例1：正常编辑
        $editData = [
            'userinfoId' => 8800000001,
            'nickname' => '新测试鉴定师',
            'avatar' => 'https://example.com/new-avatar.jpg',
            'workType' => 2,
            'isTrainee' => 1,
            'description' => '资深鉴定师，更新后',
            'certificate' => 'https://example.com/new-cert.jpg',
            'signaturePicture' => 'https://example.com/new-signature.jpg'
        ];
        
        $response = $this->call('POST', 'admin/appraiser/edit', $editData);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应
        $this->assertEquals(0, $content['code']);
        
        // 验证数据库中的数据是否正确更新
        $appraiser = AppraiserModel::query()
            ->where('userinfo_id', $editData['userinfoId'])
            ->first();
        
        $this->assertNotNull($appraiser);
        $this->assertEquals($editData['nickname'], $appraiser->nickname);
        $this->assertEquals($editData['avatar'], $appraiser->avatar);
        $this->assertEquals($editData['workType'], $appraiser->work_type);
        $this->assertEquals($editData['isTrainee'], $appraiser->is_trainee);
        $this->assertEquals($editData['description'], $appraiser->description);
        $this->assertEquals($editData['certificate'], $appraiser->certificate);
        $this->assertEquals($editData['signaturePicture'], $appraiser->signature_picture);

        // 测试用例2：编辑不存在的鉴定师
        $nonExistData = array_merge($editData, ['userinfoId' => 99999]);
        $response = $this->call('POST', 'admin/appraiser/edit', $nonExistData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(AppraiserErr::APPRAISER_NO_EXIST[0], $content['code']);

        // 测试用例3：必填参数缺失
        $invalidData = $editData;
        unset($invalidData['userinfoId']);
        $response = $this->call('POST', 'admin/appraiser/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：参数验证 - 工作类型无效
        $invalidWorkType = array_merge($editData, ['workType' => 3]);
        $response = $this->call('POST', 'admin/appraiser/edit', $invalidWorkType);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例5：参数验证 - 描述超长
        $invalidDescription = array_merge($editData, ['description' => str_repeat('a', 201)]);
        $response = $this->call('POST', 'admin/appraiser/edit', $invalidDescription);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例6：参数验证 - isTrainee无效值
        $invalidTrainee = array_merge($editData, ['isTrainee' => 3]);
        $response = $this->call('POST', 'admin/appraiser/edit', $invalidTrainee);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);
    }

    /**
     * 测试鉴定师启用/禁用接口
     */
    public function testEnable()
    {
        // 准备测试数据：先创建一个鉴定师
        $createData = [
            'userinfoId' => 8800000001,
            'nickname' => '测试鉴定师',
            'avatar' => 'https://example.com/avatar.jpg',
            'workType' => 1,
            'isTrainee' => 2,
            'description' => '资深鉴定师',
            'certificate' => 'https://example.com/cert.jpg',
            'signaturePicture' => 'https://example.com/signature.jpg'
        ];
        $this->call('POST', 'admin/appraiser/create', $createData);

        // 测试用例1：禁用鉴定师
        $response = $this->call('POST', 'admin/appraiser/enable', [
            'userinfoId' => 8800000001,
            'state' => 0
        ]);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应
        $this->assertEquals(0, $content['code']);
        
        // 验证数据库中的状态是否正确更新
        $appraiser = AppraiserModel::query()
            ->where('userinfo_id', 8800000001)
            ->first();
        $this->assertNotNull($appraiser);
        $this->assertEquals(0, $appraiser->state);

        // 测试用例2：启用鉴定师
        $response = $this->call('POST', 'admin/appraiser/enable', [
            'userinfoId' => 8800000001,
            'state' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应
        $this->assertEquals(0, $content['code']);
        
        // 验证数据库中的状态是否正确更新
        $appraiser = AppraiserModel::query()
            ->where('userinfo_id', 8800000001)
            ->first();
        $this->assertNotNull($appraiser);
        $this->assertEquals(1, $appraiser->state);

        // 测试用例3：操作不存在的鉴定师
        $response = $this->call('POST', 'admin/appraiser/enable', [
            'userinfoId' => 99999,
            'state' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        //dd($content);
        $this->assertEquals(AppraiserErr::APPRAISER_NO_EXIST[0], $content['code']);

        // 测试用例4：必填参数缺失 - userinfoId
        $response = $this->call('POST', 'admin/appraiser/enable', [
            'state' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例5：必填参数缺失 - state
        $response = $this->call('POST', 'admin/appraiser/enable', [
            'userinfoId' => 8800000001
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例6：参数验证 - state无效值
        $response = $this->call('POST', 'admin/appraiser/enable', [
            'userinfoId' => 8800000001,
            'state' => 2  // 只能是0或1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例7：参数验证 - userinfoId无效值
        $response = $this->call('POST', 'admin/appraiser/enable', [
            'userinfoId' => 0,
            'state' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);
    }

    /**
     * 测试鉴定师绑定员工接口
     */
    public function testBindAffiliation()
    {
        // 准备测试数据：先创建一个鉴定师
        $createData = [
            'userinfoId' => 8800000001,
            'nickname' => '测试鉴定师',
            'avatar' => 'https://example.com/avatar.jpg',
            'workType' => 1,
            'isTrainee' => 2,
            'description' => '资深鉴定师',
            'certificate' => 'https://example.com/cert.jpg',
            'signaturePicture' => 'https://example.com/signature.jpg'
        ];
        $this->call('POST', 'admin/appraiser/create', $createData);

        // 测试用例1：正常绑定员工
        $bindData = [
            'userinfoId' => 8800000001,
            'affiliationId' => 'WPT8800000099', // 员工ID - 字符串类型
            'affiliationName' => '测试员工', // 员工姓名
        ];
        
        $response = $this->call('POST', 'admin/appraiser/bind-affiliation', $bindData);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应
        $this->assertEquals(0, $content['code']);
        
        // 验证数据库中的数据是否正确更新
        $appraiser = AppraiserModel::query()
            ->where('userinfo_id', $bindData['userinfoId'])
            ->first();
        
        $this->assertNotNull($appraiser);
        $this->assertEquals($bindData['affiliationId'], $appraiser->affiliation_id);
        $this->assertEquals($bindData['affiliationName'], $appraiser->affiliation_name);

        // 测试用例2：绑定不存在的鉴定师
        $nonExistData = [
            'userinfoId' => 99999,
            'affiliationId' => 'WPT8800000099',
            'affiliationName' => '测试员工'
        ];
        $response = $this->call('POST', 'admin/appraiser/bind-affiliation', $nonExistData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(AppraiserErr::APPRAISER_NO_EXIST[0], $content['code']);

        // 测试用例3：必填参数缺失 - userinfoId
        $response = $this->call('POST', 'admin/appraiser/bind-affiliation', [
            'affiliationId' => 'WPT8800000099',
            'affiliationName' => '测试员工'
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：必填参数缺失 - affiliationId
        $response = $this->call('POST', 'admin/appraiser/bind-affiliation', [
            'userinfoId' => 8800000001,
            'affiliationName' => '测试员工'
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例5：必填参数缺失 - affiliationName
        $response = $this->call('POST', 'admin/appraiser/bind-affiliation', [
            'userinfoId' => 8800000001,
            'affiliationId' => 'WPT8800000099'
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例6：参数验证 - userinfoId无效值
        $response = $this->call('POST', 'admin/appraiser/bind-affiliation', [
            'userinfoId' => 0,
            'affiliationId' => 'WPT8800000099',
            'affiliationName' => '测试员工'
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例7：参数验证 - affiliationId无效值（空字符串）
        $response = $this->call('POST', 'admin/appraiser/bind-affiliation', [
            'userinfoId' => 8800000001,
            'affiliationId' => '',
            'affiliationName' => '测试员工'
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例8：参数验证 - affiliationName为空字符串
        $response = $this->call('POST', 'admin/appraiser/bind-affiliation', [
            'userinfoId' => 8800000001,
            'affiliationId' => 'WPT8800000099',
            'affiliationName' => ''
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例9：重复绑定相同员工
        $response = $this->call('POST', 'admin/appraiser/bind-affiliation', $bindData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(0, $content['code']); // 重复绑定应该返回成功，因为是幂等操作
    }
}