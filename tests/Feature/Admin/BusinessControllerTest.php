<?php

namespace Tests\Feature\Admin;

use App\ErrCode\BaseErr;
use App\Models\BusinessModel;
use Illuminate\Support\Str;
use Laravel\Lumen\Testing\DatabaseTransactions;
use Tests\TestCase;

class BusinessControllerTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * 测试添加业务
     */
    public function testAddBusiness()
    {
        // 准备测试数据
        $data = [
            'name' => '测试业务' . Str::random(5),
            'notifyUrl' => 'https://example.com/notify'
        ];

        // 测试用例1：正常添加业务
        $response = $this->call('POST', 'admin/business/add', $data);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应状态码
        $this->assertEquals(0, $content['code']);
        $this->assertTrue($content['data']);
        
        // 验证数据库中是否正确创建
        $business = BusinessModel::query()
            ->where('name', $data['name'])
            ->first();
        
        $this->assertNotNull($business);
        $this->assertEquals($data['name'], $business->name);
        $this->assertEquals($data['notifyUrl'], $business->notify_url);

        // 测试用例2：参数验证 - 业务名称缺失
        $invalidData = $data;
        unset($invalidData['name']);
        $response = $this->call('POST', 'admin/business/add', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例3：参数验证 - 业务名称过长
        $invalidData = array_merge($data, ['name' => Str::random(41)]); // 控制器中限制为max:40
        $response = $this->call('POST', 'admin/business/add', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：参数验证 - 通知URL过长
        $invalidData = array_merge($data, ['notifyUrl' => 'https://example.com/' . Str::random(256)]); // 控制器中限制为max:255
        $response = $this->call('POST', 'admin/business/add', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);
    }

    /**
     * 测试业务列表接口
     */
    public function testListBusiness()
    {
        // 准备测试数据 - 创建几个业务
        $testBusinesses = [
            [
                'name' => '测试业务1' . Str::random(3),
                'notifyUrl' => 'https://example.com/notify1',
            ],
            [
                'name' => '测试业务2' . Str::random(3),
                'notifyUrl' => 'https://example.com/notify2',
            ],
        ];

        foreach ($testBusinesses as $business) {
            $this->call('POST', 'admin/business/add', $business);
        }

        // 测试用例1：获取所有业务列表
        $response = $this->call('POST', 'admin/business/list', [
            'page' => 1,
            'pageSize' => 10
        ]);
        $content = json_decode($response->getContent(), true);
        
        $this->assertEquals(0, $content['code']);
        $this->assertArrayHasKey('list', $content['data']);
        $this->assertArrayHasKey('total', $content['data']);
        $this->assertIsArray($content['data']['list']);
        $this->assertGreaterThanOrEqual(2, $content['data']['total']); // 至少包含我们创建的2个业务

        // 测试用例2：按状态筛选 - 获取启用的业务
        $response = $this->call('POST', 'admin/business/list', [
            'page' => 1,
            'pageSize' => 10,
            'state' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(0, $content['code']);
        if (!empty($content['data']['list'])) {
            foreach ($content['data']['list'] as $item) {
                $this->assertEquals(1, $item['state']);
            }
        }

        // 测试用例3：分页参数验证
        $response = $this->call('POST', 'admin/business/list', [
            'page' => 0, // 无效的页码
            'pageSize' => 10
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：自定义每页数量
        $response = $this->call('POST', 'admin/business/list', [
            'page' => 1,
            'pageSize' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(0, $content['code']);
        $this->assertLessThanOrEqual(1, count($content['data']['list']));
    }

    /**
     * 测试编辑业务接口
     */
    public function testEditBusiness()
    {
        // 准备测试数据：先创建一个业务
        $businessName = '待编辑业务' . Str::random(5);
        $createData = [
            'name' => $businessName,
            'notifyUrl' => 'https://example.com/old-notify'
        ];
        $this->call('POST', 'admin/business/add', $createData);
        
        // 从数据库中查询刚创建的业务
        $business = BusinessModel::query()
            ->where('name', $businessName)
            ->first();
        $this->assertNotNull($business);
        $businessId = $business->id;

        // 测试用例1：正常编辑业务
        $editData = [
            'id' => $businessId,
            'name' => '已编辑业务' . Str::random(5),
            'notifyUrl' => 'https://example.com/new-notify'
        ];
        
        $response = $this->call('POST', 'admin/business/edit', $editData);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应
        $this->assertEquals(0, $content['code']);
        
        // 验证数据库中的数据是否正确更新
        $business = BusinessModel::query()->find($businessId);
        
        $this->assertNotNull($business);
        $this->assertEquals($editData['name'], $business->name);
        $this->assertEquals($editData['notifyUrl'], $business->notify_url);

        // 测试用例2：编辑不存在的业务
        $nonExistData = array_merge($editData, ['id' => 99999]);
        $response = $this->call('POST', 'admin/business/edit', $nonExistData);
        $content = json_decode($response->getContent(), true);
        $this->assertNotEquals(0, $content['code']); // 应该返回错误

        // 测试用例3：必填参数缺失 - id
        $invalidData = $editData;
        unset($invalidData['id']);
        $response = $this->call('POST', 'admin/business/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例4：必填参数缺失 - name
        $invalidData = $editData;
        unset($invalidData['name']);
        $response = $this->call('POST', 'admin/business/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例5：参数验证 - 业务名称过长
        $invalidData = array_merge($editData, ['name' => Str::random(41)]); // 控制器中限制为max:40
        $response = $this->call('POST', 'admin/business/edit', $invalidData);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);
    }

    /**
     * 测试业务启用/禁用接口
     */
    public function testEnableBusiness()
    {
        // 准备测试数据：先创建一个业务
        $businessName = '测试状态业务' . Str::random(5);
        $createData = [
            'name' => $businessName,
            'notifyUrl' => 'https://example.com/notify'
        ];
        $this->call('POST', 'admin/business/add', $createData);
        
        // 从数据库中查询刚创建的业务
        $business = BusinessModel::query()
            ->where('name', $businessName)
            ->first();
        $this->assertNotNull($business);
        $businessId = $business->id;

        // 测试用例1：禁用业务
        $response = $this->call('POST', 'admin/business/enable', [
            'id' => $businessId,
            'state' => 0
        ]);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应
        $this->assertEquals(0, $content['code']);
        
        // 验证数据库中的状态是否正确更新
        $business = BusinessModel::query()->find($businessId);
        $this->assertNotNull($business);
        $this->assertEquals(0, $business->state);

        // 测试用例2：启用业务
        $response = $this->call('POST', 'admin/business/enable', [
            'id' => $businessId,
            'state' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        
        // 验证响应
        $this->assertEquals(0, $content['code']);
        
        // 验证数据库中的状态是否正确更新
        $business = BusinessModel::query()->find($businessId);
        $this->assertNotNull($business);
        $this->assertEquals(1, $business->state);

        // 测试用例3：操作不存在的业务
        $response = $this->call('POST', 'admin/business/enable', [
            'id' => 99999,
            'state' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertNotEquals(0, $content['code']); // 应该返回错误

        // 测试用例4：必填参数缺失 - id
        $response = $this->call('POST', 'admin/business/enable', [
            'state' => 1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例5：必填参数缺失 - state
        $response = $this->call('POST', 'admin/business/enable', [
            'id' => $businessId
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);

        // 测试用例6：参数验证 - state无效值
        $response = $this->call('POST', 'admin/business/enable', [
            'id' => $businessId,
            'state' => 2  // 只能是0或1
        ]);
        $content = json_decode($response->getContent(), true);
        $this->assertEquals(BaseErr::PARAMETER_ERROR[0], $content['code']);
    }
} 