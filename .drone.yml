---
kind: pipeline
name: automation
type: docker

steps:
  - name: gen-swagger
    pull: always
    image: reg.weipaitang.com/micro/automation_gen_swagger:1.0
    commands:
      - mkdir ./swagger
      - /usr/src/automation/scripts/gen_swagger.sh proto/ ./swagger
  - name: gen-php-sdk
    pull: always
    image: reg.weipaitang.com/micro/automation:1.0
    commands:
      - /usr/src/automation/scripts/gen_php_sdk.sh imageident ./swagger
      - /usr/src/automation/scripts/git_push.sh imageident gitlab.weipaitang.com/php-sdk/image-ident-server-sdk-auto $DRONE_BRANCH $DRONE_COMMIT_MESSAGE
    when:
      branch:
        - master
        - main
        - gray
        - feat/*
        - feature/*
  - name: upload_swagger
    pull: always
    image: reg.weipaitang.com/micro/automation:1.0
    commands:
      - /usr/src/automation/scripts/swagger_upload.sh swagger imageident $DRONE_BRANCH
    when:
      branch:
        - main
        - master
        - gray
        - test

trigger:
  branch:
    - master
    - main
    - gray
    - test
    - feat/*
    - feature/*
  event:
    - push
