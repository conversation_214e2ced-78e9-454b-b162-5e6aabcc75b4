<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <coverage>
        <include>
            <directory suffix=".php">app/Logic</directory>
        </include>
        <exclude>
            <directory suffix=".php">tests/</directory>
        </exclude>
    </coverage>
    <testsuites>
        <testsuite name="Application Test Suite">
            <directory suffix="Test.php">./tests</directory>
        </testsuite>
    </testsuites>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="DB_CONNECTION" value="image_ident_server"/>
        <env name="IMAGE_IDENT_SERVER_HOST" value="*********"/>
        <env name="IMAGE_IDENT_SERVER_PORT" value="3306"/>
        <env name="IMAGE_IDENT_SERVER_DATABASE" value="image_ident_server"/>
        <env name="IMAGE_IDENT_SERVER_USERNAME" value="wpt_test"/>
        <env name="IMAGE_IDENT_SERVER_PASSWORD" value="hWhG4tjxFV*49ROY"/>

        <!-- community 数据库配置 -->
        <env name="WPT_COMMUNITY_MASTER_DB_HOST" value="127.0.0.1"/>
        <env name="WPT_COMMUNITY_MASTER_DB_PORT" value="3306"/>
        <env name="WPT_COMMUNITY_MASTER_DB_NAME" value="community"/>
        <env name="WPT_COMMUNITY_MASTER_DB_USERNAME" value="root"/>
        <env name="WPT_COMMUNITY_MASTER_DB_PASSWORD" value="123456"/>

        <!-- jzg 数据库配置 -->
        <env name="WPT_JZG_MASTER_DB_HOST" value="127.0.0.1"/>
        <env name="WPT_JZG_MASTER_DB_PORT" value="3306"/>
        <env name="WPT_JZG_MASTER_DB_NAME" value="jzg"/>
        <env name="WPT_JZG_MASTER_DB_USERNAME" value="root"/>
        <env name="WPT_JZG_MASTER_DB_PASSWORD" value="123456"/>
    </php>
</phpunit>
