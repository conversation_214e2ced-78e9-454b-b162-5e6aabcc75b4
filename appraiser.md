# 鉴定师接口文档

## 目录
- [创建鉴定师](#创建鉴定师)
- [鉴定师详情](#鉴定师详情)
- [编辑鉴定师](#编辑鉴定师)
- [启用/禁用鉴定师](#启用禁用鉴定师)
- [鉴定师列表](#鉴定师列表)

## 创建鉴定师

### 接口信息
- **接口URL**: `/admin/appraiser/create`
- **请求方式**: ANY (支持GET/POST)
- **接口描述**: 创建新的鉴定师信息

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| userinfoId | integer | 是 | 用户ID | 10001 |
| nickname | string | 是 | 昵称 | 最大长度40字符 |
| avatar | string | 是 | 头像URL | https://example.com/avatar.jpg |
| workType | integer | 是 | 工作类型 | 1:全职, 2:兼职 |
| isLeader | integer | 是 | 是否为组长 | 1:是, 2:否 |
| isTrainee | integer | 是 | 是否为实习生 | 1:是, 2:否 |
| description | string | 是 | 个人简介 | 最大长度200字符 |
| certificate | string | 否 | 资质证书URL | https://example.com/cert.jpg |
| signaturePicture | string | 是 | 签名图片URL | https://example.com/signature.jpg |

### 响应参数

```json
{
    "code": 0,
    "msg": "ok",
    "data": true
}
```

### 错误码

| 错误码 | 说明 |
|--------|------|
| 80000 | 鉴定师已存在 |
| 100 | 参数错误 |

## 鉴定师详情

### 接口信息
- **接口URL**: `/admin/appraiser/detail`
- **请求方式**: ANY (支持GET/POST)
- **接口描述**: 获取鉴定师详细信息

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| userinfoId | integer | 是 | 用户ID | 10001 |

### 响应参数

```json
{
    "code": 0,
    "msg": "ok",
    "data": {
        "userinfoId": 10001,
        "nickname": "测试鉴定师",
        "avatar": "https://example.com/avatar.jpg",
        "workType": 1,
        "isLeader": 2,
        "isTrainee": 2,
        "description": "资深鉴定师",
        "certificate": "https://example.com/cert.jpg",
        "signaturePicture": "https://example.com/signature.jpg",
        "state": 1
    }
}
```

### 错误码

| 错误码 | 说明 |
|--------|------|
| 100 | 参数错误 |

## 编辑鉴定师

### 接口信息
- **接口URL**: `/admin/appraiser/edit`
- **请求方式**: ANY (支持GET/POST)
- **接口描述**: 编辑鉴定师信息

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| userinfoId | integer | 是 | 用户ID | 10001 |
| nickname | string | 是 | 昵称 | 最大长度40字符 |
| avatar | string | 是 | 头像URL | https://example.com/avatar.jpg |
| workType | integer | 是 | 工作类型 | 1:全职, 2:兼职 |
| isLeader | integer | 是 | 是否为组长 | 1:是, 2:否 |
| isTrainee | integer | 是 | 是否为实习生 | 1:是, 2:否 |
| description | string | 是 | 个人简介 | 最大长度200字符 |
| certificate | string | 否 | 资质证书URL | https://example.com/cert.jpg |
| signaturePicture | string | 是 | 签名图片URL | https://example.com/signature.jpg |

### 响应参数

```json
{
    "code": 0,
    "msg": "ok",
    "data": true
}
```

### 错误码

| 错误码 | 说明 |
|--------|------|
| 80001 | 鉴定师不存在 |
| 100 | 参数错误 |

## 启用禁用鉴定师

### 接口信息
- **接口URL**: `/admin/appraiser/enable`
- **请求方式**: ANY (支持GET/POST)
- **接口描述**: 启用或禁用鉴定师

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| userinfoId | integer | 是 | 用户ID | 10001 |
| state | integer | 是 | 状态 | 1:启用, 0:禁用 |

### 响应参数

```json
{
    "code": 0,
    "msg": "ok",
    "data": true
}
```

### 错误码

| 错误码 | 说明 |
|--------|------|
| 80001 | 鉴定师不存在 |
| 100 | 参数错误 |

## 鉴定师列表

### 接口信息
- **接口URL**: `/admin/appraiser/list`
- **请求方式**: ANY (支持GET/POST)
- **接口描述**: 获取鉴定师列表

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| page | integer | 是 | 页码 | 1 |
| pageSize | integer | 否 | 每页数量 | 10 |
| workType | integer | 否 | 工作类型筛选 | 1:全职, 2:兼职 |
| isLeader | integer | 否 | 组长筛选 | 1:是, 2:否 |
| isTrainee | integer | 否 | 实习生筛选 | 1:是, 2:否 |
| userinfoId | integer | 否 | 用户ID筛选 | 10001 |

### 响应参数

```json
{
    "code": 0,
    "msg": "ok",
    "data": {
        "list": [
            {
                "userinfoId": 10001,
                "nickname": "测试鉴定师1",
                "avatar": "https://example.com/avatar1.jpg",
                "workType": 1,
                "isLeader": 1,
                "isTrainee": 2,
                "description": "资深鉴定师1",
                "certificate": "https://example.com/cert1.jpg",
                "signaturePicture": "https://example.com/signature1.jpg",
                "state": 1
            }
        ],
        "total": 1
    }
}
```

### 错误码

| 错误码 | 说明 |
|--------|------|
| 100 | 参数错误 | 