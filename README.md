# Spin Project Skeleton

# 安装使用
```shell
composer create-project --prefer-dist spin/spin:^3.0 项目名称(填写自己的名称) -vvv --repository https://packagist.wpt.la
```

# env配置
```shell
无需多余设置, 默认支持 env.php 和 .env 两种配置文件, 优先级 env.php 高于 .env 文件
```

# 路线图
v1.0 基于lumen7改造,对于命名空间,实现逻辑等做了调整,代码侵入性大,三方(lumen生态)兼容性差 请勿使用

v2.0 基于lumen8, 完全使用lumen8为框架驱动, spin作为中间桥接功能实现, 解决所有兼容性问题, spin作为业务代码和lumen之间桥梁存在, 通用性底层代码或者功能增加可以在spin中开发, 然后基于composer 发布安装方式, 所有业务使用方无感知 升级框架

v3.0 基于v2.0 为了避免 大版本更新导致功能异常, 所以升级为3.0 新项目可用

v3.1 [规划中] 为了更好的在开发使用spin框架, 此版本将实现无PHP环境运行此项目(仅可用于本地开发), 新电脑无需安装PHP直接运行此项目, 目前x86下测试稳定运行,arm看情况兼容

# 兼容性
> 100% 兼容 lumen 8.0 鉴于PHP版本(php7.4) 最高能支持lumen8 从lumen9开始 最小版本依赖 >= php8.0

# 调整内容
```
# 注册所有方法路由
$router->any($uri, $action);

# 跨域中间件 根据实际情况修改
app/Http/Middleware/CorsMiddleware.php

# php env
env.php支持
参考 bootstrap/app.php

# 新增.php_cs
> php-cs-fixer fix 

```


# 目录结构
```shell
├── app
    ├── Console         // 命令
    ├── Ecode           // 错误码管理
        ├── Events      // 里面有个sql监听 未开启 根据需要开启
    ├── Exceptions      // 异常
    ├── Http
        ├── Controllers // 控制器
        └── Middleware  // 中间件
    ├── Listeners       // 监听器
    ├── Models          // 模型
    └── Logic           // 业务逻辑
    └── Providers       // 服务提供者
    └── Services        // 封装共性的方法
├── bootstrap           // 启动目录
├── config              // 配置文件
├── database            // 迁移文件
├── public              // 项目入口
├── resources           // 资源目录
├── routes              // 路由
├── storage             // 存储
└── tests               // 单元测试目录
```

# 日志配置
> spin/logger组件不是基于[psr/logger](https://github.com/php-fig/log) 实现的，默认使用 [monolog/monolog](https://github.com/Seldaek/monolog) 作为驱动

```php
<?php

return [
    /**
     * 部门标示：
     * php后端: php
     * 服务支撑: ssd
     * 架构: infra
     * 大数据: bgd
     * 中台: mg
     * 数据应用: da
     * 开放中心: open
     * 推广中心: advert
     * B端: bd
     * 创新部: innovate
     * 请根据你的部门决定
     */
    'log_label' => 'php',

    /**
     * 项目名称
     */
    'log_project' => 'default',

    /**
     * 是否按照日志等级建立目录
     */
    'log_level_dir' => true,

    /**
     * 日志文件目录 必须绝对地址 如：/data/log/service
     * SPIN_LOG_DIR 可以在env.php指定到本地有权限的位置 线上不用配置
     * 'SPIN_LOG_DIR' => __DIR__.'/storage/logs/',
     */
    'log_dir' => env("SPIN_LOG_DIR", '/data/log/service'),

    /**
     * 日志切割规则
     *  day | hour | minute
     */
    'log_rotate_rule' => 'hour',

    /**
     * 日志文件 是否记录 调用文件名称和行 如果不准确 调整 log_trace_stack_ignore 参数
     */
    'log_file_line' => true,

    /**
     * trace stack 忽略掉几层
     * 用于获取准确报错位置 如果你对日志进行了包装 包装几层 就填几
     */
    'log_trace_stack_ignore' => 0,

    /**
     * 日志文件权限
     */
    'log_file_perm' => 0644,
];
```
