<?php

// 业务日志配置文件
return [
    /**
     * 部门标示：
     * php后端: php
     * 服务支撑: ssd
     * 架构: infra
     * 大数据: bgd
     * 中台: mg
     * 数据应用: da
     * 开放中心: open
     * 推广中心: advert
     * B端: bd
     * 创新部: innovate
     * 请根据你的部门决定
     */
    'log_label' => 'php',

    /**
     * 项目名称
     */
    'log_project' => 'image-ident-server',

    /**
     * 是否按照日志等级建立目录
     */
    'log_level_dir' => true,

    /**
     * 日志文件目录 必须绝对地址 如：/data/log/service
     * SPIN_LOG_DIR 可以在env.php指定到本地有权限的位置 线上不用配置
     * 'SPIN_LOG_DIR' => __DIR__.'/storage/logs/',
     */
    'log_dir' => env("SPIN_LOG_DIR", '/data/log/service'),

    /**
     * 日志切割规则
     *  day | hour | minute
     */
    'log_rotate_rule' => 'hour',

    /**
     * 日志文件 是否记录 调用文件名称和行 如果不准确 调整 log_trace_stack_ignore 参数
     */
    'log_file_line' => true,

    /**
     * trace stack 忽略掉几层
     * 用于获取准确报错位置 如果你对日志进行了包装 包装几层 就填几
     */
    'log_trace_stack_ignore' => 0,

    /**
     * 日志文件权限
     */
    'log_file_perm' => 0644,
];
