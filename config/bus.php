<?php

use \WptBus\Transport\RainbowExecutor;
use \WptBus\Transport\HttpExecutor;

$appConfig = [
    "appId" => "app05f5e106",
    "secret" => "iZJqhbVcMpOHDofZqTwxjSzrUJOxFnlX",
];

return [
    'user' => [ // 服务名
        'http' => [ // http配置
            'appId' => $appConfig["appId"],
            'secret' => $appConfig["secret"],
            'readTimeout' => isCli() ? 10000 : 3000,
            'connectTimeout' => isCli() ? 10000 : 3000,
            'executor' => RainbowExecutor::class, // Rainbow 代理 Micro 请求流量转发
            'openException' => true,  // 全部新增此配置，并且设置为true 业务代码请捕获bus异常[重要、重要、重要]
        ],
    ],
    'communityserver' => [
        'http' => [ // http配置
            'appId' => $appConfig["appId"],
            'secret' => $appConfig["secret"],
            'connectTimeout' => isCli() ? 2000 : 200,
            'readTimeout' => 2000,
            'executor' => HttpExecutor::class,
            'openException' => true,
        ]
    ],
    'zhenjin' => [
        'http' => [ // http配置
            'connectTimeout' => 2000,     // 连接超时ms
            'readTimeout' => 2000,        // 读超时ms
        ],
    ],
    'diamond' => [
        'http' => [ // http配置
            'connectTimeout' => 2000,     // 连接超时ms
            'readTimeout' => 2000,        // 读超时ms
        ],
    ],
    'sky' => [ // 服务名
        'http' => [ // http配置
            'appId' => $appConfig["appId"],
            'secret' => $appConfig["secret"],
            'readTimeout' => isCli() ? 10000 : 3000,
            'connectTimeout' => PHP_SAPI == 'cli' ? 10000 : 3000,
            // 'executor' => RainbowExecutor::class, // Rainbow 代理 Micro 请求流量转发
            // 'openException' => true,  // 全部新增此配置，并且设置为true 业务代码请捕获bus异常[重要、重要、重要]
        ],
    ],
];
