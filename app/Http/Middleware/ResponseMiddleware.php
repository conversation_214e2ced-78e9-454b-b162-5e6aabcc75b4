<?php

namespace App\Http\Middleware;

use App\ErrCode\BaseErr;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Spin\Logger\Facades\Log;
use Throwable;

class ResponseMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        /** @var Response $response */
        $response = $next($request);
        $exception = $response->exception;
        $statusCode = config('app.http_status_code') ?: 200;
        $response->setStatusCode($statusCode);

        if ($exception instanceof Throwable) {
            $errCode = $exception->getCode();
            if ($exception instanceof \ErrorException && $errCode == 0) {
                $errCode = BaseErr::SYSTEM_ERROR[0];
            }
            $throwMsg = $exception->getMessage();
            if (!is_prod()) {
                $throwMsg = $exception->getMessage() . ' in ' . $exception->getFile() . ' on ' . $exception->getLine();
            }
            $content = [
                'code'    => $errCode,
                'msg'     => $throwMsg,
                'nowTime' => time(),
                'data'    => [],
            ];
            $msg = $exception->getMessage() . ' in ' . $exception->getFile() . ' on ' . $exception->getLine();
            Log::warning(__FUNCTION__, 'responseMiddlewareThrowMsg', [
                'content' => $content,
                'line'    => $msg,
            ]);

            $response->setContent($content);
            return $response;
        }

        $content = [
            'code' => 0,
            'msg' => 'ok',
            'nowTime' => time(),
            'data' => $response->getOriginalContent(),
        ];
        $response->setContent($content);
        return $response;
    }
}
