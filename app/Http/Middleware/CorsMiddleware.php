<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CorsMiddleware
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle(Request $request, Closure $next)
    {
        $origin = $request->headers->get("Origin");
        if ($request->getMethod() == 'OPTIONS') {
            $headers = [
                'Access-Control-Allow-Origin' => $origin,
                'Access-Control-Allow-Credentials' => "true",
                'Access-Control-Allow-Headers' => "Origin,X-Requested-With,Content-Type,Accept,Authorization",
                'Access-Control-Request-Method' => 'GET,POST,OPTIONS',
                'Access-Control-Max-Age' => 172000,
            ];
            return response('', 200, $headers);
        }
        /** @var Response $response */
        $response = $next($request);
        $response->headers->set('Access-Control-Allow-Origin', $origin);
        $response->headers->set('Access-Control-Allow-Credentials', 'true');
        $response->headers->set(
            'Access-Control-Allow-Headers',
            'Origin,X-Requested-With,Content-Type,Accept,Authorization'
        );
        $response->headers->set('Access-Control-Max-Age', 172000);
        $response->headers->set('Access-Control-Request-Method', 'GET,POST,OPTIONS');

        return $response;
    }
}
