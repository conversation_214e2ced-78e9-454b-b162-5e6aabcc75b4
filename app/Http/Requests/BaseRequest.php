<?php

namespace App\Http\Requests;

use App\ErrCode\BaseErr;
use App\Exceptions\ErrException;
use Illuminate\Http\Request;
use Illuminate\Contracts\Validation\Validator;
use Spin\Logger\Facades\Log;
use <PERSON><PERSON>\Lumen\Routing\ProvidesConvenienceMethods;

class BaseRequest
{
    use ProvidesConvenienceMethods;

    /**
     * 请求实例
     *
     * @var Request
     */
    public Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Throw the failed validation exception.
     *
     * @param Request $request
     * @param Validator $validator
     * @return void
     * @throws ErrException
     */
    protected function throwValidationException(Request $request, $validator)
    {
        Log::info('baseRequest', 'validationError', $request->except(['phone']));
        throw new ErrException(BaseErr::PARAMETER_ERROR, $validator->getMessageBag()->first());
    }
}
