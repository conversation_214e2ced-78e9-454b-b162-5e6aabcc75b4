<?php


namespace App\Http\Controllers;


use App\Http\Requests\BaseRequest;
use Illuminate\Http\Request;

class BaseController extends \Spin\Routing\Controller
{
    protected BaseRequest $baseRequest;

    public int $page = 1;
    public int $pageSize = 20;

    public function __construct(BaseRequest $baseRequest) {
        $this->baseRequest = $baseRequest;

        /**
         * @var Request $request
         */
        $request = app()->make(Request::class);

        // 分页参数
        $this->page = intval($request->input('page', 1));
        $this->page = $this->page > 0 ? $this->page : 1;
        $this->pageSize = intval($request->input('pageSize', $this->pageSize));
    }

}
