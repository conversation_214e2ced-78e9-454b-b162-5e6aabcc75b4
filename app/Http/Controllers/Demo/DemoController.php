<?php


namespace App\Http\Controllers\Demo;


use App\Cache\DemoCache;
use App\Http\Controllers\BaseController;
use App\Service\AsyncService;
use App\Service\TemplateService;
use App\Utils\Vibranium;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class DemoController extends BaseController
{
    public function redis(Request $request)
    {
    }

    public function home(Request $request)
    {
        $init = $request->input('init', '');
        if ($init == 'syncOversea') {
            $taskName = "sync-oversea-order";
            $payload = [
                'php',
                '/data/www/image-ident-server.weipaitang.com/artisan',
                'sync:oversea-order',
            ];
            Vibranium::task('k8s-crond-image-ident-server', $taskName, 0, $payload);
        } elseif ($init == 'syncFwzOrder') {
            $taskName = "sync-fwz-order";
            $payload = [
                'php',
                '/data/www/image-ident-server.weipaitang.com/artisan',
                'sync:fwz-order',
            ];
            Vibranium::task('k8s-crond-image-ident-server', $taskName, 0, $payload);
        } elseif ($init == 'updateOversea') {
            Artisan::call("sync:oversea-order");
        } elseif ($init == 'syncOrder') {
            $uri = $request->input('uri', '');
            $comeFrom = $request->input('comeFrom', 0);
            $process = $request->input('process', 0);
            Artisan::call("sync:old-live-order", ['identUri' => $uri, 'comeFrom' => $comeFrom, 'identProcess' => $process]);
        } elseif ($init == 'fwzTemplate') {
            Artisan::call("init:fwz-template");
        }
    }
}
