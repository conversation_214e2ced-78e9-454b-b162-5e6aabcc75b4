<?php

namespace App\Http\Controllers\Zhenjin;

use App\ErrCode\BaseErr;
use App\ErrCode\OrderErr;
use App\Exceptions\ErrException;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Output\BufferedOutput;

class ZhenJinController extends Controller
{
    public function artisan(Request $request)
    {
        $commandAry = $request->input('params'); // 传入的参数就是 "php /path/to/artisan  command arg-list"
        $maxExecTime = $request->input('timeout', 0);
        $maxExecTime = intval($maxExecTime / 1000);
        if ($maxExecTime > 0) {
            set_time_limit($maxExecTime);
            ini_set("max_execution_time", $maxExecTime);
        }

        // 处理下得到
        if (!empty($commandAry) && false !== strpos($commandAry[0], 'php') && false !== strpos($commandAry[1], 'artisan')) {
            $output = new BufferedOutput();
            $kernel = app()->make(
                'Illuminate\Contracts\Console\Kernel'
            );
            app()->offsetSet("artisan", true);
            $request->server->set("_", $commandAry[0]);
            array_shift($commandAry);
            $request->server->set("argv", $commandAry);
            $request->server->set("argc", count($commandAry));

            $return = $kernel->handle(new ArgvInput($commandAry), $output);
            return ['callReturn' => $return, 'outPut' => $output->fetch()];
        }
        throw new ErrException(BaseErr::PARAMETER_ERROR);
    }
}
