<?php

namespace App\Http\Controllers\Admin;


use App\ErrCode\BaseErr;
use App\Exceptions\ErrException;
use App\Http\Controllers\BaseController;
use App\Logic\AppraiserLogic;
use Illuminate\Http\Request;

class AppraiserController extends BaseController
{
    /**
     * 创建 鉴定师
     *
     * @param Request $request
     * @return null
     * @throws \Illuminate\Validation\ValidationException
     */
    /*public function create(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
                'nickname' => 'required|string|max:40',
                'avatar' => 'required|string|max:200',
                'workType' =>'required|integer|in:1,2',
                //'isLeader' => 'required|integer|in:1,2',
                'isTrainee' => 'required|integer|in:1,2',
                'description' => 'required|string|max:200',
                'certificate' => 'nullable|string|max:200',
                'signaturePicture' => 'required|string|max:200',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        $nickname = $request->input('nickname');
        $avatar = $request->input('avatar');
        $workType = $request->input('workType');
        //$isLeader = $request->input('isLeader');
        $isTrainee = $request->input('isTrainee');
        $description = $request->input('description');  // 简介
        $certificate = $request->input('certificate', '');  // 资质证书
        $signaturePicture = $request->input('signaturePicture');  // 签名

        $params = compact('userinfoId','nickname','avatar','workType','isTrainee','description','certificate','signaturePicture');
        return AppraiserLogic::getInstance()->create($params);
    }*/

    /**
     * 鉴定师详情
     *
     * @param Request $request
     * @return array
     * @throws ErrException
     */
    public function detail(Request $request)
    {
        $userinfoId = $request->input('userinfoId', 0);
        if ($userinfoId <= 0) {
           throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        return AppraiserLogic::getInstance()->detail($userinfoId);
    }

    /**
     * 编辑鉴定师
     *
     * @param Request $request
     * @return bool|int
     * @throws ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function edit(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
                'nickname' => 'required|string|max:40',
                'avatar' => 'required|string|max:200',
                'workType' =>'required|integer|in:1,2',
                //'isLeader' => 'required|integer|in:1,2',
                'isTrainee' => 'required|integer|in:1,2',
                'description' => 'required|string|max:200',
                'certificate' => 'nullable|string|max:200',
                'signaturePicture' => 'required|string|max:200',
            ]
        );

        $params = $request->all();
        return AppraiserLogic::getInstance()->edit($params);
    }

    public function editRest(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
                'restStartTime' => 'required|integer|gt:0',
                'restEndTime' => 'required|integer|gt:0',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        $restStartTime = $request->input('restStartTime');
        $restEndTime = $request->input('restEndTime');
        $params = compact('userinfoId', 'restStartTime', 'restEndTime');
        return AppraiserLogic::getInstance()->editRest($params);
    }


    /**
     * 启用/禁用 鉴定师
     * @param Request $request
     * @return bool|int
     * @throws ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    /*public function enable(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
                'state' => 'required|integer|in:0,1',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        $state = $request->input('state');
        return AppraiserLogic::getInstance()->enable($userinfoId, $state);
    }*/

    public function list(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'nullable|integer|gt:0',
                'workType' =>'nullable|integer|in:1,2',
                'isLeader' =>'nullable|integer|in:1,2',
                'isTrainee' =>'nullable|integer|in:1,2',
                'page' =>'required|integer|gt:0',
                'pageSize' =>'nullable|integer|gt:0',
            ]
        );
        $page = $this->page;
        $pageSize = $this->pageSize;
        $userinfoId = $request->input('userinfoId');
        $workType = $request->input('workType');
        $isLeader = $request->input('isLeader');
        $isTrainee = $request->input('isTrainee');
        $params = compact('page','pageSize', 'userinfoId', 'workType','isLeader','isTrainee');

        return AppraiserLogic::getInstance()->list($params);
    }

    /**
     * 绑定员工信息
     *
     * @param Request $request
     * @return bool|int
     * @throws ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    /*public function bindAffiliation(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
                'affiliationId' => 'required|string|max:100',
                'affiliationName' => 'required|string|max:50',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        $affiliationId = $request->input('affiliationId');
        $affiliationName = $request->input('affiliationName');

        return AppraiserLogic::getInstance()->bindAffiliation($userinfoId, $affiliationId, $affiliationName);
    }*/
}