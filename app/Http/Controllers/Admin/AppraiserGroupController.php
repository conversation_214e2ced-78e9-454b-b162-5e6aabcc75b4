<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\BaseController;
use App\Logic\AppraiserGroupLogic;
use Illuminate\Http\Request;

class AppraiserGroupController extends BaseController
{

    /**
     * 设置分组依赖数据
     *
     * @param Request $request
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function selectInfo(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        return AppraiserGroupLogic::getInstance()->selectInfo($userinfoId);
    }

    /**
     * 开启分组（组长设置）
     *
     * @param Request $request
     * @return null
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function add(Request $request)
    {
       /* $arr = [
            "isGroup" => 1,
            "userinfoId" => 2,
            "categoryIds" => [
                1 => [901,902],
                2 => [1921,1923],
            ],
            "memberUids" => [18,25],
        ];
        dd(wpt_json_encode($arr));*/

        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
                //'isGroup' =>'required|integer|in:0,1',
                'categoryIds' => 'required|array',
                'memberUids' => 'required|array',
            ]
        );
        $userinfoId = $request->input('userinfoId');
        $categoryIds = $request->input('categoryIds');
        $memberUids = $request->input('memberUids');

        $params = compact('userinfoId',  'categoryIds', 'memberUids');
        return AppraiserGroupLogic::getInstance()->add($params);
    }

    public function detail(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        return AppraiserGroupLogic::getInstance()->detail($userinfoId);
    }

    /**
     * 关闭分组
     *
     * @param Request $request
     * @return null
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function close(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        return AppraiserGroupLogic::getInstance()->close($userinfoId);
    }


    /**
     * 排班列表
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function scheduleList(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'startTime' => 'nullable|integer|gt:0',
                'endTime' =>'nullable|integer|gt:0',
            ]
        );

        $startTime = $request->input('startTime');
        $endTime = $request->input('endTime');

        $params = compact('startTime', 'endTime');
        return AppraiserGroupLogic::getInstance()->scheduleList($params);
    }

    /**
     * 排班编辑
     *
     * @param Request $request
     * @return bool|int
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function scheduleEdit(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
                'memberUid' => 'required|integer|gt:0',
                'assignedVolume' => 'required|integer',
                'assignmentDate' => 'required|date',
            ]
        );
        $userinfoId = $request->input('userinfoId');
        $memberUid = $request->input('memberUid');
        $assignedVolume = $request->input('assignedVolume');
        $assignmentDate = $request->input('assignmentDate');
        $params = compact('userinfoId', 'memberUid', 'assignedVolume', 'assignmentDate');
        return AppraiserGroupLogic::getInstance()->scheduleEdit($params);

    }
}