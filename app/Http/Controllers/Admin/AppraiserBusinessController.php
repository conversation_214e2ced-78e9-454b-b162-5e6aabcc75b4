<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\BaseController;
use App\Logic\AppraiserBusinessLogic;
use Illuminate\Http\Request;

class AppraiserBusinessController extends BaseController
{
    public function queryAppraiserBusiness(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' =>'required|integer',
            ]
        );
        $userinfoId = $request->input('userinfoId');
        return AppraiserBusinessLogic::getInstance()->queryAppraiserBusiness($userinfoId);
    }

    /**
     * 鉴定师业务类目编辑
     *
     * @param Request $request
     * @return true
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function edit(Request $request)
    {
        $state = $request->input('state');
        $validateArr = [
            'userinfoId' =>'required|integer|gt:0',
            'businessId' => 'required|integer|gt:0',
            'state' => 'required|integer|in:0,1',
        ];
        // 启用状态
        if ($state == 1) {
            $validateArr = array_merge($validateArr, [
                'category' =>'required|array',
                'extend' => 'nullable|array',
                'orderAcceptanceLimit' => 'nullable|integer',   // 寄售每日接单量
            ]);
        }
        $this->baseRequest->validate(
            $request,
            $validateArr
        );

        // 管理后台identPrice价格传入的是元，要处理成分
        if (!empty($request->input('extend'))) {
            $extend = $request->input('extend');
            foreach ($extend as &$extendItem) {
                if ($extendItem['field_key'] == 'identPrice') {
                    $extendItem['field_val'] = convert2cents($extendItem['field_val']); // 元转分
                }
            }
            // 覆盖
            $request->merge(['extend' => $extend]);
        }

        return AppraiserBusinessLogic::getInstance()->edit($request->all());
    }


    /**
     * 鉴定师的业务类目详情
     *
     * @param Request $request
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function detail(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' =>'required|integer|gt:0',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        $detail = AppraiserBusinessLogic::getInstance()->detail($userinfoId);

        // 价格 从分转为元
        foreach ($detail as &$item) {
            if (!empty($item['extend'])) {
                foreach ($item['extend'] as $key => &$extendItem) {
                    if ($key == 'identPrice') {
                        $extendItem['fieldVal'] = fenToYuan($extendItem['fieldVal']);
                    }
                }
            }
        }

        return [
            'detail' => $detail
        ];
    }
}
