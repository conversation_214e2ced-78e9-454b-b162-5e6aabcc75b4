<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\BaseController;
use App\Logic\BusinessCategoryLogic;
use Illuminate\Http\Request;

class BusinessCategoryController extends BaseController
{

    /**
     * 业务类目创建
     *
     * @param Request $request
     * @return bool
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function add(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'businessId' => 'required|integer|gt:0',   // 业务id
                'categoryName' => 'required|string|max:50',
                'categoryIdentifier' => 'required|string|max:50',   // 业务侧自定义类目标志
            ]
        );

        $businessId = $request->input('businessId');
        $categoryName = $request->input('categoryName');
        $categoryIdentifier = $request->input('categoryIdentifier');

        $params = compact('businessId','categoryName','categoryIdentifier');
        return BusinessCategoryLogic::getInstance()->add($params);
    }

    /**
     * 业务类目列表
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function listByBusiness(Request $request): array
    {
        $this->baseRequest->validate(
            $request,
            [
                'businessId' => 'required|integer|gt:0',
            ]
        );
        $businessId = $request->input('businessId');

        $params = compact('businessId');
        return BusinessCategoryLogic::getInstance()->listByBusiness($params);
    }

    /**
     * 全部业务类目列表
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function list(Request $request)
    {
        return BusinessCategoryLogic::getInstance()->list();
    }

    /**
     * 业务类目编辑
     *
     * @param Request $request
     * @return bool
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function edit(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' =>'required|integer|gt:0',
                'categoryName' =>'required|string|max:50',
                'categoryIdentifier' =>'required|string|max:50',
                'state' => 'nullable|integer|in:0,1',
            ]
        );

        $id = $request->input('id');
        $categoryName = $request->input('categoryName');
        $categoryIdentifier = $request->input('categoryIdentifier');
        $state = $request->input('state');

        $params = compact('id','categoryName','categoryIdentifier','state');
        return BusinessCategoryLogic::getInstance()->edit($params);
    }
}