<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\BaseController;
use App\Logic\BusinessLogic;
use Illuminate\Http\Request;

class BusinessController extends BaseController
{
    /**
     * 创建接入的业务
     *
     * @param Request $request
     * @return bool
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function add(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'name' =>'required|string|max:40',
                'notifyUrl' => 'nullable|string|max:255',
            ]
        );

        $name = $request->input('name');
        $notifyUrl = $request->input('notifyUrl');

        return BusinessLogic::getInstance()->add($name, $notifyUrl);
    }


    /**
     * 业务列表
     *
     * @param Request $request
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function list(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'state' => 'nullable|in:0,1',
                'page' =>'required|integer|gt:0',
                'pageSize' =>'nullable|integer|gt:0',
            ]
        );

        $state = $request->input('state', -1);
        $page = $this->page;
        $pageSize = $this->pageSize;
        $params = compact('state','page', 'pageSize');

        return BusinessLogic::getInstance()->list($params);
    }

    /**
     * 编辑业务
     *
     * @param Request $request
     * @return bool|int
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function edit(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' =>'required|integer|gt:0',
                'name' =>'required|string|max:40',
                'notifyUrl' =>'nullable|string|max:255',
            ]
        );

        $id = $request->input('id');
        $name = $request->input('name');
        $notifyUrl = $request->input('notifyUrl', '');
        $params = compact('name','notifyUrl');

        return BusinessLogic::getInstance()->edit($id, $params);
    }

    /**
     * 开关
     *
     * @param Request $request
     * @return array|bool|int
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function enable(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' => 'required|integer|gt:0',
                'state' => 'required|integer|in:0,1',
            ]
        );
        $id = $request->input('id');
        $state = $request->input('state');

        return BusinessLogic::getInstance()->enable($id, $state);
    }


}