<?php


namespace App\Http\Controllers\Inner;


use App\Constants\LogConst;
use App\Http\Controllers\BaseController;
use App\Logic\Inner\OrderLogic;
use App\Params\Order\OrderParams;
use Illuminate\Http\Request;
use Spin\Logger\Facades\Log;

/**
 * 订单
 *
 * Class OrderController
 * @package App\Http\Controllers\Inner
 */
class OrderController extends BaseController
{
    /**
     * 订单创建
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException|\Throwable
     */
    public function create(Request $request)
    {
        $params = $request->input();
        $orderParams = new OrderParams($params);

        Log::info(LogConst::IMAGE_IDENT_SERVER, '创建订单参数', $request->all());
        return OrderLogic::getInstance()->create($orderParams);
    }

    /**
     * 订单详情
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function detail(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'uri' => 'required|string',
            ]
        );

        $uri = $request->input('uri', '');

        return OrderLogic::getInstance()->detail($uri);
    }

    /**
     * 简单详情
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function simpleDetail(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'uri' => 'required|string',
            ]
        );

        $uri = $request->input('uri', '');

        return OrderLogic::getInstance()->simpleDetail($uri);
    }

    /**
     * 批量获取详情
     *
     * @param Request $request
     * @return array[]
     * @throws \Illuminate\Validation\ValidationException
     */
    public function batchSimpleDetail(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'uris' => 'required|array',
            ]
        );
        $uris = $request->input('uris', []);
        return OrderLogic::getInstance()->batchSimpleDetail($uris);
    }

    /**
     * 获取模板参数
     *
     * @param Request $request
     * @return mixed
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function getParams(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'uri' => 'required|string',
            ]
        );

        $uri = $request->input('uri', '');

        return OrderLogic::getInstance()->getParams($uri);
    }

    /**
     * 修改鉴定师/指定鉴定师
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException|\Illuminate\Validation\ValidationException
     */
    public function assignAppraiser(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'uri' => 'required|string',
                'appraiserId' => 'required|integer'
            ]
        );

        $uri = $request->input('uri', '');
        $appraiserId = intval($request->input('appraiserId', 0));
        return OrderLogic::getInstance()->assignAppraiser($uri, $appraiserId);
    }

    /**
     * 订单取消
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function cancel(Request $request)
    {
        Log::info(LogConst::IMAGE_IDENT_SERVER, "取消订单", $request->all());
        $this->baseRequest->validate(
            $request,
            [
                'uri' => 'required|string',
            ]
        );

        $uri = $request->input('uri', '');

        return OrderLogic::getInstance()->cancel($uri);
    }

    /**
     * 打回订单
     *
     * @param Request $request
     * @return bool
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function reject(Request $request)
    {
        Log::info(LogConst::IMAGE_IDENT_SERVER, "打回订单", ['request' => $request->all()]);

        $this->baseRequest->validate(
            $request,
            [
                'uri' => 'required|string',
                'userinfoId' => 'required|integer|gt:0',
                'rejectReason' => 'required|string|max:50'
            ]
        );

        $uri = $request->input('uri', '');
        $userinfoId = $request->input('userinfoId', 0);
        $rejectReason = $request->input('rejectReason', '');

        return OrderLogic::getInstance()->reject($uri, $userinfoId, $rejectReason);
    }


    /**
     * 订单列表
     *
     * @param Request $request
     * @return array
     */
    public function list(Request $request)
    {
        $params = $request->all();
        return OrderLogic::getInstance()->list($params);
    }

    /**
     * 订单提交
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     */
    public function submit(Request $request)
    {
        $params = $request->all();
        return OrderLogic::getInstance()->submit($params);
    }
}
