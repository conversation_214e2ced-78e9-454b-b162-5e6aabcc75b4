<?php

namespace App\Http\Controllers\Inner;

use App\ErrCode\BaseErr;
use App\Exceptions\ErrException;
use App\Http\Controllers\BaseController;
use App\Logic\Inner\AppraiserSearchLogic;
use App\Utils\Singleton;
use Illuminate\Http\Request;

class AppraiserSearchController extends BaseController
{
    use Singleton;

    public function detailByUserinfoId(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer|gt:0',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        return AppraiserSearchLogic::getInstance()->detailByUserinfoId($userinfoId);
    }

    public function listByUserinfoIds(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoIds' => 'required|array',
            ]
        );
        $userinfoIds = $request->input('userinfoIds');
        if (count($userinfoIds) > 5) {
            throw new ErrException(BaseErr::PARAMETER_ERROR, 'userinfoIds最多5个');
        }

        return AppraiserSearchLogic::getInstance()->listByUserinfoIds($userinfoIds);
    }

    public function simpleListByUserinfoIds(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoIds' => 'required|array',
            ]
        );
        $userinfoIds = $request->input('userinfoIds');
        return AppraiserSearchLogic::getInstance()->simpleListByUserinfoIds($userinfoIds);
    }

}