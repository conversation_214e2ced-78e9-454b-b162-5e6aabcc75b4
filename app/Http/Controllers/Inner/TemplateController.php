<?php


namespace App\Http\Controllers\Inner;


use App\Http\Controllers\BaseController;
use App\Logic\Inner\TemplateLogic;
use Illuminate\Http\Request;

/**
 * 模板管理
 *
 * Class TemplateController
 * @package App\Http\Controllers\Inner
 */
class TemplateController extends BaseController
{
    /**
     * 模板列表
     *
     * @param Request $request
     * @return array
     */
    public function list(Request $request)
    {
        $params = $request->all();

        return TemplateLogic::getInstance()->list($params);
    }

    /**
     * 创建模板
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function create(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'templateName' => 'required|string',
                'businessId' => 'required|integer',
                'bizType' => 'required|string',
            ]
        );
        return TemplateLogic::getInstance()->create($request->all());
    }

    /**
     * 编辑模板
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function edit(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' => 'required|integer',
                'templateName' => 'required|string',
            ]
        );

        return TemplateLogic::getInstance()->edit($request->all());
    }

    /**
     * 启用/禁用
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function enable(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' => 'required|integer',
                'state' => 'required|integer',
            ]
        );

        $id = intval($request->input('id'));
        $state = intval($request->input('state'));

        return TemplateLogic::getInstance()->enable($id, $state);
    }

    public function detail(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' => 'required|integer',
            ]
        );
        $id = intval($request->input('id'));
        return TemplateLogic::getInstance()->detail($id);
    }

    /**
     * 添加模板字段
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function addField(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'templateId' => 'required|integer',
                'fieldId' => 'required|integer',
                'outputType' => 'required|integer',
            ]
        );

        return TemplateLogic::getInstance()->addField($request->all());
    }

    /**
     * 更新模板字段
     *
     * @param Request $request
     * @return mixed
     * @throws \Illuminate\Validation\ValidationException
     */
    public function editField(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' => 'required|integer',
                'outputType' => 'required|integer',
            ]
        );

        return TemplateLogic::getInstance()->editField($request->all());
    }

    /**
     * 删除模板字段
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function delField(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' => 'required|integer',
            ]
        );

        $id = intval($request->input('id'));
        return TemplateLogic::getInstance()->delField($id);
    }
}
