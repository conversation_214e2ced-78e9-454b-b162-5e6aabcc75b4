<?php


namespace App\Http\Controllers\Inner;


use App\Http\Controllers\BaseController;
use App\Logic\Inner\FieldLogic;
use Illuminate\Http\Request;

/**
 * 字典(字段)
 *
 * Class FieldController
 * @package App\Http\Controllers\Inner
 */
class FieldController extends BaseController
{
    /**
     * 字典列表
     *
     * @param Request $request
     * @return array
     */
    public function list(Request $request)
    {
        $params = $request->all();

        return FieldLogic::getInstance()->list($params);
    }

    /**
     * 字典创建
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function create(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'name' => 'required|string',
                'businessId' => 'required|integer',
                'fieldType' => 'required|string',
                'fieldKey' => 'required|string',
                'bizType' => 'required|string',
            ]
        );

        $params = $request->all();
        return FieldLogic::getInstance()->create($params);
    }

    /**
     * 编辑字典
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function edit(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'name' => 'required|string',
                'id' => 'required|integer',
            ]
        );

        $params = $request->all();
        return FieldLogic::getInstance()->edit($params);
    }

    /**
     * 删除
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function deleted(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' => 'required|integer',
            ]
        );

        $id = intval($request->input('id'));
        return FieldLogic::getInstance()->deleted($id);
    }

    /**
     * 启用/禁用
     *
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ErrException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function enable(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'id' => 'required|integer',
                'state' => 'required|integer',
            ]
        );

        $id = intval($request->input('id'));
        $state = intval($request->input('state'));
        return FieldLogic::getInstance()->enable($id, $state);
    }
}
