<?php

namespace App\Http\Controllers\Inner;

use App\Http\Controllers\BaseController;
use App\Logic\Inner\AppraiserGroupLogic;
use Illuminate\Http\Request;

class AppraiserGroupController extends BaseController
{
    /**
     * 排班信息
     *
     * @param Request $request
     * @return array
     */
    public function scheduleInfo(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoIds' => 'required|array',
                'dateList' =>'required|array',
            ]
        );

        $userinfoIdList = $request->input('userinfoIds');
        $dateList = $request->input('dateList');

        return AppraiserGroupLogic::getInstance()->scheduleInfo($userinfoIdList, $dateList);
    }

}