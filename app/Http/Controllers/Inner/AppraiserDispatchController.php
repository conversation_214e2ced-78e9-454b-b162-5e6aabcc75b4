<?php

namespace App\Http\Controllers\Inner;

use App\ErrCode\BaseErr;
use App\Exceptions\ErrException;
use App\Http\Controllers\BaseController;
use App\Logic\Inner\AppraiserDispatchLogic;
use App\Logic\OrderAssignLogic;
use App\Service\OrderAssignService;
use Illuminate\Http\Request;
use Spin\Logger\Facades\Log;

/**
 * 鉴定师调度
 *
 * Class AppraiserDispatchController
 * @package App\Http\Controllers\Inner
 */
class AppraiserDispatchController extends BaseController
{
    public function list(Request $request)
    {
        Log::info("AppraiserDispatch", "搜索鉴定师", ["request" => $request->all()]);

        $fields = $request->all();
        if (!is_array($fields) || empty($fields)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        // 必要参数校验
        $fieldKeys = array_column($fields, 'field_key');
        if (!in_array('businessId', $fieldKeys)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        $rules = [];
        foreach ($fields as $index => $field) {
            $rules["{$index}.field_key"] = 'required|string';
            $rules["{$index}.field_val"] = 'required';
        }
        $this->baseRequest->validate(
            $request,
            $rules,
        );

        return AppraiserDispatchLogic::getInstance()->list($fields);
    }

    public function test(Request $request)
    {
        $businessId = $request->input('businessId');
        $categoryId = $request->input('categoryId');
        $userinfoId = $request->input('userinfoId');
        return OrderAssignLogic::getInstance()->assignRecheck2($businessId, $categoryId, $userinfoId);
    }

}