<?php

namespace App\Http\Controllers\Inner;

use App\Http\Controllers\BaseController;
use App\Http\Controllers\Controller;
use App\Logic\Inner\OrderDataLogic;
use Illuminate\Http\Request;

class OrderDataController extends BaseController
{
    /**
     * 获取业务订单数据
     *
     * @param Request $request
     * @return array[]
     * @throws \Illuminate\Validation\ValidationException
     */
    public function getBusinessOrderData(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer',
                'businessIds' => 'required|array',
            ]
        );
        $userinfoId = $request->input('userinfoId');
        $businessIds = $request->input('businessIds');
        return OrderDataLogic::getInstance()->getBusinessOrderData($userinfoId, $businessIds);
    }

    /**
     * 个人中心数据
     *
     * @param Request $request
     * @return array
     * @throws \Illuminate\Validation\ValidationException
     */
    public function center(Request $request)
    {
        $this->baseRequest->validate(
            $request,
            [
                'userinfoId' => 'required|integer',
            ]
        );

        $userinfoId = $request->input('userinfoId');
        $businessIds = $request->input('businessIds', []);
        return OrderDataLogic::getInstance()->center($userinfoId, $businessIds);
    }
}
