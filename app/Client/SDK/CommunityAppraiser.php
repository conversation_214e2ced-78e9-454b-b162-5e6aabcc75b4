<?php

namespace App\Client\SDK;

use App\Constants\LogConst;
use App\Utils\Singleton;
use Communityserver\Client\AppraiserApi;
use Spin\Logger\Facades\Log;

class CommunityAppraiser
{
    use Singleton;


    /**
     * 获取鉴定师排班信息
     * @param array $userinfoIds
     * @param string $workDate
     * @return array
     */
    public static function getGroupSchedule($userinfoIds, $workDate)
    {
        $teacherList = [];
        try {
            $params = [
                'date' => [$workDate],
                'appraiserUserinfoIds' => $userinfoIds
            ];
            $result = AppraiserApi::getInstance()->appraiserBatchGetGroupSchedule($params);
            Log::info(LogConst::IMAGE_IDENT_SERVER, '获取鉴定师排班信息SDK', ['params' => $params, 'result' => $result]);
            $scheduleList = $result['scheduleInfo'] ?? [];
            if (empty($scheduleList)) {
                return $teacherList;
            }

            // 组装下数据
            foreach ($scheduleList as $teacher) {
                $teacherList[$teacher['assignmentDate']][] = $teacher['appraiserUserInfoId'];
            }

            return $teacherList;
        } catch (\Throwable $e) {
            dd($e->getMessage());
            Log::error(LogConst::IMAGE_IDENT_SERVER, '获取鉴定师排班信息异常', ['params' => $params]);
        }

        return $teacherList;
    }

}