<?php

namespace App\Client\Bus;

use App\Constants\LogConst;
use Spin\Logger\Facades\Log;
use User\Client\InfoApi;

class BusUser extends BaseBus
{
    /**
     * 获取用户基础信息
     * @param string $identity 用户唯一标示，当前可传userinfoId、uri、openid
     * @param array $fields 获取字段
     * @return array
     */
    public static function getBaseInfo(string $identity, array $fields): array
    {
        try {
            //获取对象
            $object = InfoApi::getInstance()->getBaseInfo();
            //调用sdk
            $result = $object->setIdentity($identity)
                ->setField($fields)
                ->setTimeout(1500)
                ->setRetryTimes(1)
                ->doRequest();

            //处理返回值
            return self::dealResult($result);
        } catch (\Exception $e) {
            Log::tag(LogConst::IMAGE_IDENT_SERVER)->error(
                'bus',
                'user->getBaseInfo代理失败',
                [
                    'code' => $e->getCode(),
                    'msg' => $e->getMessage(),
                    ['req' => [$identity, $fields]]
                ]
            );
            return ['code' => $e->getCode(), 'msg' => $e->getMessage()];
        }
    }

}