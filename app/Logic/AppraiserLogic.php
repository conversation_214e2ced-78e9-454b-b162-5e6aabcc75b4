<?php

namespace App\Logic;

use App\Constants\CommonConst;
use App\ErrCode\AppraiserErr;
use App\Exceptions\ErrException;
use App\Models\AppraiserModel;
use App\Params\Appariser\AppraiserParams;
use App\Service\AppraiserService;
use App\Utils\Singleton;

class AppraiserLogic
{
    use Singleton;

    /**
     * 创建鉴定师
     *
     * @param $params
     * @return bool
     * @throws ErrException
     */
    /*public function create($params): bool
    {
        // 判断是否已存在
        $appraiser = AppraiserService::getInstance()->detail($params['userinfoId']);
        if (!empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_EXIST);
        }

        $params['state'] = CommonConst::STATE_ACTIVE;
        $params['joinTime'] = time();
        $appraiserParams = new AppraiserParams($params);

        return AppraiserService::getInstance()->create($appraiserParams);
    }*/

    /**
     * 鉴定师详情
     *
     * @param $userinfoId
     * @return array|null
     */
    public function detail($userinfoId)
    {
        // 获取基础信息
        $detail = AppraiserService::getInstance()->detail($userinfoId);
        return [
            'detail' => $detail
        ];
    }

    /**
     *
     * @param $params
     * @return int
     * @throws ErrException
     */
    public function edit($params)
    {
        $userinfoId = $params['userinfoId'];

        // 判断是否已存在
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }

        $updateParams = new AppraiserParams($params);
        return AppraiserService::getInstance()->updateByUserinfoId($userinfoId, $updateParams);
    }

    public function editRest($params)
    {
        $userinfoId = $params['userinfoId'];
        $restStartTime = $params['restStartTime'];
        $restEndTime = $params['restEndTime'];

        // 判断是否已存在
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }

        return AppraiserModel::query()
            ->where('userinfo_id', $userinfoId)
            ->update([
                'rest_start_time' => $restStartTime,
                'rest_end_time' => $restEndTime,
            ]);
    }

    /**
     * @param $userinfoId
     * @param $state
     * @return int
     * @throws ErrException
     */
    /*public function enable($userinfoId, $state)
    {
        // 判断是否已存在
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }

        return AppraiserModel::query()
            ->where('userinfo_id', $userinfoId)
            ->update(['state' => $state]);
    }*/

    public function list($params)
    {
        $page = $params['page'];
        $pageSize = $params['pageSize'];

        $result = AppraiserService::getInstance()->list($params, $page, $pageSize);
        return [
            'list' => $result['list'],
            'isEnd' => $result['isEnd'],
            'page' => $page ++,
            'pageSize' => $pageSize,
            'total' => $result['total']
        ];
    }

    /**
     * @param $userinfoId
     * @param $affiliationId
     * @param $affiliationName
     * @return int
     * @throws ErrException
     */
    /*public function bindAffiliation($userinfoId, $affiliationId, $affiliationName)
    {
        // 判断是否已存在
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }

        // 更新绑定
        return AppraiserModel::query()
            ->where('userinfo_id', $userinfoId)
            ->update([
                'affiliation_id' => $affiliationId,
                'affiliation_name' => $affiliationName
            ]);
    }*/
}
