<?php

namespace App\Logic;

use App\Service\OrderAssign\OrderAssignService;
use App\Utils\Singleton;

class OrderAssignLogic
{
    use Singleton;

    const SOURCE_GROUP_MAP = [
        'service_state' => 100260134,
        'yinsi' => 0,
    ];

    public function assign1($businessId, $category) {

        $userinfoIdList = $this->getImagePartTimeTeacherToday($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList = $this->getImageFullTimeTeacherToday($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList = OrderAssignService::getInstance()->getDefaultLeaderTeacher($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoId = OrderAssignService::getInstance()->getDefaultLastTeacher();
        if (!empty($userinfoId)) {
            return $userinfoId;
        }
    }

    public function assign2($businessId, $category, $userinfoId) {
        $today = date('Y-m-d');
        $userinfoIdList = OrderAssignService::getInstance()->getImageAssignFirstTeacher($userinfoId, $businessId, $category, $today);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList =$this->getImageFullTimeTeacherToday($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList = OrderAssignService::getInstance()->getDefaultLeaderTeacher($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoId = OrderAssignService::getInstance()->getDefaultLastTeacher();
        if (!empty($userinfoId)) {
            return $userinfoId;
        }
    }


    // 实物 -指定鉴定师 次日
    public function assign3($businessId, $category, $source = 'service_state') {
        $groupUserinfoId = self::SOURCE_GROUP_MAP[$source];
        $tomorrow = date('Y-m-d', strtotime('+1 day'));

        $userinfoIdList = OrderAssignService::getInstance()->getRealityAssignFirstNoDispatchCountTeacher($groupUserinfoId, $businessId, $category, $tomorrow);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList = OrderAssignService::getInstance()->getRealityFullTimeAssignGroupTeacher($groupUserinfoId, $businessId, $category, $tomorrow);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoId = OrderAssignService::getInstance()->getForceAssignLeaderTeacher($groupUserinfoId);
        if (!empty($userinfoId)) {
            return $userinfoId;
        }

        $userinfoId = OrderAssignService::getInstance()->getDefaultLastTeacher();
        if (!empty($userinfoId)) {
            return $userinfoId;
        }
    }

    public function assign4($businessId, $category) {
        $tomorrow = date('Y-m-d', strtotime('+1 day'));

        $userinfoIdList = OrderAssignService::getInstance()->getRealityFullTimeTeacher($businessId, $category, $tomorrow);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList = OrderAssignService::getInstance()->getDefaultLeaderTeacher($businessId, $category);
        //dd($useinfoIdList);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoId = OrderAssignService::getInstance()->getDefaultLastTeacher();
        if (!empty($userinfoId)) {
            return $userinfoId;
        }
    }

    public function assign5($businessId, $category, $userinfoId) {
        $tomorrow = date('Y-m-d', strtotime('+1 day'));

        $userinfoIdList = OrderAssignService::getInstance()->getRealityAssignFirstTeacher($userinfoId, $businessId, $category, $tomorrow);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList = OrderAssignService::getInstance()->getRealityFullTimeTeacher($businessId, $category, $tomorrow);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList = OrderAssignService::getInstance()->getDefaultLeaderTeacher($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        return OrderAssignService::getInstance()->getDefaultLastTeacher();
    }

    public function assign6($businessId, $category)
    {
        // 全职学徒
        $userinfoIdList = $this->getRealityFullTimeTraineeTeacherTomorrow($businessId, $category);
        //dd($userinfoIdList);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        // 全职非学徒
        $userinfoIdList = $this->getRealityFullTimeNotTraineeTeacherTomorrow($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList = OrderAssignService::getInstance()->getDefaultLeaderTeacher($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        return OrderAssignService::getInstance()->getDefaultLastTeacher();
    }

    /**
     * 实物复审 - 当前用户是否满足条件
     *
     * @param $businessId
     * @param $category
     * @param $userinfoId
     * @return bool
     * @throws \App\Exceptions\ErrException
     */
    public function checkAssignRecheck($businessId, $category, $userinfoId)
    {
        // 全职 & 实物寄售权限 & 复审权限
        $userinfoIdList = OrderAssignService::getInstance()->getRealityFullTimeRecheckTeacherWithNoDispatchCount($businessId, $category);
        return in_array($userinfoId, $userinfoIdList);
    }

    /**
     * 实物复审
     *
     * @param $businessId
     * @param $category
     * @return array|int|void
     */
    public function assignRecheck2($businessId, $category)
    {
        $userinfoIdList = $this->getRealityFullTimeRecheckTeacherToday($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        $userinfoIdList = OrderAssignService::getInstance()->getDefaultLeaderTeacher($businessId, $category);
        if (!empty($userinfoIdList)) {
            return $this->getRandUserinfoId($userinfoIdList);
        }

        return OrderAssignService::getInstance()->getDefaultLastTeacher();
    }




    private function getImageFullTimeTeacherToday($businessId, $category)
    {
        $today = date('Y-m-d');
        return OrderAssignService::getInstance()->getImageFullTimeTeacher($businessId, $category, $today);
    }

    private function getImageFullTimeTeacherTomorrw($businessId, $category)
    {
        $tomorrow = date('Y-m-d', strtotime('+1 day'));
        return OrderAssignService::getInstance()->getImageFullTimeTeacher($businessId, $category, $tomorrow);
    }

    private function getImagePartTimeTeacherToday($businessId, $category)
    {
        $today = date('Y-m-d');
        return OrderAssignService::getInstance()->getImagePartTimeTeacher($businessId, $category, $today);
    }

    private function getRealityFullTimeRecheckTeacherToday($businessId, $category)
    {
        $today = date('Y-m-d');
        return OrderAssignService::getInstance()->getRealityFullTimeRecheckTeacher($businessId, $category, $today);
    }

    private function getRealityFullTimeTraineeTeacherTomorrow($businessId, $category)
    {
        $tomorrow = date('Y-m-d', strtotime('+1 day'));
        // 全职学徒
        return OrderAssignService::getInstance()->getRealityFullTimeIfTraineeTeacher($businessId, $category, $tomorrow, true);
    }
    private function getRealityFullTimeNotTraineeTeacherTomorrow($businessId, $category)
    {
        $tomorrow = date('Y-m-d', strtotime('+1 day'));
        // 全职非学徒
        return OrderAssignService::getInstance()->getRealityFullTimeIfTraineeTeacher($businessId, $category, $tomorrow, false);
    }


    private function getRandUserinfoId($userinfoIdList)
    {
        return $userinfoIdList[array_rand($userinfoIdList)];
    }

}