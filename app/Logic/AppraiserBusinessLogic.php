<?php

namespace App\Logic;

use App\Constants\BusinessConst;
use App\Constants\CommonConst;
use App\ErrCode\AppraiserErr;
use App\ErrCode\BusinessErr;
use App\Exceptions\ErrException;
use App\Service\AppraiserBusinessCategoryService;
use App\Service\AppraiserBusinessExtendService;
use App\Service\AppraiserBusinessService;
use App\Service\AppraiserService;
use App\Service\BusinessCategoryService;
use App\Service\BusinessService;
use App\Utils\Singleton;
use Illuminate\Support\Facades\DB;

class AppraiserBusinessLogic
{
    use Singleton;

    /**
     * 获取用户业务
     *
     * @param $userinfoId
     * @return array[]
     */
    public function queryAppraiserBusiness($userinfoId)
    {
        $list = AppraiserBusinessService::getInstance()->getListByUserinfoId($userinfoId);
        $resultList = [];

        $businessIds = array_unique(array_column($list, 'business_id'));
        $businessList = BusinessService::getInstance()->getListByIds($businessIds);
        $businessList = array_column($businessList->toArray(), null, 'id');
        foreach ($list as $item) {
            if ($item['state'] == 0) {
                continue;
            }
            $business = $businessList[$item['business_id']] ?? [];
            $resultList[] = [
                'businessId' => $item['business_id'],
                'businessName' => $business['name'] ?? '',
            ];
        }
        return [
            'list' => $resultList
        ];
    }
    /**
     * 编辑用户业务相关权限
     *
     * @param $params
     * @return bool
     * @throws ErrException
     */
    public function edit($params)
    {
        $userinfoId = $params['userinfoId'];
        $businessId = $params['businessId'];
        $state = $params['state'];
        $category = $params['category'] ?? [];
        $extendArr = $params['extend'] ?? [];
        $orderAcceptanceLimit = $params['orderAcceptanceLimit'] ?? -1;   // 当前业务每日派单量 考虑到更新为0的情况，默认取-1

        // businessId验证
        if (empty(BusinessService::getInstance()->getOneById($businessId))) {
            throw new ErrException(BusinessErr::BUSINESS_NO_EXIST);
        }

        // category属性验证
        if (!empty($category)) {
            $categoryList = BusinessCategoryService::getInstance()->getListByBusinessId($businessId);
            foreach ($category as $categoryIdentifier) {
                if (!in_array($categoryIdentifier, array_column($categoryList, 'categoryIdentifier'))) {
                    throw new ErrException(BusinessErr::BUSINESS_CATEGORY_NO_EXIST);
                }
            }
        }

        // extend属性验证
        foreach ($extendArr as $item) {
            $fieldKey = $item['field_key'];
            if (!AppraiserBusinessExtendService::getInstance()->checkFieldKey($businessId, $fieldKey)) {
                throw new ErrException(BusinessErr::BUSINESS_EXTEND_ERR);
            }
        }

        try {
            // 开启事务
            DB::connection('image_ident_server')->beginTransaction();
            // 业务绑定处理
            if ($state == CommonConst::STATE_ACTIVE) {
                // 鉴定师类目
                AppraiserBusinessService::getInstance()->bindBusiness($userinfoId, $businessId, $orderAcceptanceLimit);
                // 业务下类目更新
                AppraiserBusinessCategoryService::getInstance()->unBindBusinessCategory($userinfoId, $businessId);
                AppraiserBusinessCategoryService::getInstance()->bindBusinessCategory($userinfoId, $businessId, $category);

                // 额外属性处理
                AppraiserBusinessExtendService::getInstance()->unBindBusinessExtends($userinfoId, $businessId);
                AppraiserBusinessExtendService::getInstance()->bindBusinessExtends($userinfoId, $businessId, $extendArr);

            } else {    // 业务解绑处理
                AppraiserBusinessService::getInstance()->unbindBusiness($userinfoId, $businessId);
                AppraiserBusinessCategoryService::getInstance()->unBindBusinessCategory($userinfoId, $businessId);
                AppraiserBusinessExtendService::getInstance()->unBindBusinessExtends($userinfoId, $businessId);
            }


            DB::connection('image_ident_server')->commit();
            return true;
        } catch (\Exception $e) {
            DB::connection('image_ident_server')->rollBack();
            throw new ErrException([$e->getcode(), $e->getMessage()]);
        }
    }

    public function detail($userinfoId)
    {
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }

        $businessList = BusinessService::getInstance()->getAll();

        // 当前鉴定师的业务的开关状态
        $appraiserBusinessList = AppraiserBusinessService::getInstance()->getListByUserinfoId($userinfoId);
        $appraiserBusinessIds = array_column($appraiserBusinessList, 'business_id');
        $appraiserBusinessDispatchCountMap = array_column($appraiserBusinessList, 'dispatch_count', 'business_id'); // 当前业务派单量


        // 开启业务对应的类目
        $categoryList = AppraiserBusinessCategoryService::getInstance()->getListByUserinfoId($userinfoId);
        $categoryListMap = [];
        foreach ($categoryList as $item) {
            $categoryListMap[$item['business_id']][] = $item['category_id'];
        }

        // 业务对应的其他属性
        $extendList = AppraiserBusinessExtendService::getInstance()->getListByUserinfoId($userinfoId);
        $extendListMap = [];
        foreach ($extendList as $item) {
            $businessId = $item['business_id'];
            $fieldKey = $item['field_key'];
            if (!AppraiserBusinessExtendService::getInstance()->checkFieldKey($businessId, $fieldKey)) {
                continue;
            }

            $extendListMap[$businessId][] = [
                'fieldName' => AppraiserBusinessExtendService::getInstance()->getFieldName($businessId, $fieldKey),
                'fieldKey' => $item['field_key'],
                'fieldVal' => is_numeric($item['field_val']) ? intval($item['field_val']) : $item['field_val'],
            ];

            // todo 拼接上业务下其他扩展属性？还是下发配置到前端 - 拼接上业务下其他扩展属性
        }

        // 组装数据
        foreach ($businessList as &$item) {
            $businessId = $item['id'];
            $state = in_array($businessId, $appraiserBusinessIds) ? 1 : 0;
            $item['businessId'] = $businessId;
            $item['state'] = $state;
            $item['category'] = $categoryListMap[$businessId] ?? [];
            // 寄售查验拼接上最大接单量
            if ($businessId == BusinessConst::BUSINESS_TYPE_CONSIGN_VERIFY) {
                $item['orderAcceptanceLimit'] = 0; // 默认为0
                if ($state == CommonConst::STATE_ACTIVE) {
                    $item['orderAcceptanceLimit'] = $appraiserBusinessDispatchCountMap[$businessId] ?? 0;
                }
            }
            $item['extend'] = $this->getExtendDetail($businessId, $extendListMap);

            unset($item['id']);
        }

        return $businessList;
    }

    public function batchDetail($userinfoIdList)
    {
        // 当前鉴定师的业务的开关状态
        $appraiserBusinessList = AppraiserBusinessService::getInstance()->getListByUserinfoIds($userinfoIdList);
        $appraiserBusinessList = collect($appraiserBusinessList)->groupBy('userinfo_id')->toArray();
        //dd($appraiserBusinessList);

        //$appraiserBusinessIdsMap = [];
        foreach ($appraiserBusinessList as $uid => $appraiserBusinessListItem) {
            // 开通的业务id
            $appraiserBusinessIds[$uid] = array_column($appraiserBusinessListItem, 'business_id');
            // 派单量
            $appraiserBusinessDispatchCountMap[$uid] = array_column($appraiserBusinessListItem, 'dispatch_count', 'business_id'); // 当前业务派单量
        }


        // 开启业务对应的类目
        $categoryList = AppraiserBusinessCategoryService::getInstance()->getListByUesrinfoIds($userinfoIdList);
        $categoryList = collect($categoryList)->groupBy('userinfo_id')->toArray();
        $categoryListMap = [];
        foreach ($categoryList as $uid => $items) {
            foreach ($items as $item) {
                $categoryListMap[$uid][$item['business_id']][] = $item['category_id'];
            }
        }
        //dd($categoryListMap);

        // 业务对应的其他属性
        $extendList = AppraiserBusinessExtendService::getInstance()->getListByUserinfoIds($userinfoIdList);
        $extendList = collect($extendList)->groupBy('userinfo_id')->toArray();
        //dd($extendList);
        $extendListMap = [];
        foreach ($extendList as $uid => $items) {
            foreach ($items as $item) {
                $businessId = $item['business_id'];
                $fieldKey = $item['field_key'];
                if (!AppraiserBusinessExtendService::getInstance()->checkFieldKey($businessId, $fieldKey)) {
                    continue;
                }

                $extendListMap[$uid][$businessId][] = [
                    'fieldName' => AppraiserBusinessExtendService::getInstance()->getFieldName($businessId, $fieldKey),
                    'fieldKey' => $item['field_key'],
                    'fieldVal' => is_numeric($item['field_val']) ? intval($item['field_val']) : $item['field_val'],
                ];
            }
        }
        //dd($extendListMap);

        // 所有业务
        $businessList = BusinessService::getInstance()->getAll();
//dd($appraiserBusinessIds);
        // 组装数据
        $resultList = [];
        foreach ($userinfoIdList as $uid) {
            foreach ($businessList as $business) {
                $result = [];

                $businessId = $business['id'];
                $state = in_array($businessId, $appraiserBusinessIds[$uid] ?? []) ? 1 : 0;
                $result['businessId'] = $businessId;
                $result['state'] = $state;
                $result['category'] = $categoryListMap[$uid][$businessId] ?? [];
                // 寄售查验拼接上最大接单量
                if ($businessId == BusinessConst::BUSINESS_TYPE_CONSIGN_VERIFY) {
                    $result['orderAcceptanceLimit'] = 0; // 默认为0
                    if ($state == CommonConst::STATE_ACTIVE) {
                        $result['orderAcceptanceLimit'] = $appraiserBusinessDispatchCountMap[$uid][$businessId] ?? 0;
                    }
                }
                $result['extend'] = $this->getExtendDetail($businessId, $extendListMap[$uid] ?? []);

                $resultList[$uid][] = $result;
            }
        }

        return $resultList;
    }

    private function getExtendDetail($businessId, $extendListMap)
    {
        $fieldKeysConfig = AppraiserBusinessExtendService::getInstance()->getFieldKeys($businessId);
        foreach($fieldKeysConfig as $fieldKey => $arr) {
            $fieldKeysConfig[$fieldKey] = [
                'fieldName' => $arr['name'],
                'fieldKey' => $fieldKey,
                'fieldVal' => $arr['defaultVal'],
            ];
        }

        // 已存在的
        $extendList = [];
        if (!empty($extendListMap[$businessId])) {
            $extendList = array_column($extendListMap[$businessId], null, 'fieldKey');
        }

        // 存在项覆盖配置默认项
        return (array_merge($fieldKeysConfig, $extendList));
    }

}
