<?php

namespace App\Logic;

use App\Constants\CommonConst;
use App\Models\AppraiserGroupMembersRelationModel;
use App\Params\Appariser\AppraiserParams;
use App\Service\AppraiserBusinessCategoryService;
use App\Service\AppraiserBusinessExtendService;
use App\Service\AppraiserBusinessService;
use App\Service\AppraiserGroupCategoryService;
use App\Service\AppraiserGroupMemberService;
use App\Service\AppraiserGroupScheduleService;
use App\Service\AppraiserGroupService;
use App\Service\AppraiserService;
use App\Utils\Singleton;
use Illuminate\Support\Facades\DB;
use Spin\Logger\Facades\Log;

class SyncAppraiserLogic
{
    use Singleton;

    protected $prefix = 'sync-community-appraiser';

    // 海外名家 鉴定师id，注意国内的不能放在里面
    const OVERSEA_EXPERT = [
        'dev' => [100320642, 11740300, 11742588],
        'prod' => [175723358, 175720899, 175796588, 54793162, 10137407, 60985426],    // 名家userinfoId
    ];

    public function syncAppraiser($appraiser, $appraiserExtend, $businessName = '')
    {
        $userinfoId = $appraiser['userinfoId'];
        // 1、鉴定师基础数据
        $appraiserParams = new AppraiserParams(['userinfoId' => $userinfoId]);
        $appraiserParams->nickname = $appraiser['nickname'];
        $appraiserParams->avatar = combineImgUrl($appraiser['avatar']);
        $appraiserParams->workType = $appraiser['appraiser_type'];  //(1全职,2兼职)
        //$appraiserParams->isLeader = $appraiser['is_leader'];// todo，在组长设置里
        $appraiserParams->isTrainee = $appraiserExtend['trainee_state'] == 2 ? 1 : 2;
        $appraiserParams->description = $appraiser['description'];
        $appraiserParams->personalProfile = $appraiser['profile'];
        $appraiserParams->certificate = combineImgUrl($appraiser['certificate']);
        $appraiserParams->signaturePicture = combineImgUrl($appraiserExtend['expert_signature_picture']);
        $appraiserParams->affiliationId = $appraiserExtend['affiliation_id'];
        $appraiserParams->affiliationName = $appraiserExtend['affiliation_name'];
        $appraiserParams->joinTime = $appraiser['settlingTime'];

        /**
         * 并发问题：
         * 批量洗数据是串行的没有问题，
         * 订阅事件更新，对同一个userinfoId加锁sleep
         */
        $appraiserItem = AppraiserService::getInstance()->detail($userinfoId, false);
        if (empty($appraiserItem)) {
            AppraiserService::getInstance()->create($appraiserParams);
        } else {
            // 存在删除的，激活一下
            if ($appraiserItem['state'] == CommonConst::STATE_DISABLE) {
                AppraiserService::getInstance()->activeByUserinfoId($userinfoId);
            }
            Log::info($this->prefix, '已存在鉴定师-更新数据', ['userinfoId' => $userinfoId]);
            AppraiserService::getInstance()->updateByUserinfoId($userinfoId, $appraiserParams);
        }

        // 2、按业务同步鉴定师数据
        // todo 自己设置权限后，去掉同步
        $businessNameList = [
            'general',
            'expert',
            'court',
            'oversea',
            //'consignValuate',//寄售估价
            //'consignVerify',//寄售查验
        ];
        if (!empty($businessName)) {
            $businessNameList = [$businessName];
        }

        foreach ($businessNameList as $businessName) {
            $params = $this->formatParams($appraiser, $appraiserExtend, $businessName);

            if (empty($params)) {
                Log::info($this->prefix, '无同步数据', ['userinfoId' => $userinfoId, 'businessName' => $businessName]);
                continue;
            }
            Log::info($this->prefix, '同步数据完成', ['userinfoId' => $userinfoId, 'businessName' => $businessName]);

            $res = AppraiserBusinessLogic::getInstance()->edit($params);
            Log::info($this->prefix, '同步数据完成', ['userinfoId' => $userinfoId, 'businessName' => $businessName, 'params' => $params]);
        }
    }

    public function deleteAppraiser($userinfoId)
    {
        try {
            DB::connection('image_ident_server')->beginTransaction();

            // 删除鉴定师权限相关
            AppraiserService::getInstance()->deleteByUserinfoId($userinfoId);
            AppraiserBusinessService::getInstance()->unBindBusinessBatch($userinfoId);
            AppraiserBusinessCategoryService::getInstance()->unBindBusinessCategoryBatch($userinfoId);
            AppraiserBusinessExtendService::getInstance()->unBindBusinessExtendsBatch($userinfoId);

            // 删除分组和排班相关
            $this->handleDeleteGroupAndSchedule($userinfoId);

            DB::connection('image_ident_server')->commit();
            return true;
        } catch (\Exception $e) {
            DB::connection('image_ident_server')->rollBack();
            Log::error($this->prefix, '删除鉴定师失败', $e->getMessage());

            return false;
        }
    }

    private function handleDeleteGroupAndSchedule($userinfoId)
    {
        // 是否组长
        $isLeader = AppraiserGroupService::getInstance()->isLeader($userinfoId);
        if ($isLeader) {
            // 组长，删除分组
            AppraiserGroupService::getInstance()->delByUserinfoId($userinfoId);
            AppraiserGroupCategoryService::getInstance()->delByUserinfoId($userinfoId);
            AppraiserGroupMemberService::getInstance()->delByUserinfoId($userinfoId);
            AppraiserGroupScheduleService::getInstance()->delByUserinfoId($userinfoId);
        }

        // 组员，删除组员
        AppraiserGroupMemberService::getInstance()->delByMemberUid($userinfoId);
        AppraiserGroupScheduleService::getInstance()->delByMemberUid($userinfoId);
    }

    private function formatParams($appraiser, $appraiserExtend, $businessName)
    {
        $userinfoId = $appraiser['userinfoId'];
        $params = [];
        if ($businessName == 'general') {
            // 国内普通图文
            $imageIdentifyState = $appraiserExtend['image_identify_state'] == 1 ? 1 : 0;
            $category = $appraiserExtend['image_identify_category'];
            $params = [
                "userinfoId" => $userinfoId,
                "businessId" => 1,
                "state" => $imageIdentifyState,
                "category" => array_filter(explode(',', $category)),
            ];
            $offHours = $appraiserExtend['image_identify_off_hours'];
            if (!empty($offHours)) {
                $params['extend'][] = ['field_name' => '休息时间接单', 'field_key' => 'offHours', 'field_val' => $offHours];
            }

        } elseif ($businessName == 'expert') {
            // 名家鉴定
            $state = $appraiser['is_expert'] == 1 ? 1 : 0;
            $category = $appraiser['expert_category'];
            $params = [
                "userinfoId" => $userinfoId,
                "businessId" => 2,
                "state" => $state,
                "category" => array_filter(explode(',', $category)),
            ];
            $identPrice = $appraiser['expert_price'];
            if (!empty($identPrice)) {
                $params['extend'][] = ['field_name' => '鉴定价格', 'field_key' => 'identPrice', 'field_val' => $identPrice];
            }

        } elseif ($businessName == 'court') {
            // 小法庭
            $state = $appraiser['is_petty_court'] == 1 ? 1 : 0;
            $category = $appraiserExtend['court_review_category'];
            $params = [
                "userinfoId" => $userinfoId,
                "businessId" => 3,
                "state" => $state,
                "category" => array_filter(explode(',', $category)),
            ];
            // extend
            $reviewType = $appraiserExtend['court_review_type'];
            $identPrice = $appraiserExtend['court_price'];
            if (!empty($reviewType)) {
                $params['extend'][] = ['field_name' => '评审类型', 'field_key' => 'reviewType', 'field_val' => $reviewType];
            }
            if (!empty($identPrice)) {
                $params['extend'][] = ['field_name' => '鉴定价格', 'field_key' => 'identPrice', 'field_val' => $identPrice];
            }
        } elseif ($businessName == 'oversea') {
            // 海外图文
            $isOversea = $appraiser['is_overseas'] == 1 ? 1 : 0;
            $params = [
                "userinfoId" => $userinfoId,
                "businessId" => 4,
                "state" => $isOversea,
                "category" => [1923,1924],
            ];
            // extend
            $isForeign = $appraiser['come_from'] == 2 ? 1 : 0; // 鉴定师归属 1国内 2国外
            $params['extend'][] = ['field_name' => '是否海外', 'field_key' => 'isForeign', 'field_val' => $isForeign];

            $envKey = is_test() ? 'dev' : 'prod';
            $expertList = self::OVERSEA_EXPERT[$envKey] ?? [];
            if (in_array($userinfoId, $expertList)) {
                $params['extend'][] = ['field_name' => '是否专家', 'field_key' => 'isExpert', 'field_val' => 1];
            }

            $customerId = $appraiser['customer_id'];
            if (!empty($customerId)) {
                $params['extend'][] = ['field_name' => '客户ID', 'field_key' => 'customerId', 'field_val' => $customerId];
            }
        } elseif ($businessName == 'consignValuate') {
            // 寄售评估
            $state = $appraiser['is_consign_evaluate'] == 1 ? 1 : 0;

        } else {
            //$this->error('businessName err');
            return [];
        }

        return $params;

    }

}