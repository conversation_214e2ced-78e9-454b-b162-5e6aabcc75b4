<?php

namespace App\Logic\Inner;

use App\ErrCode\BusinessErr;
use App\Exceptions\ErrException;
use App\Service\AppraiserBusinessCategoryService;
use App\Service\AppraiserBusinessExtendService;
use App\Service\AppraiserBusinessService;
use App\Service\AppraiserService;
use App\Service\BusinessCategoryService;
use App\Utils\Singleton;
use Spin\Logger\Facades\Log;

class AppraiserDispatchLogic
{
    use Singleton;

    /**
     * 查询鉴定师列表
     *
     * @param $params
     * @return array
     * @throws ErrException
     */
    public function list($params)
    {
        $businessId = $this->getFieldValByFieldKey($params, 'businessId');
        $cateIdentifier = $this->getFieldValByFieldKey($params, 'category');

        // 过滤合法的field_key字段
        $fieldKeys = AppraiserBusinessExtendService::getInstance()->getFieldKeys($businessId);
        $extendParams = array_filter($params, function($item) use ($fieldKeys) {
            return in_array($item['field_key'], array_keys($fieldKeys));
        });

        // 没有类目，通过业务查询
        if (empty($cateIdentifier)) {
            $appraiserList = $this->listByBusiness($businessId, $extendParams);
        } else {
            // 通过业务和类目查询
            $appraiserList = $this->listByBusinessCategory($businessId, $cateIdentifier, $extendParams);
        }

        Log::info("AppraiserDispatch", "搜索鉴定师结果", ["params" => $params, "appraiserList" => $appraiserList]);

        return [
            'list' => $appraiserList
        ];
    }


    private function listByBusiness($businessId, $extendParams): array
    {
        // 获取当前业务下所有鉴定师
        $userinfoIdList = AppraiserBusinessService::getInstance()->getListByBusinessId($businessId);
        $userinfoIdList = array_unique(array_column($userinfoIdList, 'userinfo_id'));
        // 查appraiser表，过滤掉状态为0的
        $userinfoList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIdList);
        $userinfoIdList = array_column($userinfoList, 'userinfoId');

        // 鉴定师开通的类目
        $where['businessId'] = $businessId;
        $businessCategoryList = AppraiserBusinessCategoryService::getInstance()->getListByUesrinfoIds($userinfoIdList, $where);
        $categoryByUid = [];
        foreach ($businessCategoryList as $item) {
            $categoryByUid[$item['userinfo_id']][] = $item['category_id'];
        }

        // 通过extend条件过滤
        $list = AppraiserBusinessExtendService::getInstance()->getListByField($userinfoIdList, $businessId, $extendParams);
        $userinfoIdList = array_unique(array_keys($list));
        //dd($userinfoIdList);

        // 获取鉴定师信息
        $appraiserList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIdList);
        foreach ($appraiserList as &$appraiser) {
            $userinfoId = $appraiser['userinfoId'];
            $appraiser['category'] = $categoryByUid[$userinfoId]?? [];
            foreach ($list[$userinfoId] as $extend) {
                $appraiser[$extend['field_key']] = is_numeric($extend['field_val']) ? intval($extend['field_val']) : $extend['field_val'];
            }
        }

        // todo 排班情况
        return $appraiserList;
    }

    private function listByBusinessCategory($businessId, $cateIdentifier, $extendParams): array
    {
        // 校验类目是否合法
        $categoryList = BusinessCategoryService::getInstance()->getListByBusinessId($businessId);
        if (! in_array($cateIdentifier, array_column($categoryList, 'categoryIdentifier'))) {
            throw new ErrException(BusinessErr::BUSINESS_CATEGORY_NO_EXIST);
        }

        // 获取当前业务下类目的所有鉴定师
        $where = [
            'businessId' => $businessId,
            'categoryId' => $cateIdentifier
        ];
        $list = AppraiserBusinessCategoryService::getInstance()->getList($where);
        $userinfoIdList = array_column($list, 'userinfo_id');
        // 查appraiser表，过滤掉状态为0的
        $userinfoList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIdList);
        $userinfoIdList = array_column($userinfoList, 'userinfoId');

        // 鉴定师开通的类目
        $where['businessId'] = $businessId;
        $businessCategoryList = AppraiserBusinessCategoryService::getInstance()->getListByUesrinfoIds($userinfoIdList, $where);
        $categoryByUid = [];
        foreach ($businessCategoryList as $item) {
            $categoryByUid[$item['userinfo_id']][] = $item['category_id'];
        }

        // 获取满足extend
        $list = AppraiserBusinessExtendService::getInstance()->getListByField($userinfoIdList, $businessId, $extendParams);
        $userinfoIdList = array_unique(array_keys($list));
        //dd($list, $userinfoIdList);
        // 获取鉴定师信息
        $appraiserList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIdList);
        foreach ($appraiserList as &$appraiser) {
            $userinfoId = $appraiser['userinfoId'];
            //dd($list[$userinfoId]);
            $appraiser['category'] = $categoryByUid[$userinfoId]?? [];
            foreach ($list[$userinfoId] as $extend) {
                $appraiser[$extend['field_key']] = is_numeric($extend['field_val']) ? intval($extend['field_val']) : $extend['field_val'];
            }
        }

        // todo 排班情况
        return $appraiserList;
    }

    private function getFieldValByFieldKey($array, $searchKey) {
        $filtered = array_filter($array, function($item) use ($searchKey) {
            return isset($item['field_key']) && $item['field_key'] === $searchKey;
        });

        if (!empty($filtered)) {
            return reset($filtered)['field_val']; // 获取第一个匹配项的field_val
        }

        return null;
    }

}