<?php

namespace App\Logic\Inner;

use App\Service\AppraiserGroupScheduleService;
use App\Utils\Singleton;

class AppraiserGroupLogic
{
    use Singleton;


    public function scheduleInfo($userinfoIdList, $dateList)
    {
       $scheduleList = AppraiserGroupScheduleService::getInstance()->getListByMemberUidsAndAssignmentDate($userinfoIdList, $dateList);
       return [
           'list' => $scheduleList,
       ];

    }


}