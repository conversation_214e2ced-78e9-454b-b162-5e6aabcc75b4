<?php


namespace App\Logic\Inner;


use App\Constants\CommonConst;
use App\ErrCode\BaseErr;
use App\ErrCode\FieldErr;
use App\ErrCode\TemplateErr;
use App\Exceptions\ErrException;
use App\Models\BusinessTemplateModel;
use App\Service\BusinessService;
use App\Service\FieldService;
use App\Service\TemplateService;
use App\Utils\Singleton;

/**
 * 模板管理
 *
 * Class TemplateLogic
 * @package App\Logic\Inner
 */
class TemplateLogic
{
    use Singleton;

    /**
     * 模板列表
     *
     * @param array $params
     * @return array
     */
    public function list(array $params)
    {
        $page = max(intval($params['page'] ?? 1), 1);
        $pageSize = intval($params['pageSize'] ?? 20);
        $query = $this->buildListWhere($params);
        $list = $query->forPage($page, $pageSize)->get();

        return [
            'page' => $page + 1,
            'pageSize' => $pageSize,
            'list' => $this->formatList($list),
            'isEnd' => count($list) < $pageSize
        ];
    }

    /**
     * 创建模板
     *
     * @param array $params
     * @return array
     * @throws ErrException
     */
    public function create(array $params)
    {
        $businessId = $params['businessId'];
        $templateName = $params['templateName'];
        $bizType = $params['bizType'];
        if (empty(BusinessService::getInstance()->getOneById($businessId))) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        if (TemplateService::getInstance()->exist($businessId, $templateName)) {
            throw new ErrException(TemplateErr::TEMPLATE_EXISTS);
        }

        TemplateService::getInstance()->create($businessId, $templateName, $bizType);
        return [];
    }

    /**
     * 编辑模板
     *
     * @param array $params
     * @return array
     * @throws ErrException
     */
    public function edit(array $params)
    {
        $id = $params["id"];
        $templateName = $params['templateName'];

        $template = TemplateService::getInstance()->getTemplateById($id);
        if (empty($template)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        TemplateService::getInstance()->updateById($template->id, ['template_name' => $templateName]);
        return [];
    }

    /**
     * 获取详情
     *
     * @param int $id
     * @return array
     * @throws ErrException
     */
    public function detail(int $id)
    {
        $template = TemplateService::getInstance()->getTemplateDetail($id);
        if (empty($template)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        return $template;
    }

    /**
     * 启用/禁用
     *
     * @param int $id
     * @param int $state
     * @return array
     * @throws ErrException
     */
    public function enable(int $id, int $state)
    {
        $template = TemplateService::getInstance()->getTemplateById($id);
        if (empty($template)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        if (!in_array($state, [0, 1])) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        TemplateService::getInstance()->updateById($id, ['state' => $state]);

        return [];
    }

    /**
     * 添加模板字段
     *
     * @param array $params
     * @return array
     * @throws ErrException
     */
    public function addField(array $params)
    {
        $templateId = $params['templateId'];
        $template = TemplateService::getInstance()->getTemplateById($templateId);
        if (empty($template)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }
        $fieldId = $params['fieldId'];
        $field = FieldService::getInstance()->getFieldById($fieldId);
        if (empty($field)) {
            throw new ErrException(FieldErr::FIELD_NOT_EXISTS);
        }
        $data = [
            'template_id' => $templateId,
            'parent_id' => $params['parentId'] ?? 0,
            'parent_option_name' => $params['parentOptionName'] ?? '',
            'field_id' => $fieldId,
            'field_key' => $field['fieldKey'],
            'output_type' => $params['outputType'],
        ];
        TemplateService::getInstance()->createTemplateField($data);
        return [];
    }

    /**
     * 更新模板字段
     *
     * @param array $params
     * @return array
     * @throws ErrException
     */
    public function editField(array $params)
    {
        $id = intval($params['id']);
        $templateField = TemplateService::getInstance()->getTemplateFileById($id);
        if (empty($templateField)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        $data = [
            'output_type' => $params['outputType'],
            'parent_option_name' => $params['parentOptionName'] ?? '',
            'parent_id' => $params['parentId'] ?? 0
        ];

        TemplateService::getInstance()->updateTemplateFieldById($templateField->id, $data);
        return [];
    }

    /**
     * 删除模板字段
     *
     * @param int $id
     * @return array
     * @throws ErrException
     */
    public function delField(int $id)
    {
        $templateField = TemplateService::getInstance()->getTemplateFileById($id);
        if (empty($templateField)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        TemplateService::getInstance()->updateTemplateFieldById($templateField->id, ['is_deleted' => CommonConst::DELETED]);
        return [];
    }

    /**
     * 构建列表条件
     *
     * @param array $params
     * @return BusinessTemplateModel|\Illuminate\Database\Eloquent\Builder
     */
    private function buildListWhere(array $params)
    {
        $query = BusinessTemplateModel::query()->where('is_deleted', 0);
        if (!empty($params['businessId'])) {
            $query->where('business_id', $params['businessId']);
        }

        if (!empty($params['bizType'])) {
            $query->where('biz_type', $params['bizType']);
        }

        if (isset($params['state']) && is_numeric($params['state'])) {
            $query->where('state', $params['state']);
        }
        return $query;
    }

    /**
     * 格式化输出
     *
     * @param $list
     * @return array
     */
    private function formatList($list)
    {
        if (empty($list)) {
            return [];
        }
        $businessIds = array_column($list->toArray(), 'business_id');
        $businessList = BusinessService::getInstance()->getListByIds($businessIds);
        $businessList = array_column($businessList->toArray(), null, 'id');
        $resultList = [];
        /** @var BusinessTemplateModel $item */
        foreach ($list as $item) {
            $business = $businessList[$item->business_id] ?? [];
            $resultList[] = [
                'id' => $item->id,
                'businessId' => $item->business_id,
                'businessName' => $business['name'] ?? '',
                'templateName' => $item->template_name,
                'bizType' => $item->biz_type,
                'state' => $item->state,
                'createTime' => date('Y-m-d H:i:s', $item->create_time)
            ];
        }
        return $resultList;
    }
}
