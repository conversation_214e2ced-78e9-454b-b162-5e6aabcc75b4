<?php

namespace App\Logic\Inner;

use App\Constants\OrderConst;
use App\Models\OrderModel;
use App\Utils\Singleton;

class OrderDataLogic
{
    use Singleton;

    /**
     * 获取业务订单数据
     *
     * @param $userinfoId
     * @param $businessIds
     * @return array[]
     */
    public function getBusinessOrderData($userinfoId, $businessIds)
    {
        $result = OrderModel::query()->where('userinfo_id', $userinfoId)
            ->where('is_deleted', 0)
            ->whereIn('business_id', $businessIds)
            ->selectRaw('
                business_id as businessId,
                COUNT(*) as totalOrders,
                SUM(CASE WHEN state = 20 THEN 1 ELSE 0 END) as pendingOrders
            ')
            ->groupBy('business_id')
            ->get()
            ->toArray();

        return [
            'list' => $result,
        ];
    }

    /**
     * 获取个人中心数据
     *
     * @param $userinfoId
     * @param $businessIds
     * @return array
     */
    public function center($userinfoId, $businessIds)
    {
        $todayIdentCount = OrderModel::query()->where('userinfo_id', $userinfoId)
            ->where('ident_time', '>', strtotime(date('Y-m-d')))
            ->where('state', OrderConst::ORDER_STATE_COMPLETE)
            ->when(!empty($businessIds), function ($query) use ($businessIds) {
                $query->whereIn('business_id', $businessIds);
            })
            ->count();

        $monthIdentCount = OrderModel::query()->where('userinfo_id', $userinfoId)
            ->where('ident_time', '>=', strtotime(date('Y-m-01')))
            ->where('state', OrderConst::ORDER_STATE_COMPLETE)
            ->when(!empty($businessIds), function ($query) use ($businessIds) {
                $query->whereIn('business_id', $businessIds);
            })
            ->count();

        $lastMonthIdentCount = OrderModel::query()->where('userinfo_id', $userinfoId)
            ->where('ident_time', '>=', strtotime(date('Y-m-01', strtotime('-1 month'))))
            ->where('ident_time', '<', strtotime(date('Y-m-01')))
            ->where('state', OrderConst::ORDER_STATE_COMPLETE)
            ->when(!empty($businessIds), function ($query) use ($businessIds) {
                $query->whereIn('business_id', $businessIds);
            })
            ->count();

        $totalIdentCount = OrderModel::query()->where('userinfo_id', $userinfoId)
            ->where('state', OrderConst::ORDER_STATE_COMPLETE)
            ->where('detail_json', 'like', '%newSubmit%')
            ->when(!empty($businessIds), function ($query) use ($businessIds) {
                $query->whereIn('business_id', $businessIds);
            })
            ->count();

        return [
            'todayIdentCount' => $todayIdentCount,
            'monthIdentCount' => $monthIdentCount,
            'lastMonthIdentCount' => $lastMonthIdentCount,
            'totalIdentCount' => $totalIdentCount,
        ];
    }
}
