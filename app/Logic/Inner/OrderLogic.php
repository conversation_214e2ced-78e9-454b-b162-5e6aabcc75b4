<?php


namespace App\Logic\Inner;

use App\Cache\AppraiserCache;
use App\Constants\AppraiserConst;
use App\Constants\BusinessConst;
use App\Constants\LogConst;
use App\Constants\OrderConst;
use App\Constants\TemplateConst;
use App\ErrCode\AppraiserErr;
use App\ErrCode\BaseErr;
use App\ErrCode\FieldErr;
use App\ErrCode\OrderErr;
use App\ErrCode\TemplateErr;
use App\Exceptions\ErrException;
use App\Libraries\RedLock;
use App\Models\BusinessFieldModel;
use App\Models\BusinessTemplateFieldModel;
use App\Models\OrderItemFieldModel;
use App\Models\OrderItemModel;
use App\Models\OrderModel;
use App\Params\Order\OrderParams;
use App\Service\AppraiserService;
use App\Service\AsyncService;
use App\Service\BusinessCategoryService;
use App\Service\BusinessService;
use App\Service\OrderCommissionService;
use App\Service\OrderService;
use App\Service\OrderSubmitService;
use App\Service\TemplateService;
use App\Utils\Production;
use App\Utils\Singleton;
use App\Utils\StringUtil;
use Spin\Logger\Facades\Log;

/**
 * 订单服务
 *
 * Class OrderLogic
 * @package App\Logic\Inner
 */
class OrderLogic
{
    use Singleton;

    /**
     * 业务下单
     *
     * @param OrderParams $params
     * @return array
     * @throws \Throwable
     */
    public function create(OrderParams $params)
    {
        $params = $this->makeCreateParams($params);

        $uri = StringUtil::getUri(6);
        $orderId = OrderService::getInstance()->createOrder($params, ['uri' => $uri]);

        if (empty($orderId)) {
            throw new ErrException(OrderErr::CREATE_ORDER_FAIL);
        }
        // 判断是否配置了结算
        OrderCommissionService::getInstance()->orderCommission($params->businessId, $params->categoryIdentifier, $params->appraiserId, $orderId, $params->orderAmount, $params->subType);

        // 同步图片
        if ($params->businessId == 4) {
            AsyncService::syncOverseaImg($uri);
        }
        return [
            'uri' => $uri,
            'appraiserId' => $params->appraiserId,
            'appraiserName' => AppraiserService::getInstance()->detail($params->appraiserId)['nickname'] ?? '',
        ];
    }

    /**
     * 获取参数
     *
     * @param string $uri
     * @return mixed
     * @throws ErrException
     */
    public function getParams(string $uri)
    {
        $order = OrderService::getInstance()->getOrderByUri($uri);
        if (empty($order)) {
            throw new ErrException(OrderErr::ORDER_DOES_NOT_EXIST);
        }
        $data['inputParams'] = OrderService::getInstance()->getOrderSubmitParams($order);
        Log::info("order", 'getPrams', $data);
        return $data;
    }

    /**
     * 订单详情
     *
     * @param string $uri
     * @return array
     * @throws ErrException
     */
    public function detail(string $uri)
    {
        Log::info("order", '获取详情', ['uri' => $uri]);
        $order = OrderService::getInstance()->getOrderByUri($uri);
        if (empty($order)) {
            throw new ErrException(OrderErr::ORDER_DOES_NOT_EXIST);
        }
        $inputData = OrderService::getInstance()->getOrderInputData($order);
        $outputData = OrderService::getInstance()->getOrderOutputData($order);

        $category = BusinessCategoryService::getInstance()->getCategoryByIdentifier($order->category_identifier, $order->business_id);
        return [
            'uri' => $order->uri,
            'businessId' => $order->business_id,
            'cover' => $order->cover,
            'businessName' => BusinessService::getInstance()->getOneById($order->business_id)['name'] ?? '',
            'businessNo' => $order->business_no,
            'businessMasterNo' => $order->business_master_no,
            'categoryIdentifier' => $order->category_identifier,
            'categoryId' => $order->category_id,
            'state' => $order->state,
            'categoryName' => $category ? $category->category_name : '',
            'endTime' => $order->end_time,
            'inputTemplateId' => $order->input_template_id,
            'outputTemplateId' => $order->output_template_id,
            'appraiserId' => $order->userinfo_id,
            'detailJson' => json_decode($order->detail_json, true),
            'acceptTime' => date('Y-m-d H:i:s', $order->accept_time),
            'createTime' => date('Y-m-d H:i:s', $order->create_time),
            'input' => $inputData,
            'output' => $outputData,
        ];
    }

    /**
     * 简单详情
     *
     * @param string $uri
     * @return array
     * @throws ErrException
     */
    public function simpleDetail(string $uri)
    {
        $detail = $this->detail($uri);
        $outputData = [];
        if ($detail['output']) {
            foreach ($detail['output']['items'] as $item) {
                $fieldObject = [];
                foreach ($item as $field) {
                    $fieldObject[$field['fieldKey']] = $field['fieldValue'];
                }

                $outputData['items'][] = $fieldObject;
            }
            $fieldObject = [];
            foreach ($detail['output']['order'] as $field) {
                $fieldObject[$field['fieldKey']] = $field['fieldValue'];
            }

            $outputData['order'] = $fieldObject;
        }

        $detail['output'] = $outputData;
        unset($detail['input']);
        return $detail;
    }

    /**
     * 批量获取详情
     *
     * @param $uris
     * @return array[]
     */
    public function batchSimpleDetail($uris)
    {
        $result = [];
        foreach ($uris as $uri) {
            try {
                $result[$uri] = $this->simpleDetail($uri);
            } catch (\Throwable $e) {
                Log::error(LogConst::IMAGE_IDENT_SERVER, '获取订单失败', $uri);
            }
        }

        return ['list' => $result];
    }

    /**
     * 修改鉴定师傅/指定鉴定师
     *
     * @param string $uri
     * @param int $appraiserId
     * @return array
     * @throws ErrException
     */
    public function assignAppraiser(string $uri, int $appraiserId)
    {
        $order = OrderService::getInstance()->getOrderByUri($uri);
        if (empty($order)) {
            throw new ErrException(OrderErr::ORDER_DOES_NOT_EXIST);
        }
        // 状态判断
        if (!in_array($order->state, [OrderConst::ORDER_STATE_WAIT_DISTRIBUTION, OrderConst::ORDER_STATE_WAIT_IDENTIFY])) {
            throw new ErrException(OrderErr::ORDER_STATUS_CANNOT_ASSIGN_OR_MODIFY);
        }

        $appraiser = AppraiserService::getInstance()->detail($appraiserId);
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }

        $saveData = [
            'userinfo_id' => $appraiserId,
        ];
        if ($order->state == OrderConst::ORDER_STATE_WAIT_DISTRIBUTION) {
            $saveData['state'] = OrderConst::ORDER_STATE_WAIT_IDENTIFY;
        }

        $ret = OrderService::getInstance()->updateById($order->id, $saveData);
        if (empty($ret)) {
            throw new ErrException(OrderErr::ORDER_ASSIGN_OR_MODIFY_FAIL);
        }
        return [
            'state' => 1,
        ];
    }

    /**
     * 订单取消
     *
     * @param string $uri
     * @return array
     * @throws ErrException
     */
    public function cancel(string $uri)
    {
        $order = OrderService::getInstance()->getOrderByUri($uri);
        if (empty($order)) {
            throw new ErrException(OrderErr::ORDER_DOES_NOT_EXIST);
        }

        // 状态判断
        if (!in_array($order->state, [OrderConst::ORDER_STATE_WAIT_DISTRIBUTION, OrderConst::ORDER_STATE_WAIT_IDENTIFY])) {
            throw new ErrException(OrderErr::ORDER_STATUS_CANNOT_CANCEL);
        }

        $saveData = [
            'state' => OrderConst::ORDER_STATE_CANCEL,
        ];
        $ret = OrderService::getInstance()->updateById($order->id, $saveData);
        if (empty($ret)) {
            throw new ErrException(OrderErr::ORDER_CANCEL_FAIL);
        }
        Log::info(LogConst::IMAGE_IDENT_SERVER, "取消订单成功", $uri);
        return [];
    }

    public function reject($uri, $userinfoId, $rejectReason)
    {
        $order = OrderService::getInstance()->getOrderByUri($uri);
        if (empty($order)) {
            throw new ErrException(OrderErr::ORDER_DOES_NOT_EXIST);
        }

        // 状态判断
        if (!in_array($order->state, [OrderConst::ORDER_STATE_COMPLETE])) {
            throw new ErrException(OrderErr::ORDER_STATUS_CANNOT_REJECT);
        }

        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }

        // 获取拒绝原因field
        $templateField = BusinessTemplateFieldModel::query()
            ->where('is_deleted', 0)
            ->where('template_id', $order->input_template_id)
            ->where('field_key', 'rejectReason')
            ->first();
        if (empty($templateField)) {
            throw new ErrException(FieldErr::FIELD_NOT_EXISTS);
        }
        $field = BusinessFieldModel::query()
            ->where('id', $templateField->field_id)
            ->first();
        if (empty($field)) {
            throw new ErrException(FieldErr::FIELD_NOT_EXISTS);
        }

        // 获取orderItem数据
        $orderItem = OrderItemModel::query()
            ->where('order_id', $order->id)
            ->first();
        if (empty($orderItem)) {
            throw new ErrException(OrderErr::ORDER_ITEM_NO_EXIST);
        }


        // 更新订单状态
        $detailJson = json_decode($order->detail_json, true);
        $detailJson['rejectFlag'] = 1; // 退回标记
        $saveData = [
            'userinfo_id' => $userinfoId,
            'state' => OrderConst::ORDER_STATE_WAIT_IDENTIFY,
            'detail_json' => wpt_json_encode($detailJson)
        ];
        $ret = OrderService::getInstance()->updateById($order->id, $saveData);
        if (empty($ret)) {
            throw new ErrException(OrderErr::ORDER_REJECT_FAIL);
        }

        // 插入拒绝原因
        $orderItemField = [
            'order_id' => $order->id,
            'order_item_id' => $orderItem->id,
            'biz_type' => OrderConst::BIZ_TYPE_INPUT,
            'field_id' => $field->id,
            'field_name' => $field->name,
            'field_key' => $field->field_key,
            'field_value' => $rejectReason,
            'field_type' => $field->field_type,
            'output_type' => OrderConst::OUTPUT_TYPE_ORDER_ITEM, // 订单维度
            'create_time' => time(),
        ];
        $res = OrderItemFieldModel::query()->insert($orderItemField);
        Log::info(LogConst::IMAGE_IDENT_SERVER, "订单退回成功", ['uri' => $uri, 'userinfoId' => $userinfoId, 'rejectReason' => $rejectReason]);

        return [
            'result' => $res
        ];
    }

    /**
     * 获取列表数据
     *
     * @param array $params
     * @return array
     */
    public function list(array $params)
    {
        $page = max(intval($params['page'] ?? 1), 1);
        $pageSize = intval($params['pageSize'] ?? 20);

        $list = $this->buildListWhere($params)
            ->forPage($page, $pageSize)
            ->orderBy('create_time', 'desc')
            ->get();

        return [
            'page' => $page + 1,
            'pageSize' => $pageSize,
            'list' => $this->formatList($list),
            'isEnd' => count($list) < $pageSize
        ];
    }

    /**
     * 鉴定单提交
     *
     * @param array $params
     * @return array
     * @throws ErrException|\Throwable
     */
    public function submit(array $params)
    {
        Log::info("order", '订单提交', $params);
        $uri = $params['uri'];
        $lock = RedLock::getInstance()->lock('submit_' . $uri, 10);
        if (!$lock) {
            throw new ErrException(BaseErr::REQUEST_TOO_FREQUENT);
        }

        $order = OrderService::getInstance()->getOrderByUri($uri);
        if (empty($order)) {
            throw new ErrException(OrderErr::ORDER_DOES_NOT_EXIST);
        }

        if ($order->state != OrderConst::ORDER_STATE_WAIT_IDENTIFY) {
            throw new ErrException(OrderErr::ORDER_STATE_ABNORMAL);
        }

        $inputParams = $this->checkInputParams($order, $params);
        Log::info("order", 'inputParams', $inputParams);
        $fieldList = [];
        OrderItemFieldModel::query()->where('order_id', $order->id)
            ->where('biz_type', TemplateConst::BIZ_TYPE_OUTPUT)
            ->delete();
        foreach ($inputParams as $input) {
            if (in_array($input['fieldKey'], ['identResult', 'identTruth'])) {
                continue;
            }
            $fieldList[] = [
                'order_id' => $order->id,
                'order_item_id' => $input['orderItemId'],
                'biz_type' => OrderConst::BIZ_TYPE_OUTPUT,
                'field_id' => $input['fieldId'],
                'field_name' => $input['fieldName'],
                'field_key' => $input['fieldKey'],
                'field_value' => is_array($input['fieldValue']) ? wpt_json_encode($input['fieldValue']) : $input['fieldValue'],
                'field_type' => $input['fieldType'],
                'output_type' => $input['outputType'],
                'create_time' => time(),
            ];
        }

        OrderItemFieldModel::query()->insert($fieldList);
        $detailJson = json_decode($order->detail_json, true);
        $detailJson['newSubmit'] = 1;
        // 更新状态
        $saveOrderData = [
            'ident_result' => $params['identResult'] ?? '',
            'ident_time' => time(),
            'state' => OrderConst::ORDER_STATE_COMPLETE,
            'detail_json' => wpt_json_encode($detailJson),
        ];
        // 结果
        if (isset($params['identTruth']) && is_numeric($params['identTruth'])) {
            $saveOrderData['ident_truth'] = $params['identTruth'];
        }

        OrderService::getInstance()->updateById($order->id, $saveOrderData);
        // 结果推送
        AsyncService::syncIdentResultNotify($uri);
        // 结果推送数据集
        Production::push('image-ident-order-complete', ['uri' => $uri, 'businessId' => $order->business_id, 'env' => env('ENV', '')], 0);
        // 进行结算
        OrderCommissionService::getInstance()->orderCommissionSettlement($order->id);
        return [];
    }

    /**
     * 检查参数
     *
     * @param OrderModel $order
     * @param array $params
     * @return array
     * @throws ErrException
     */
    private function checkInputParams(OrderModel $order, array $params)
    {
        $inputParams = [];
        $templateParams = OrderService::getInstance()->getOrderSubmitParams($order);
        $orderItems = OrderItemModel::query()->where('order_id', $order->id)->get();
        foreach ($orderItems as $itemKey => $orderItem) {
            foreach ($templateParams as $field) {
                if ($field['outputType'] == OrderConst::OUTPUT_TYPE_ORDER_ITEM) {
                    $field['fieldValue'] = $params['items'][$itemKey][$field['fieldKey']] ?? null;
                    $field['orderItemId'] = $orderItem->id;
                    $field['fieldValue'] = OrderSubmitService::getInstance()->switchCheck($field);
                    $inputParams[] = $field;
                }
            }
        }

        foreach ($templateParams as $field) {
            if ($field['outputType'] == OrderConst::OUTPUT_TYPE_ORDER) {
                $field['fieldValue'] = $params[$field['fieldKey']] ?? null;
                $field['orderItemId'] = 0;
                $field['fieldValue'] = OrderSubmitService::getInstance()->switchCheck($field);
                $inputParams[] = $field;
            }
        }

        return $inputParams;
    }

    /**
     * 格式化列表
     *
     * @param $list
     * @return array
     */
    private function formatList($list)
    {
        if (empty($list)) {
            return [];
        }
        $listArr = $list->toArray();
        $businessIds = array_unique(array_column($listArr, 'business_id'));
        $businessList = BusinessService::getInstance()->getListByIds($businessIds);
        $businessList = array_column($businessList->toArray(), null, 'id');

        // 获取分类信息
        $resultList = [];
        /** @var OrderModel $item */
        foreach ($list as $item) {
            $business = $businessList[$item->business_id] ?? [];
            $category = BusinessCategoryService::getInstance()->getCategoryByIdentifier($item->category_identifier, $item->business_id);
            $resultList[] = [
                'uri' => $item->uri,
                'state' => $item->state,
                'businessId' => $item->business_id,
                'businessName' => $business['name'] ?? '',
                'businessNo' => $item->business_no,
                'businessMasterNo' => $item->business_master_no,
                'categoryIdentifier' => $item->category_identifier,
                'categoryId' => $item->category_id,
                'categoryName' => $category['category_name'] ?? '',
                'cover' => $item->cover,
                'endTime' => $item->end_time,
                'inputTemplateId' => $item->input_template_id,
                'outputTemplateId' => $item->output_template_id,
                'appraiserId' => $item->userinfo_id,
                'acceptTime' => date('Y-m-d H:i:s', $item->accept_time),
                'createTime' => date('Y-m-d H:i:s', $item->create_time),
            ];
        }
        return $resultList;
    }

    /**
     * 构建列表条件
     *
     * @param array $params
     * @return OrderModel|\Illuminate\Database\Eloquent\Builder
     */
    private function buildListWhere(array $params)
    {
        $query = OrderModel::query()->where('is_deleted', 0);

        if (!empty($params['userinfoId'])) {
            $query->where('userinfo_id', $params['userinfoId']);
        }
        if (!empty($params['businessId'])) {
            $query->where('business_id', $params['businessId']);
        } else {
            $businessIds = $params['businessIds'] ?? [];
            if ($businessIds) {
                $query->whereIn('business_id', $businessIds);
            }
        }

        if (!empty($params['state'])) {
            $query->where('state', $params['state']);
        }
        if (isset($params['identTruth']) && is_numeric($params['identTruth'])) {
            $query->where('ident_truth', $params['identTruth']);
        }
        return $query;
    }

    /**
     * 参数初始化
     *
     * @param OrderParams $params
     * @return OrderParams
     * @throws ErrException
     */
    private function makeCreateParams(OrderParams $params)
    {
        if (empty($params->businessId)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }
        if (empty($params->categoryIdentifier)) {
            throw new ErrException(OrderErr::CATEGORY_NOT_CONFIGURED_CORRECTLY);
        }

        if (empty($params->items)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }

        // 分配鉴定师傅
        if (empty($params->appraiserId)) {
            $where = [
                ["field_key" => "businessId", "field_val" => $params->businessId],
                ["field_key" => "category", "field_val" => $params->categoryIdentifier],
            ];
            // 海外图文派单
            if ($params->businessId == BusinessConst::BUSINESS_TYPE_IMAGE_OVERSEAS) {
                $params->appraiserId = $this->dispatchAppraiserOversea($where);
            }
            // TODO
            if ($params->businessId == BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE) {
                $params->appraiserId = 100320642;
            }
        }
        if (empty($params->appraiserId)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_DISPATCH);
        }

        // 获取类目ID
        $category = BusinessCategoryService::getInstance()->getCategoryByIdentifier($params->categoryIdentifier, $params->businessId);
        if (empty($category)) {
            throw new ErrException(OrderErr::CATEGORY_NOT_CONFIGURED_CORRECTLY);
        }
        $params->categoryId = $category->id;

        if (empty($params->inputTemplateId)) {
            $params->inputTemplateId = $category->input_template_id;
        }

        if (empty($params->outputTemplateId)) {
            $params->outputTemplateId = $category->output_template_id;
        }

        $template = TemplateService::getInstance()->getTemplateDetail($params->inputTemplateId);
        if (empty($template) || $template['isDeleted'] || $template['bizType'] != TemplateConst::BIZ_TYPE_INPUT) {
            throw new ErrException(TemplateErr::TEMPLATE_NOT_EXISTS);
        }
        // 检查模板
        $outputTemplate = TemplateService::getInstance()->getTemplateById($params->outputTemplateId);
        if (empty($outputTemplate) || $outputTemplate->is_deleted || $outputTemplate->biz_type != TemplateConst::BIZ_TYPE_OUTPUT) {
            throw new ErrException(TemplateErr::TEMPLATE_NOT_EXISTS);
        }

        $inputFieldList = $template['fields'];
        $itemList = [];
        foreach ($params->items as $item) {
            $itemInput = [];
            if (empty($item['imgs']) || !is_array($item['imgs'])) {
                throw new ErrException(OrderErr::ORDER_ITEM_IMG_ERR);
            }
            if (!empty($item['video']) && !is_array($item['video'])) {
                throw new ErrException(OrderErr::ORDER_ITEM_VIDEO_ERR);
            }
            $itemInput['imgs'] = $item['imgs'];
            $itemInput['video'] = $item['video'] ?? '';
            $itemInput['remark'] = $item['remark'] ?? '';
            if (strlen($itemInput['remark']) > 500) {
                throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "remark不能超过500字符");
            }
            if ($itemInput['video']) {
                $videoJson = wpt_json_encode($itemInput['video']);
                if (strlen($videoJson) > 800) {
                    throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "video不能超过800字符");
                }

                foreach ($itemInput['video'] as $video) {
                    if (empty($video['videoUrl'])) {
                        throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "videoUrl不能为空");
                    }
                }
            }
            // 检查长度
            $imgsJson = wpt_json_encode($item['imgs']);
            if (strlen($imgsJson) > 1500) {
                throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "imgs不能超过1500字符");
            }

            $itemInput['fieldList'] = [];
            foreach ($inputFieldList as $field) {
                $field['fieldValue'] = $item[$field['fieldKey']] ?? '';
                if (!in_array($field['fieldKey'], ['imgs', 'video', 'remark']) && $field['outputType'] == OrderConst::OUTPUT_TYPE_ORDER_ITEM) {
                    $fieldValue = $field['fieldValue'];
                    if (is_array($fieldValue)) {
                        $fieldValue = wpt_json_encode($fieldValue);
                    }
                    if (strlen($fieldValue) > 1000) {
                        throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, $field['fieldTitle'] . "不能超过500字符");
                    }
                    $itemInput['fieldList'][] = $field;
                }
            }

            $itemList[] = $itemInput;
        }
        $params->itemList = $itemList;
        // 处理订单入参
        foreach ($inputFieldList as $field) {
            if ($field['outputType'] == OrderConst::OUTPUT_TYPE_ORDER) {
                $field['fieldValue'] = $params->order[$field['fieldKey']] ?? '';

                $fieldValue = $field['fieldValue'];
                if (is_array($fieldValue)) {
                    $fieldValue = wpt_json_encode($fieldValue);
                }
                if (strlen($fieldValue) > 1000) {
                    throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, $field['fieldTitle'] . "不能超过500字符");
                }

                $params->orderItem[] = $field;
            }
        }
        return $params;
    }

    private function dispatchAppraiserOversea($where)
    {
        $where[] = ["field_key" => "isExpert", "field_val" => 0];   // 非专家
        $result = AppraiserDispatchLogic::getInstance()->list($where);
        $appraiserList = $result['list'] ?? [];
        // 走兜底
        if (empty($appraiserList)) {
            Log::info(LogConst::IMAGE_IDENT_SERVER, '没有可以派单鉴定师走兜底', ['params' => $where]);

            $envKey = is_test() ? 'dev' : 'prod';
            $defaultAppraiser = AppraiserConst::OVER_SEA_DEFAULT_APPRAISER[$envKey] ?? [];;
            return $defaultAppraiser[array_rand($defaultAppraiser)] ?? AppraiserConst::OVER_SEA_DEFAULT_UID;
        }

        $allUserinfoIds = array_column($appraiserList, 'userinfoId');
        // 存在鉴定师，循环派单
        $hasDispatchUserinfoIds = AppraiserCache::getOverseaDispatchAppraiser();
        $noDispatchUserinfoIds = array_diff($allUserinfoIds, $hasDispatchUserinfoIds);
        if (!empty($noDispatchUserinfoIds)) {
            $dispatchUserinfoId = $noDispatchUserinfoIds[array_rand($noDispatchUserinfoIds)];
        } else {
            $dispatchUserinfoId = $allUserinfoIds[array_rand($allUserinfoIds)];
            AppraiserCache::delOverseaDispatchAppraiser();
        }
        AppraiserCache::addOverseaDispatchAppraiser($dispatchUserinfoId);

        Log::info(LogConst::IMAGE_IDENT_SERVER, '海外图文鉴定老师派单成功', ['userinfoId' => $dispatchUserinfoId, 'params' => $where]);
        return $dispatchUserinfoId;
    }
}
