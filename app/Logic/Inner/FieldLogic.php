<?php


namespace App\Logic\Inner;

use App\Constants\CommonConst;
use App\ErrCode\FieldErr;
use App\Exceptions\ErrException;
use App\Models\BusinessFieldModel;
use App\Service\BusinessService;
use App\Service\FieldService;
use App\Utils\Singleton;

/**
 * 字典(字段)
 *
 * Class FieldLogic
 * @package App\Logic\Inner
 */
class FieldLogic
{
    use Singleton;

    /**
     * 字典列表
     *
     * @param array $params
     * @return array
     */
    public function list(array $params)
    {
        $page = max(intval($params['page'] ?? 1), 1);
        $pageSize = intval($params['pageSize'] ?? 20);
        $query = $this->buildListWhere($params);
        $list = $query->forPage($page, $pageSize)->get();

        return [
            'page' => $page + 1,
            'pageSize' => $pageSize,
            'list' => $this->formatList($list),
            'isEnd' => count($list) < $pageSize
        ];
    }

    /**
     * 创建字典
     *
     * @param array $params
     * @return array
     * @throws ErrException
     */
    public function create(array $params)
    {
        $businessId = $params['businessId'];
        $fieldKey = $params['fieldKey'];
        $bizType = $params['bizType'];
        $field = BusinessFieldModel::query()
            ->where('is_deleted', 0)
            ->where('business_id', $businessId)
            ->where('field_key', $fieldKey)
            ->where('biz_type', $bizType)
            ->first();
        if ($field) {
            throw new ErrException(FieldErr::FIELD_EXISTS);
        }

        $fieldData = [
            'business_id' => $businessId,
            'name' => $params['name'],
            'field_type' => $params['fieldType'],
            'field_key' => $fieldKey,
            'placeholder' => $params['placeholder'] ?? '',
            'max_length' => $params['maxLength'] ?? 0,
            'biz_type' => $bizType,
            'is_required' => $params['isRequired'] ?? 0,
            'create_time' => time(),
        ];
        $fieldOptionList = [];
        foreach ($params['optionsList'] ?? [] as $item) {
            if (!empty($item['optionName'])) {
                $fieldOptionList[] = [
                    'option_name' => $item['optionName'],
                    'option_img' => $item['optionImg'] ?? '',
                ];
            }
        }
        $fieldId = FieldService::getInstance()->create($fieldData, $fieldOptionList);
        return [
            'id' => $fieldId
        ];
    }

    /**
     * 编辑字典
     *
     * @param array $params
     * @return array
     * @throws ErrException
     */
    public function edit(array $params)
    {
        $id = $params['id'];
        $field = FieldService::getInstance()->getFieldById($id);
        if (empty($field)) {
            throw new ErrException(FieldErr::FIELD_NOT_EXISTS);
        }

        $saveData = [
            'name' => $params['name'],
            'placeholder' => $params['placeholder'] ?? '',
            'max_length' => $params['maxLength'] ?? 0,
            'is_required' => $params['isRequired'] ?? 0,
        ];
        FieldService::getInstance()->updateFieldById($id, $saveData);
        return [];
    }

    /**
     * 删除
     *
     * @param int $id
     * @return array
     * @throws ErrException
     */
    public function deleted(int $id)
    {
        $field = FieldService::getInstance()->getFieldById($id);
        if (empty($field)) {
            throw new ErrException(FieldErr::FIELD_NOT_EXISTS);
        }

        $data = [
            'is_deleted' => CommonConst::DELETED
        ];
        FieldService::getInstance()->updateFieldById($id, $data);

        return [];
    }

    /**
     * 启用/禁用
     *
     * @param int $id
     * @param int $state
     * @return array
     * @throws ErrException
     */
    public function enable(int $id, int $state)
    {
        $field = FieldService::getInstance()->getFieldById($id);
        if (empty($field)) {
            throw new ErrException(FieldErr::FIELD_NOT_EXISTS);
        }

        $data = [
            'state' => $state
        ];
        FieldService::getInstance()->updateFieldById($id, $data);

        return [];
    }

    /**
     * 返回列表
     *
     * @param $list
     * @return array
     */
    private function formatList($list)
    {
        if (empty($list)) {
            return [];
        }
        $businessIds = array_column($list->toArray(), 'business_id');
        $businessList = BusinessService::getInstance()->getListByIds($businessIds);
        $businessList = array_column($businessList->toArray(), null, 'id');
        $resultList = [];
        /** @var BusinessFieldModel $item */
        foreach ($list as $item) {
            $business = $businessList[$item->business_id] ?? [];
            $resultList[] = [
                'id' => $item->id,
                'businessId' => $item->business_id,
                'businessName' => $business['name'] ?? '',
                'fieldType' => $item->field_type,
                'fieldKey' => $item->field_key,
                'name' => $item->name,
                'placeholder' => $item->placeholder,
                'maxLength' => $item->max_length,
                'isRequired' => $item->is_required,
                'bizType' => $item->biz_type,
                'state' => $item->state,
                'createTime' => date('Y-m-d H:i:s', $item->create_time)
            ];
        }
        return $resultList;
    }

    /**
     * 构建条件
     *
     * @param array $params
     * @return BusinessFieldModel|\Illuminate\Database\Eloquent\Builder
     */
    private function buildListWhere(array $params)
    {
        $query = BusinessFieldModel::query()->where('is_deleted', 0);
        if (!empty($params['businessId'])) {
            $query->where('business_id', $params['businessId']);
        }

        if (isset($params['state']) && is_numeric($params['state'])) {
            $query->where('state', $params['state']);
        }

        if (!empty($params['bizType'])) {
            $query->where('biz_type', $params['bizType']);
        }
        return $query;
    }
}
