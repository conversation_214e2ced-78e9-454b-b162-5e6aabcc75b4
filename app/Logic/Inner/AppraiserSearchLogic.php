<?php

namespace App\Logic\Inner;

use App\Logic\AppraiserBusinessLogic;
use App\Service\AppraiserBusinessService;
use App\Service\AppraiserService;
use App\Utils\Singleton;

class AppraiserSearchLogic
{
    use Singleton;

    public function detailByUserinfoId($userinfoId)
    {
        // 鉴定师基本信息, 兼容删除状态
        $baseInfo = AppraiserService::getInstance()->detail($userinfoId, false);

        // 鉴定师业务类目信息
        $businessCategoryInfo = AppraiserBusinessLogic::getInstance()->detail($userinfoId);

        return [
            'baseInfo' => $baseInfo,
            'businessInfo' => $businessCategoryInfo,
        ];
    }

    public function listByUserinfoIds($userinfoIds)
    {
        $userinfoIds = array_filter($userinfoIds);
        $baseList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIds);
        if (empty($baseList)) {
            return [];
        }

        $baseList = array_column($baseList, null, 'userinfoId');
        $userinfoIds = array_column($baseList, 'userinfoId');

        $businessCategoryInfoList = [];
        foreach ($userinfoIds as $userinfoId) {
            $businessCategoryInfoList[$userinfoId] = AppraiserBusinessLogic::getInstance()->detail($userinfoId);
        }

        $result = [];
        foreach ($userinfoIds as $userinfoId) {
            $result[] = [
                'baseInfo' => $baseList[$userinfoId] ?? [],
                'businessInfo' => $businessCategoryInfoList[$userinfoId]?? [],
            ];
        }
        return $result;
    }


    public function simpleListByUserinfoIds($userinfoIds)
    {
        $userinfoIds = array_unique(array_filter($userinfoIds));
        $appraiserInfoList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIds, ['userinfo_id as userinfoId', 'rest_start_time as restStartTime', 'rest_end_time as restEndTime']);
        if (empty($appraiserInfoList)) {
            return [];
        }

        // 所有userinfoId
        $userinfoIds = array_column($appraiserInfoList, 'userinfoId');
        $userinfoListChunk = array_chunk($userinfoIds, 20);

        $businessInfoMap = [];
        foreach ($userinfoListChunk as $userinfoList) {
            $appraiserBusinessMap = AppraiserBusinessLogic::getInstance()->batchDetail($userinfoList);
            //dd($appraiserBusinessMap);
            foreach ($appraiserBusinessMap as $userinfoId => $businessInfo) {
                $businessInfoMap[$userinfoId] = $businessInfo;
            }
            //dd($businessInfoMap);
        }

        // 拼接业务权限数据
        foreach ($appraiserInfoList as &$appraiserInfo) {
            $appraiserInfo['businessInfo'] = $businessInfoMap[$appraiserInfo['userinfoId']] ?? [];
        }

        return [
            'list' => $appraiserInfoList
        ];
    }

}