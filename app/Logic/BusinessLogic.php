<?php

namespace App\Logic;

use App\ErrCode\BusinessErr;
use App\Exceptions\ErrException;
use App\Service\BusinessService;
use App\Utils\Singleton;

class BusinessLogic
{
    use Singleton;

    /**
     * 添加
     *
     * @param $name
     * @param $notifyUrl
     * @return bool
     * @throws ErrException
     */
    public function add($name, $notifyUrl)
    {
        // 是否重名
        $business = BusinessService::getInstance()->getOneByName($name);
        if (!empty($business)) {
            throw new ErrException(BusinessErr::BUSINESS_EXIST);
        }

        return BusinessService::getInstance()->add($name, $notifyUrl);
    }

    public function list($params)
    {
        $page = $params['page'];
        $pageSize = $params['pageSize'];

        return BusinessService::getInstance()->list($params, $page, $pageSize);
    }

    /**
     * 编辑
     *
     * @param $id
     * @param $params
     * @return int
     * @throws ErrException
     */
    public function edit($id, $params)
    {
        // 是否存在
        $business = BusinessService::getInstance()->getOneById($id);
        if (empty($business)) {
            throw new ErrException(BusinessErr::BUSINESS_NO_EXIST);
        }

        return BusinessService::getInstance()->updateById($id, $params);
    }

    /**
     * 启用/禁用
     *
     * @param $id
     * @param $state
     * @return int
     * @throws ErrException
     */
    public function enable($id, $state)
    {
        // 是否存在
        $business = BusinessService::getInstance()->getOneById($id);
        if (empty($business)) {
            throw new ErrException(BusinessErr::BUSINESS_NO_EXIST);
        }

        $params = ['state' => $state];
        return BusinessService::getInstance()->updateById($id, $params);
    }


}
