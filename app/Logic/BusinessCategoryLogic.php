<?php

namespace App\Logic;

use App\ErrCode\BusinessErr;
use App\Exceptions\ErrException;
use App\Service\BusinessCategoryService;
use App\Service\BusinessService;
use App\Utils\Singleton;

class BusinessCategoryLogic
{
    use Singleton;

    public function add($params)
    {
        $businessId = $params['businessId'];
        $categoryName = $params['categoryName'];
        $categoryIdentifier = $params['categoryIdentifier'];

        // 对应业务验证
        $business = BusinessService::getInstance()->getOneById($businessId);
        if (empty($business)) {
            throw new ErrException(BusinessErr::BUSINESS_NO_EXIST);
        }

        // 类目名称、类目自定义标记 重名验证
        $list = BusinessCategoryService::getInstance()->getListByBusinessId($businessId);
        $categoryNameList = array_column($list, 'categoryName');
        $categoryIdentifierList = array_column($list, 'categoryIdentifier');
        if (in_array($categoryName, $categoryNameList)) {
            throw new ErrException(BusinessErr::BUSINESS_CATEGORY_EXIST);
        }
        if (in_array($categoryIdentifier, $categoryIdentifierList)) {
            throw new ErrException(BusinessErr::BUSINESS_CATEGORY_IDENT_EXIST);
        }

        return BusinessCategoryService::getInstance()->add($params);
    }

    public function listByBusiness($params)
    {
        $businessId = $params['businessId'];
        // 对应业务验证
        $business = BusinessService::getInstance()->getOneById($businessId);
        if (empty($business)) {
            throw new ErrException(BusinessErr::BUSINESS_NO_EXIST);
        }

        $list = BusinessCategoryService::getInstance()->getListByBusinessId($businessId);
        return [
            'list' => $list,
        ];
    }

    public function list()
    {
        $list = BusinessCategoryService::getInstance()->getList();

        $businessList = BusinessService::getInstance()->getAll();
        $businessNameMap = array_column($businessList, 'name', 'id');

        $result = array_map(function ($item) use ($businessNameMap)  {
            unset($item['id']);
            unset($item['createTime']);
            $item['categoryIdentifier'] = (int)$item['categoryIdentifier'];
            $item['businessName'] = $businessNameMap[$item['businessId']] ?? '';
            return $item;
        }, $list);

        return [
            'list' => $result,
            'config' => [   // 其他下发配置
                'reviewType' => [   // 小法庭 - 审验类型
                    ['value' => 1, 'label' => '鉴定机构'],
                    ['value' => 2, 'label' => '拍卖行'],
                    ['value' => 3, 'label' => '鉴定师'],
                    ['value' => 4, 'label' => '名家'],
                ],
            ],
        ];
    }


    public function edit($params)
    {
        $id = $params['id'];
        $businessCategory = BusinessCategoryService::getInstance()->getOneById($id);
        if (empty($businessCategory)) {
            throw new ErrException(BusinessErr::BUSINESS_ITEM_NO_EXIST);
        }
        // dd($businessCategory);
        $businessId = $businessCategory['business_id'];
        $list = BusinessCategoryService::getInstance()->getListByBusinessId($businessId);

        // 去除当前类目
        $categoryNameList = array_diff(array_column($list, 'categoryName'), [$businessCategory['category_name']]);
        $categoryIdentifierList = array_diff(array_column($list, 'categoryIdentifier'), [$businessCategory['category_identifier']]);
        //dd($categoryNameList, $categoryIdentifierList);
        $categoryName = $params['categoryName'];
        $categoryIdentifier = $params['categoryIdentifier'];
        if (in_array($categoryName, $categoryNameList)) {
            throw new ErrException(BusinessErr::BUSINESS_CATEGORY_EXIST);
        }
        if (in_array($categoryIdentifier, $categoryIdentifierList)) {
            throw new ErrException(BusinessErr::BUSINESS_CATEGORY_IDENT_EXIST);
        }

        unset($params['id']);
        return BusinessCategoryService::getInstance()->editById($id, $params);
    }


}
