<?php

namespace App\Logic;

use App\Constants\AppraiserConst;
use App\Constants\BusinessConst;
use App\Constants\CommonConst;
use App\ErrCode\AppraiserErr;
use App\ErrCode\BusinessErr;
use App\Exceptions\ErrException;
use App\Models\AppraiserGroupCategoryRelationModel;
use App\Models\AppraiserGroupMembersRelationModel;
use App\Models\AppraiserGroupModel;
use App\Models\AppraiserModel;
use App\Service\AppraiserGroupCategoryService;
use App\Service\AppraiserGroupMemberService;
use App\Service\AppraiserGroupScheduleService;
use App\Service\AppraiserGroupService;
use App\Service\AppraiserService;
use App\Service\BusinessCategoryService;
use App\Utils\CommonUtil;
use App\Utils\Singleton;
use Illuminate\Support\Facades\DB;


class AppraiserGroupLogic
{
    use Singleton;


    public function selectInfo($userinfoId)
    {
        // 所有鉴定师
        // $allAppraiser =  \App\Service\Community\AppraiserService::getInstance()->getAllCommunityAppraiser();
        $allAppraiser = AppraiserService::getInstance()->getAll();
        $allAppraiserList = array_map(function (&$item) {
            return [
                'userinfoId' => $item['userinfoId'],
                'nickname' => $item['nickname'],
            ];
        }, $allAppraiser);

        // 非当前组长的所有组员
        $groupMemberList = AppraiserGroupMembersRelationModel::query()
            ->where('is_deleted', 0)
            ->where('userinfo_id', '!=', $userinfoId)
            ->get()
            ->toArray();
        //dd($groupMemberList);
        $selectedAppraiserList = array_column($groupMemberList, 'member_uid');
        //dd($groupMemberUserinfoIds);

        // 非当前组长类目的所有类目
        $categoryList = AppraiserGroupCategoryRelationModel::query()
            ->where('is_deleted', 0)
            ->where('business_id', BusinessConst::BUSINESS_TYPE_CONSIGN_VERIFY)
            ->where('userinfo_id', '!=', $userinfoId)
            ->get()
            ->toArray();
        $selectedCategoryList = array_column($categoryList, 'category_id');

        return [
            'allAppraiserList' => $allAppraiserList,
            'selectedAppraiserList' => array_unique($selectedAppraiserList),
            'selectedCategoryList' => array_unique($selectedCategoryList),
        ];

    }

    public function add($params)
    {
        $userinfoId = $params['userinfoId'];
        $categoryIds = $params['categoryIds'];
        $memberUids = $params['memberUids'];

        // 检查组长
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }
        // 检查组员
        $memberList = AppraiserService::getInstance()->listByUserinfoIds($memberUids);
        if (count($memberList) != count($memberUids)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }


        $businessCategoryList = BusinessCategoryService::getInstance()->getList();
        $businessCategoryMap = [];
        foreach ($businessCategoryList as $item) {
            $businessCategoryMap[$item['businessId']][] = $item['categoryIdentifier'];
        }
        // 检查业务和分类是否存在
        foreach ($categoryIds as $businessId => $categoryIdList) {
            foreach ($categoryIdList as $categoryId) {
                if (!in_array($categoryId, $businessCategoryMap[$businessId] ?? [])) {
                    throw new ErrException(BusinessErr::BUSINESS_CATEGORY_NO_EXIST);
                }
            }
        }

        // 分组处理
        AppraiserGroupService::getInstance()->delByUserinfoId($userinfoId);
        AppraiserGroupService::getInstance()->add(['userinfoId' => $userinfoId]);

        // 关联分类处理
        AppraiserGroupCategoryService::getInstance()->delByUserinfoId($userinfoId);
        AppraiserGroupCategoryService::getInstance()->add($userinfoId, $categoryIds);

        // 关联成员处理
        AppraiserGroupMemberService::getInstance()->delByUserinfoId($userinfoId);
        AppraiserGroupMemberService::getInstance()->add($userinfoId, $memberUids);

        // 组员排班处理
        $scheduleList = AppraiserGroupScheduleService::getInstance()->getListByUserinfoId($userinfoId);
        $memberUidList = array_unique(array_column($scheduleList, 'member_uid'));
        $delUidList = array_diff($memberUidList, $memberUids);
        AppraiserGroupScheduleService::getInstance()->delByUserinfoIdAndMemberUids($userinfoId, $delUidList);

        // 更新组长身份
        AppraiserModel::query()->where('userinfo_id', $userinfoId)->update(['is_leader' => AppraiserConst::IS_LEADER]);

        return true;
    }

    public function detail($userinfoId)
    {
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }

        //是否是组长
        $isLeader = AppraiserGroupService::getInstance()->isLeader($userinfoId);

        // 组员列表
        $groupMember = AppraiserGroupMemberService::getInstance()->getListByUserinfoId($userinfoId);
        $memberUserinfoIdList = array_column($groupMember, 'member_uid');
        //dd($memberUserinfoIdList);
        //$memberList = \App\Service\Community\AppraiserService::getInstance()->getListByUserinfoIds($memberUserinfoIdList);
        $memberList = AppraiserService::getInstance()->listByUserinfoIds($memberUserinfoIdList);
        $memberList = array_map(function ($item) {
            return [
                'userinfoId' => $item['userinfoId'],
                'nickname' => $item['nickname'],
            ];
        }, $memberList);

        // 类目列表
        $categoryList = AppraiserGroupCategoryService::getInstance()->getListByUserinfoId($userinfoId);
        $businessCategoryList = BusinessCategoryService::getInstance()->getList();

        $businessCategoryMap = [];
        foreach ($businessCategoryList as $businessCategory) {
            $businessCategoryMap[$businessCategory['businessId']][$businessCategory['categoryIdentifier']] = $businessCategory['categoryName'];
        }

        $groupCategoryList = [];
        foreach ($categoryList as &$category) {
            $groupCategoryList[$category['business_id']][] = [
                'categoryId' => $category['category_id'],
                'categoryName' =>$businessCategoryMap[$category['business_id']][$category['category_id']],
            ];
        }

        return [
            'isLeader' => $isLeader,
            'groupName' => $appraiser['nickname'],
            'memberList' => $memberList,
            'categoryList' => $groupCategoryList,
        ];
    }


    public function close($userinfoId)
    {
        $appraiser = AppraiserService::getInstance()->detail($userinfoId, false); // 通过binlog更新了删除，这里查询需要注意
        if (empty($appraiser)) {
            throw new ErrException(AppraiserErr::APPRAISER_NO_EXIST);
        }

        $appraiserGroup = AppraiserGroupService::getInstance()->getByUserinfoId($userinfoId);
        if (empty($appraiserGroup)) {
            throw new ErrException(AppraiserErr::GROUP_LEADER_NO_EXIST);
        }

        try {
            DB::connection('image_ident_server')->beginTransaction();

            // 关闭组长相关
            AppraiserGroupService::getInstance()->delByUserinfoId($userinfoId);
            AppraiserGroupCategoryService::getInstance()->delByUserinfoId($userinfoId);
            AppraiserGroupMemberService::getInstance()->delByUserinfoId($userinfoId);
            AppraiserGroupScheduleService::getInstance()->delByUserinfoId($userinfoId);

            // 更新是否是组长
            AppraiserModel::query()->where('userinfo_id', $userinfoId)->update(['is_leader' => AppraiserConst::IS_NOT_LEADER]);

            DB::connection('image_ident_server')->commit();
            return true;

        } catch (\Exception $e) {
            DB::connection('image_ident_server')->rollBack();
            throw new ErrException([$e->getcode(), $e->getMessage()]);
        }
    }

    /**
     * 是否组长
     *
     * @param $userinfoId
     * @return array
     */
    public function isLeader($userinfoId): array
    {
        $isLeader = AppraiserGroupService::getInstance()->isLeader($userinfoId);
        return [
            'isLeader' => $isLeader,
        ];
    }

    /**
     * 获取所在组的所有人员（组长+组员）
     *
     * @param $userinfoId
     * @return array
     */
    public function memberList($userinfoId)
    {
        $isLeader = AppraiserGroupService::getInstance()->isLeader($userinfoId);
        $groupUserinfoId = $userinfoId;
        // 是组员，取组长id
        if (!$isLeader) {
            $groupMember = AppraiserGroupMemberService::getInstance()->getOneByMemberUid($userinfoId);
            $groupUserinfoId = $groupMember['userinfo_id'] ?? 0;
            // 不是组员，直接返回
            if (empty($groupUserinfoId)) {
                return ['list' => [$userinfoId]];
            }
        }

        // 属于组长或者是组员
        // 获取组长下组员
        $memberList = AppraiserGroupMemberService::getInstance()->getListByUserinfoId($groupUserinfoId);
        $memberUidList = array_column($memberList, 'member_uid');

        // 拼接上组长
        $memberUidList[] = $groupUserinfoId;
        return [
            'list' => $memberUidList
        ];
    }

    public function getGroupUserinfoId($userinfoId)
    {
        $isLeader = AppraiserGroupService::getInstance()->isLeader($userinfoId);
        if ($isLeader) {
            return $userinfoId;
        }

        $groupMember = AppraiserGroupMemberService::getInstance()->getOneByMemberUid($userinfoId);
        // 不是组员，返回0
        return $groupMember['userinfo_id'] ?? 0;
    }


    public function scheduleList($params)
    {
        if (empty($params['startTime']) || empty($params['endTime'])) {
            $now = time();
            $params['startTime'] = $now - 86400 * 2;
            $params['endTime'] = $now + 86400 * 8;
        }
        if ($params['endTime'] < $params['startTime']) {
            throw new ErrException(AppraiserErr::GROUP_SCHEDULE_TIME_ERR);
        }

        // 所有组员
        $memberList = AppraiserGroupMemberService::getInstance()->getListAll();
        // 所有组长id
        $groupUserinfoIds = array_unique(array_column($memberList,'userinfo_id'));
        //dd($groupUserinfoIds);
        $appraiserList = [];
        foreach ($groupUserinfoIds as $groupUserinfoId) {
            $appraiserList[] = [
                'userinfoId' => $groupUserinfoId,
                'memberUid' => $groupUserinfoId,
                'isGroup' => 1, // 组长
            ];
            foreach ($memberList as $member) {
                if ($member['userinfo_id'] == $groupUserinfoId) {
                    $appraiserList[] = [
                        'userinfoId' => $groupUserinfoId,
                        'memberUid' => $member['member_uid'],
                        'isGroup' => 0, // 组员
                    ];
                }
            }
        }
        //dd($appraiserList);
        $userinfoIds = array_column($appraiserList, 'memberUid');
        $appraiserInfoList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIds);
        $appraiserInfoList = array_column($appraiserInfoList,null,'userinfoId');

        $schedulingList = AppraiserGroupScheduleService::getInstance()->getListMap($params['startTime'], $params['endTime']);
        $dateList = CommonUtil::periodTime($params['startTime'], $params['endTime']);

        $list = [];
        foreach ($appraiserList as $appraiser) {
            // 鉴定师名字
            //dd($appraiser);
            $name = $appraiserInfoList[$appraiser['memberUid']]['nickname'] ?? '';
            $isGroup = $appraiser['isGroup'];
            $groupName = '';
            if ($isGroup) {
                $groupName = $name;
                $name = $name . '(组长)';
            }

            $appraiserDateList = [];
            foreach ($dateList as $date) {
                $timestamp = strtotime($date);
                $dayOfWeek = date("N", $timestamp);

                $assignedVolume = 0;
                $key = $appraiser['userinfoId'] . '_' . $appraiser['memberUid'] . '_' . $date;
                if (!empty($schedulingList[$key])) {
                    $assignedVolume = $schedulingList[$key]['assignedVolume'];
                }

                $appraiserDateList[] = [
                    "assignmentDate" => $date,//   日期
                    "assignedVolume" => $assignedVolume,    //   分配量
                    "dayOfWeek" => $dayOfWeek,    //   周几
                ];
            }

            $list[] = [
                "isGroup" => $isGroup,   //   是否组长
                "userinfoId" => $appraiser['userinfoId'],   //   组长id
                "groupName" => $groupName, //  小组名称
                "memberName" => $name, //  组员名称
                "memberUid" => $appraiser['memberUid'],   //   组员id
                "date" => $appraiserDateList,
            ];

        }

        return [
            'list' => $list
        ];
    }

    public function scheduleEdit($params)
    {
        $userinfoId = $params['userinfoId'];
        $memberUid = $params['memberUid'];
        $assignedVolume = $params['assignedVolume'];
        $assignmentDate = $params['assignmentDate'];

        // 用户身份校验
        $memberList = AppraiserGroupMemberService::getInstance()->getListByUserinfoId($userinfoId);
        $memberUids = array_column($memberList, 'member_uid');
        if (!in_array($memberUid, $memberUids)) {
            throw new ErrException(AppraiserErr::GROUP_MEMBER_NO_EXIST);
        }

        // 存在更新
        $schedule = AppraiserGroupScheduleService::getInstance()->getOne($userinfoId, $memberUid, $assignmentDate);
        if (!empty($schedule)) {
           return AppraiserGroupScheduleService::getInstance()->editAssignedVolume($userinfoId, $memberUid, $assignmentDate, $assignedVolume);
        }

       return AppraiserGroupScheduleService::getInstance()->add([
           'userinfoId' => $userinfoId,
           'memberUid' => $memberUid,
           'assignmentDate' => $assignmentDate,
           'assignedVolume' => $assignedVolume,
        ]);
    }
}
