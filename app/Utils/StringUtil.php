<?php
/**
 * 字符串处理工具类
 * Created by PhpStorm
 * USER zhangcc
 * Date 2021/7/13   1:56 下午
 */

namespace App\Utils;

use App\Libraries\RedLock;

class StringUtil
{
    /**
     * 取得随机字符串,并上锁60秒
     *
     * @param int $length
     * @param string $type
     * @param bool $isNumeric
     * @return string
     */
    public static function createRandStr($length = 32, $type = '', $isNumeric = false)
    {
        $chars = $isNumeric ? "0123456789" : "abcdefghijklmnopqrstuvwxyz0123456789";
        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        if ($type) {
            $key = 'randstr_' . $type . '_' . $str;
        } else {
            $key = 'randstr_' . $str;
        }
        if (!RedLock::getInstance()->lock($key, 60)) {
            return self::createRandStr($length, $type, $isNumeric);
        }

        return $str;
    }

    /**
     * 取得URI
     *
     * @param int $len
     * @param string $pre
     * @return string
     */
    public static function getUri($len = 6, $pre = '')
    {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        $str = "";
        for ($i = 0; $i < $len; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        $key = $pre . date("ymdHi") . $str;
        if (!RedLock::getInstance()->lock($key, 60)) {
            return self::getUri($len, $pre);
        }
        return $key;
    }

    /**
     * 数组或者数组转字符串
     *
     * @param $val
     * @return array|string
     * <AUTHOR>
     * @date 2021-10-12 3:33 下午
     */
    public static function toStr($val)
    {
        if (is_array($val)) {
            return array_map('strval', $val);
        } else {
            return strval($val);
        }
    }

    /**
     * 下划线转驼峰
     *
     * @param array $array
     * @return mixed|string
     */
    public static function underLineArrTOCamel($array)
    {
        $newObj = [];
        if (!is_array($array) || !$array) {
            return null;
        }
        foreach ($array as $key => $v) {
            $keyTmp = array_reduce(explode('_', $key), function ($v1, $v2) {
                return ucfirst($v1) . ucfirst($v2);
            });
            $keyTmp = lcfirst($keyTmp);
            $newObj[$keyTmp] = $v;
            unset($array[$key]);
        }
        return $newObj;
    }
}
