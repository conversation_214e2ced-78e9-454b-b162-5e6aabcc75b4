<?php

namespace App\Utils;

class CommonUtil
{

    /**
     *  获取指定段时间内的每天的日期
     * @param int $stimestamp 开始日期 时间戳
     * @param int $etimestamp 结束日期 时间戳
     * @return array
     */
    public static function periodTime(int $stimestamp, int $etimestamp): array
    {
        // 计算日期段内有多少天
        $days = ($etimestamp - $stimestamp) / 86400 + 1;

        // 保存每天日期
        $date = array();

        for ($i = 0; $i < $days; $i++) {
            $date[] = date('Y-m-d', $stimestamp + (86400 * $i));
        }

        return $date;
    }
}