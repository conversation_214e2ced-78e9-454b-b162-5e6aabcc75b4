<?php

namespace App\Utils;

use Spin\Logger\Facades\Log;
use <PERSON>henjin\Client\ZhenjinApi;

/**
 * 异步任务投递
 * Class Vibranium
 *
 * @package App\Utils
 */
class Vibranium
{
    /**
     * 异步脚本任务投递
     *
     * @param string $tag           任务标签 k8s-crond-api
     * @param string $taskName     任务名称
     * @param int    $execTime     执行时间戳,毫秒级,0 立刻（支持延迟）
     * @param array  $cmd           运行的命令和参数
     * @param int    $deadlineTime 最后期限时间戳,毫秒级，0 无限制
     * @param array  $extend       扩展信息
     */
    public static function task(
        string $tag,
        string $taskName,
        int $execTime,
        array $cmd,
               $deadlineTime = 0,
               $extend = []
    ) {
        //任务超时时间,毫秒数，0 无限制
        $timeout = (int)get_property($extend, 'timeout', 0);
        //优先级
        $priority = (int)get_property($extend, 'priority', 0);
        //责任人
        $principal = (string)get_property($extend, 'principal', '');
        //来源
        $from = (string)get_property($extend, 'from', '');
        try {
            $taskData = [
                'task_name' => $taskName,
                'tag' => $tag,
                'from' => $from,
                'priority' => $priority,
                'principal' => $principal,
                'exec_time' => $execTime,
                'deadline_time' => $deadlineTime,
                'timeout' => $timeout,
                'cmd' => $cmd
            ];
            $res = ZhenjinApi::getInstance()->openException(false)->zhenjinDeliveryScript($taskData);
            if (!empty($res) && isset($res['code']) && $res['code'] != 0) {
                Log::error("async_script_task_fail_log", "异步任务投递失败", [
                    'params' => func_num_args(),
                    'res' => $res,
                ]);
                return '';
            }
            Log::info('async_script_task_log', '振金投递记录', [
                'params' => func_get_args(),
                'result' => $res,
                'code'   => get_property($res, 'code'),
                'msg'    => get_property($res, 'msg'),
            ]);
            return $res;
        } catch (\Exception $e) {
            Log::error("async_script_task_fail_log", "异步任务投递失败", [
                'Msg'    => $e->getMessage(),
                'Code'   => $e->getCode(),
                'params' => func_get_args(),
            ]);
            return '';
        }
    }

    /**
     * 回调URL任务投递
     *
     * @param string $url           回调 url
     * @param mixed  $content       回调内容
     * @param int    $execTime     执行时间戳,毫秒级,0 立刻 （支持延迟时间）
     * @param string $taskName     任务名称
     * @param string $tag           任务标签 k8s-crond-http
     * @param int    $deadlineTime 最后期限时间戳,毫秒级，0 无限制
     * @param array  $extend       扩展信息
     * @return mixed
     */
    public static function delivery(
        string $url,
               $content,
               $execTime = 0,
               $taskName = 'shop-api-http-callback',
               $tag = 'k8s-crond-http',
               $deadlineTime = 0,
               $extend = []
    ) {
        try {
            //任务超时时间,毫秒数，0 无限制
            $timeout = (int)get_property($extend, 'timeout', 0);
            //优先级
            $priority = (int)get_property($extend, 'priority', 0);
            //责任人
            $principal = (string)get_property($extend, 'principal', '');
            //来源
            $from = (string)get_property($extend, 'from', '');
            //回调内容类型，枚举：form , json ,jsonstr
            $contentType = (string)get_property($extend, 'contentType', '');
            //true为失败后多次尝试
            $clientAck = (bool)get_property($extend, 'clientAck', false);

            if (is_array($content)) {
                $content      = json_encode($content, JSON_UNESCAPED_UNICODE);
                $contentType = 'json';
            }
            $taskData = [
                'task_name' => $taskName,
                'tag' => $tag,
                'from' => $from,
                'priority' => $priority,
                'principal' => $principal,
                'exec_time' => $execTime,
                'deadline_time' => $deadlineTime,
                'timeout' => $timeout,
                'url' => $url,
                'content_type' => $contentType,
                'content' => $content,
                'client_ack' => $clientAck
            ];
            $res = ZhenjinApi::getInstance()->openException(false)->zhenjinDeliveryHttp($taskData);
            if (!empty($res) && isset($res['code']) && $res['code'] != 0) {
                Log::error("async_http_task_fail_log", "异步任务投递失败", [
                    'params' => func_num_args(),
                    'res' => $res,
                ]);
                return '';
            }
            Log::info('async_http_task_log', '振金投递记录', [
                'params' => func_num_args(),
                'result' => $res,
                'code'   => get_property($res, 'code'),
                'msg'    => get_property($res, 'msg'),
            ]);
            return $res;
        } catch (\Exception $e) {
            Log::error("async_http_task_fail_log", "异步任务投递失败", [
                'Msg'    => $e->getMessage(),
                'Code'   => $e->getCode(),
                'params' => func_num_args(),
            ]);
            return '';
        }
    }
}
