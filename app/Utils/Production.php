<?php

namespace App\Utils;

use Diamond\Client\DiamondApi;
use Exception;
use Spin\Logger\Facades\Log;

class Production
{
    public static function push(string $datasetName, array $data, int $delayTime = 0): bool
    {
        try {
            $object = DiamondApi::getInstance()->diamondCustomPush();
            $res = $object->setDatasetName($datasetName)
                ->setData(json_encode($data))
                ->setDelayTime($delayTime)
                ->openException(false)
                ->doRequest();

            if (!empty($res) && isset($res['code']) && $res['code'] != 200) {
                Log::error('production', '推送diamond数据失败', ["args" => func_get_args(), 'res' => $res]);
                return false;
            }
            Log::info('production', '推送diamond数据成功', ['args' => func_get_args(), 'res' => $res]);
            return true;
        } catch (Exception $e) {
            Log::error('production', '推送diamond数据失败:' . $e->getMessage(), func_get_args());
            return false;
        }
    }
}
