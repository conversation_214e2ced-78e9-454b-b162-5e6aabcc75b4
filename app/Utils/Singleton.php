<?php

/**
 * 单例Trait.
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
namespace App\Utils;

trait Singleton
{
    private static $instance = [];

    /**
     * 全局静态单例模式
     *
     * @return static
     */
    public static function getInstance(...$args)
    {
        if (!isset(self::$instance[static::class])) {
            self::$instance[static::class] = new static(...$args);
        }
        return self::$instance[static::class];
    }
}
