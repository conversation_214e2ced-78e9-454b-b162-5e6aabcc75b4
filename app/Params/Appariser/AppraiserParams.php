<?php

namespace App\Params\Appariser;

use App\Params\ParamTransformer;

class AppraiserParams extends ParamTransformer
{
    public int $id = 0;
    public int $userinfoId = 0;
    public string $nickname = '';
    public string $avatar = '';
    public int $workType = 2;
    public int $isLeader = 2;
    public int $isTrainee = 2;
    public string $description = '';
    public string $personalProfile = '';
    public string $certificate = '';
    public string $signaturePicture = '';
    public int $totalCommission = 0;
    public int $totalAmount = 0;
    public int $joinTime = 0;
    public string $affiliationId = '';
    public string $affiliationName = '';
    public int $restStartTime = 0;
    public int $restEndTime = 0;
    public int $state = 1;  // 1启用 0关闭
    public int $createTime = 0;

}