<?php


namespace App\Params\Order;


use App\Params\ParamTransformer;


class OrderParams extends ParamTransformer
{
    // 鉴定师ID
    public int $appraiserId = 0;
    // 业务ID
    public int $businessId = 0;
    // 业务单号
    public string $businessNo = '';
    // 业务母单号
    public string $businessMasterNo = '';
    // 业务类目标识
    public string $categoryIdentifier = '';
    // 打回uri
    public string $rejectUri = '';
    // 业务分类ID
    public int $categoryId = 0;
    // 订单截止时间
    public int $endTime = 0;
    // 输入模板
    public int $inputTemplateId = 0;
    // 输出模板
    public int $outputTemplateId = 0;

    public int $orderAmount = 0;

    public int $subType = 0;
    // 订单商品
    public array $items = [];

    public array $order = [];

    public array $orderItem = [];

    public array $itemList = [];
}
