<?php

namespace App\Params;


use Illuminate\Contracts\Support\Arrayable;

class ParamTransformer implements Arrayable
{
    public function __construct(array $data = [])
    {
        foreach ($data as $key => $value) {
            if (property_exists($this, $key)) {
                $this->$key = $value;
            }
        }

        if (method_exists($this, 'init')) {
            $this->init();
        }
    }

    public function toArray(): array
    {
        return get_object_vars($this);
    }
}
