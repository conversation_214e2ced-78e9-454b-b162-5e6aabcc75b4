<?php


namespace App\Models;

/**
 * 模板字段
 *
 * Class BusinessTemplateFieldModel
 * @package App\Models
 *
 * @property int $id AUTO_INCREMENT PRIMARY KEY
 * @property int $template_id 模板ID
 * @property int $parent_id 父级id
 * @property string $parent_option_name 父级选项名称
 * @property int $field_id 业务字段id
 * @property string $field_key 业务字段key
 * @property int $output_type 输出类型 1订单 2订单物品
 * @property int $state 状态 0禁用 1启用
 * @property int $sort 排序
 * @property int $is_deleted 是否删除 0否 1是
 * @property int $create_time 创建时间
 * @property string $modify_time 更新时间
 */
class BusinessTemplateFieldModel extends BaseModel
{
    protected $table = 'business_template_field';

    public $timestamps = false;
}
