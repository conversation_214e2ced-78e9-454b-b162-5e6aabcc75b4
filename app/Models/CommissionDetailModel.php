<?php

namespace App\Models;

/**
 * 佣金明细
 * Class CommissionDetailModel
 * @package App\Models
 *
 * @property int $id
 * @property int $userinfo_id 订单ID
 * @property int $business_id 业务ID
 * @property int $order_id 订单ID
 * @property int $commission_type 佣金类型
 * @property int $commission_amount 佣金金额
 * @property int $commission_rate 佣金比例
 * @property int $order_amount 订单金额
 * @property int $is_settlement 是否结算
 * @property string $detail_json 明细json
 * @property string $remark 备注
 * @property int $created_at 创建时间
 * @property int $modify_time 更新时间
 */
class CommissionDetailModel extends BaseModel
{
    protected $table = 'commission_detail';

    public $timestamps = false;
}
