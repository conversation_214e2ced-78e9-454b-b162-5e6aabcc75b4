<?php


namespace App\Models;

/**
 * 订单属性字典
 *
 * Class OrderItemFieldModel
 * @package App\Models
 *
 * @property int $id
 * @property int $order_id
 * @property int $order_item_id
 * @property string $biz_type
 * @property int $field_id
 * @property string $field_name
 * @property string $field_key
 * @property string $field_value
 * @property string $field_type
 * @property string $output_type
 * @property string $create_time
 */
class OrderItemFieldModel extends BaseModel
{
    protected $table = 'order_item_field';

    public $timestamps = false;
}
