<?php

namespace App\Models;

/**
 * class AppraiserModel
 * @package App\Models
 *
 * @property int $id id
 * @property int $userinfo_id 用户id
 * @property string $nickname 昵称
 * @property string $avatar 头像
 * @property int $work_type
 * @property int $is_leader
 * @property int $is_trainee
 * @property string $description
 * @property string $personal_profile
 * @property string $certificate
 * @property string $signature_picture
 * @property int $state
 * @property int $join_time
 * @property int $affiliation_id
 * @property int $affiliation_name
 * @property int $rest_start_time
 * @property int $rest_end_time
 * @property int $total_amount
 * @property int $total_commission
 * @property int $create_time
 */
class AppraiserModel extends BaseModel
{
    protected $table = 'appraiser';

}
