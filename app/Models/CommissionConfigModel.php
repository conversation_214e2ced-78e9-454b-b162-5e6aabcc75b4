<?php

namespace App\Models;

/**
 * 佣金配置
 * Class commissionConfigModel
 * @package App\Models
 *
 * @property int $id
 * @property int $business_id 业务id
 * @property int $business_category_id 业务类目id
 * @property int $userinfo_id 鉴定师ID
 * @property int $sub_type 分类类型
 * @property int $commission_type 佣金类型 1固定金额 2比例
 * @property int $commission_amount 佣金金额
 * @property int $commission_rate 佣金比例
 * @property int $state 状态 1启用 0禁用
 * @property int $is_deleted 删除状态 1删除 0正常
 * @property int $created_at 创建时间
 * @property int $modify_time 更新时间
 */
class CommissionConfigModel extends BaseModel
{
    protected $table = 'commission_config';

    public $timestamps = false;
}
