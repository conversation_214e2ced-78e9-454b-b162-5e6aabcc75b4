<?php

namespace App\Models;

use App\Utils\Singleton;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BaseModel
 * @package App\Models
 * @method static \Illuminate\Database\Eloquent\Builder|static newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|static newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|static query()
 * @method static \Illuminate\Database\Eloquent\Builder|static make($attributes = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static withGlobalScope($identifier, $scope)
 * @method static \Illuminate\Database\Eloquent\Builder|static withoutGlobalScope($scope)
 * @method static \Illuminate\Database\Eloquent\Builder|static withoutGlobalScopes($scopes = null)
 * @method static \Illuminate\Database\Eloquent\Builder|static removedScopes()
 * @method static \Illuminate\Database\Eloquent\Builder|static whereKey($id)
 * @method static \Illuminate\Database\Eloquent\Builder|static whereKeyNot($id)
 * @method static \Illuminate\Database\Eloquent\Builder|static where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static \Illuminate\Database\Eloquent\Builder|static firstWhere($column, $operator = null, $value = null, $boolean = 'and')
 * @method static \Illuminate\Database\Eloquent\Builder|static orWhere($column, $operator = null, $value = null)
 * @method static \Illuminate\Database\Eloquent\Builder|static latest($column = null)
 * @method static \Illuminate\Database\Eloquent\Builder|static oldest($column = null)
 * @method static \Illuminate\Database\Eloquent\Builder|static hydrate($items)
 * @method static \Illuminate\Database\Eloquent\Builder|static fromQuery($query, $bindings = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static find($id, $columns = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static findMany($ids, $columns = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static findOrFail($id, $columns = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static findOrNew($id, $columns = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static firstOrNew($attributes = [], $values = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static firstOrCreate($attributes, $values = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static updateOrCreate($attributes, $values = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static firstOrFail($columns = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static firstOr($columns = [], $callback = null)
 * @method static \Illuminate\Database\Eloquent\Builder|static value($column)
 * @method static \Illuminate\Database\Eloquent\Builder|static get($columns = [])
 * @method static \Illuminate\Database\Eloquent\Builder|static getModels($columns = [])
 */
class BaseModel extends Model
{
    use Singleton;

    /**
     * 该模型是否被自动维护时间戳.
     *
     * @var bool
     */
    public $timestamps = false;

    protected $connection = 'image_ident_server';
}
