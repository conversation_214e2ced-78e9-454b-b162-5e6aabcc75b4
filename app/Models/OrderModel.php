<?php


namespace App\Models;

/**
 * 订单
 *
 * Class OrderModel
 * @package App\Models
 * @property int $id
 * @property string $uri
 * @property int $userinfo_id
 * @property string $business_master_no
 * @property string $business_no
 * @property int $business_id
 * @property int $category_id
 * @property string $category_identifier
 * @property int $input_template_id
 * @property int $output_template_id
 * @property int $end_time
 * @property int $accept_time
 * @property int $ident_time
 * @property string $cover
 * @property string $ident_result
 * @property int $ident_truth
 * @property string $detail_json
 * @property int $state
 * @property int $create_time
 */
class OrderModel extends BaseModel
{
    protected $table = 'order';

    public $timestamps = false;
}
