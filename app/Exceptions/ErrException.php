<?php

namespace App\Exceptions;

class ErrException extends \Exception
{
    /**
     * @var array 当异常时候返回数据
     */
    protected $with = [];

    /**
     * ErrException constructor.
     *
     * @override
     * @param array $error
     * @param       $args
     */
    public function __construct(array $error, $args = [])
    {
        if (empty($error) && count($error) != 2) {
            throw new \InvalidArgumentException("异常参数错误");
        }
        list($this->code, $this->message) = $error;
        if (is_array($args) && !empty($args)) {
            $this->message = sprintf($this->message, ...$args);
        } else {
            $this->message = $args ?: $this->message;
        }
        parent::__construct($this->message, $this->code);
    }

    public function with(array $data): ErrException
    {
        $this->with = $data;
        return $this;
    }

    /**
     * @param string|int $key
     * @return array|mixed
     */
    public function getData($key = '')
    {
        if (!empty($key)) {
            return $this->with[$key];
        }
        return $this->with;
    }
}
