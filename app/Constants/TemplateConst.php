<?php


namespace App\Constants;


class TemplateConst
{
    const BIZ_TYPE_INPUT = 'input';
    const BIZ_TYPE_OUTPUT = 'output';

    const TRUTH_FAKE = 0;//假
    const TRUTH_REAL = 1;//真
    const TRUTH_CANT_IDENT = 2;//存疑
    const TRUTH_PASS = 11; // 符合
    const TRUTH_DENY = 12;// 不符合

    // 远程鉴别鉴别结果映射
    const TRUTH_DESC = [
        'common' => [
            self::TRUTH_FAKE => '假',
            self::TRUTH_REAL => '真',
            self::TRUTH_CANT_IDENT => '存疑',
            self::TRUTH_PASS => '符合该品牌正品工艺特征',
            self::TRUTH_DENY => '不符合该品牌正品工艺特征',
        ],
        5 => [
            self::TRUTH_FAKE => '不允许寄售',
            self::TRUTH_REAL => '通过',
            self::TRUTH_CANT_IDENT => '需上手',
        ],
    ];

    // 输入公共参数配置
    const INPUT_COMMON_ITEM = [
        'common' => [
            'imgs' => [
                'fieldName' => '鉴定物品图片',
                'fieldType' => 'imageList',
            ],
            'video' => [
                'fieldName' => '鉴定物品视频',
                'fieldType' => 'videoList',
            ],
            'remark' => [
                'fieldName' => '用户备注',
                'fieldType' => 'textarea',
            ]
        ]
    ];

    /**
     * 鉴定结论配置
     */
    const OUTPUT_COMMON_RESULT = [
        4 => [
            'common' => [
                'fieldName' => '鉴定结论',
            ],
            'sort' => 8,
        ],
        3 => [
            'common' => [
                'fieldName' => '鉴定结论',
                'placeholder' => '请具体描述鉴定意见',
                'maxLength' => 200,
            ],
            'sort' => 8,
        ],
        1 => [
            'common' => [
                'fieldName' => '鉴定结论'
            ],
            'sort' => 8,
        ]
    ];

    /**
     * 鉴定真假配置
     */
    const OUTPUT_COMMON_TRUTH = [
        4 => [
            'common' => [
                [
                    "value" => self::TRUTH_REAL, // 真
                    "icon" => "https://cdn.weipaitang.com/static/2020090707b53631-aa78-3631aa78-8ec6-88b89b2f1dc6-W123H123",
                    "selectedIcon" => "https://cdn.weipaitang.com/static/20200907db79fa2f-d00c-fa2fd00c-92f7-ce8031126e7b-W123H123"
                ],
                [
                    "value" => self::TRUTH_FAKE, // 假
                    "icon" => "https://cdn.weipaitang.com/static/202009077976b853-c38f-b853c38f-bf4d-9bdc1998caed-W123H123",
                    "selectedIcon" => "https://cdn.weipaitang.com/static/202009072fb4e6c1-ecc0-e6c1ecc0-a47f-1cbf47f738a1-W123H123"
                ],
                [
                    "value" => self::TRUTH_CANT_IDENT, // 存疑
                    "icon" => "https://cdn.weipaitang.com/static/2020090700e8ff30-4d29-ff304d29-8247-528895caaac1-W123H123",
                    "selectedIcon" => "https://cdn.weipaitang.com/static/202009079492623f-fc27-623ffc27-db0b-62ca447fa712-W123H123"
                ]
            ],
            'fieldName' => '鉴定结果',
            'sort' => 10,
        ],
        1 => [
            'common' => [
                [
                    "value" => self::TRUTH_REAL, // 真
                    "icon" => "https://cdn.weipaitang.com/static/2020090707b53631-aa78-3631aa78-8ec6-88b89b2f1dc6-W123H123",
                    "selectedIcon" => "https://cdn.weipaitang.com/static/20200907db79fa2f-d00c-fa2fd00c-92f7-ce8031126e7b-W123H123"
                ],
                [
                    "value" => self::TRUTH_FAKE, // 假
                    "icon" => "https://cdn.weipaitang.com/static/202009077976b853-c38f-b853c38f-bf4d-9bdc1998caed-W123H123",
                    "selectedIcon" => "https://cdn.weipaitang.com/static/202009072fb4e6c1-ecc0-e6c1ecc0-a47f-1cbf47f738a1-W123H123"
                ],
            ],
            'fieldName' => '鉴定结果',
            'sort' => 10,
        ],
        3 => [
            'common' => [
                [
                    "value" => self::TRUTH_REAL, // 真
                    "icon" => "https://cdn.weipaitang.com/static/2020090707b53631-aa78-3631aa78-8ec6-88b89b2f1dc6-W123H123",
                    "selectedIcon" => "https://cdn.weipaitang.com/static/20200907db79fa2f-d00c-fa2fd00c-92f7-ce8031126e7b-W123H123"
                ],
                [
                    "value" => self::TRUTH_FAKE, // 假
                    "icon" => "https://cdn.weipaitang.com/static/202009077976b853-c38f-b853c38f-bf4d-9bdc1998caed-W123H123",
                    "selectedIcon" => "https://cdn.weipaitang.com/static/202009072fb4e6c1-ecc0-e6c1ecc0-a47f-1cbf47f738a1-W123H123"
                ]
            ],
            'fieldName' => '鉴别结果',
            'sort' => 10,
        ],
        5 => [
            'common' => [
                [
                    "value" => self::TRUTH_REAL, // 真
                    'name' => '通过',
                ],
                [
                    "value" => self::TRUTH_FAKE, // 真
                    'name' => '不允许寄售',
                ],
                [
                    "value" => self::TRUTH_CANT_IDENT, // 真
                    'name' => '需上手',
                ]
            ],
            'fieldName' => '评估结果',
            'sort' => 10,
        ]
    ];
}
