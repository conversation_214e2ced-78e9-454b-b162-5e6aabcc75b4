<?php


namespace App\Cache;


use Illuminate\Support\Facades\Redis;
use Spin\Logger\Facades\Log;

/**
 * redis父类
 * 自定义方法禁止和redis方法同名
 * @method static bool setex(string $key, int $ttl, string|mixed $value)
 * @method static bool setnx(string $key, string $value)
 * @method static string|mixed|bool get(string $key)
 * @method static bool set(string $key, string $value)
 * @method static int incr(string $key, int $offset, int $ttl)
 * @method static int incrBy(string $key, int $offset)
 * @method static int decrBy(string $key, int $value)
 * @method static int|bool sAdd($key, ...$value1)
 * @method static array sMembers($key)
 * @method static bool exists(string $key)
 * @method static bool expire(string $key, int $ttl)
 * @method static bool hMset(string $key, array $hashKeys)
 * @method static array hMGet(string $key, array $hashKeys)
 * @method static bool hSet(string $key, string $subkey, string $value)
 * @method static int hIncrBy(string $key, string $subkey, int $offset)
 * @method static array hKeys(string $key)
 * @method static int hLen(string $key)
 * @method static int|bool rPush(string $key, string|mixed ...$value1)
 * @method static int|bool lLen(string $key)
 * @method static mixed|bool lPop(string $key)
 * @method static mixed|bool lRem(string $key, int $count, string $value)
 * @method static array lRange(string $key, int $start, int $end)
 * @method static int del(int|string|array $key1, int|string ...$otherKeys)
 * @method static string|bool hGet(string $key, string $hashKey)
 * @method static array hGetAll(string $key)
 * @method static int|false hDel(string $key, string $hashKey1, string ...$otherHashKeys)
 * @method static int sRem(string $key, string|mixed ...$member1)
 * @method static int sCard(string $key)
 * @method static array sInter($key1, ...$otherKeys)
 * @method static bool sIsMember(string $key, string $member)
 * @method static bool hSetNx(string $key, string $hashKey, string $value)
 * @method static int zAdd(string $key, $score, $value = '')
 * @method static bool zRem(string $key, $member1, ...$otherMembers)
 * @method static int zCard(string $key)
 * @method static int zRank(string $key, $member)
 * @method static int zInterStore(string $output, array $zSetKeys, array $weights = null, $aggregateFunction = 'SUM')
 * @method static array zRevRange(string $key, $start, $end, $withscores = null)
 * @method static array zRange(string $key, $start, $end, $withscores = null)
 * @method static int geoadd($key, $longitude, $latitude, $member)
 * @method static array georadius($key, $longitude, $latitude, $radius, $unit, array $options = null)
 * @method static array sPop(string $key)
 * @method static array zRevRangeByScore($key, $start, $end, array $options = array())
 * @method static bool|float zScore($key, $member)
 * @method static array sRandMember(string $key, int $num)
 * @method static string lIndex(string $key, int $index)
 * @method static int ttl(string $key)
 * @method static mixed eval(string $script, int $numberOfKeys, ...$arguments)
 * Class BaseCache
 * @package App\Cache
 */
class BaseCache
{
    public static $redisHandel;

    const CACHE_PRE = 'tws_';// 图文鉴定服务

    /**
     * 1分钟
     */
    const ONE_MINUTE = 60;

    /**
     * 2分钟
     */
    const TWO_MINUTE = 120;

    /**
     * 5分钟
     */
    const FIVE_MINUTE = 300;
    /**
     * 10分钟
     */
    const TEN_MINUTE = 600;

    /**
     * 20分钟
     */
    const TWENTY_MINUTE = 1200;

    /**
     * 30分钟或者半个小时
     */
    const THIRTY_MINUTE = 1800;

    /**
     * 1小时
     */
    const ONE_HOUR = 3600;

    /**
     * 2小时
     */
    const TWO_HOUR = 7200;

    /**
     * 3小时
     */
    const THREE_HOUR = 10800;

    /**
     * 12小时或者半天
     */
    const TWELVE_HOUR = 43200;

    /**
     * 1天
     */
    const ONE_DAY = 86400;

    /**
     * 2天
     */
    const TWO_DAY = 172800;

    /**
     * 3天
     */
    const THREE_DAY = 259200;

    /**
     * 4天
     */
    const FOUR_DAY = 345600;

    /**
     * 5天
     */
    const FIVE_DAY = 432000;

    /**
     * 7天
     */
    const SEVEN_DAY = 604800;

    /**
     * 10天
     */
    const TEN_DAY = 864000;

    /**
     * 30天或者1个月
     */
    const ONE_MONTH = 2592000;

    /**
     * 40天
     */
    const FORTY_DAY = 3456000;

    /**
     * 3个月或者90天
     */
    const THREE_MONTH = 7776000;

    /**
     * 6个月半年或180天
     */
    const SIX_MONTH = 15552000;

    /**
     * 一年365天
     */
    const ONE_YEAR = 31536000;

    /**
     * 获取键值,第一个参数是键值,其他参数跟随
     */
    protected static function getKey()
    {
        $arr = func_get_args();
        $arr[0] = self::CACHE_PRE . $arr[0];
        return call_user_func_array('sprintf', $arr);
    }

    /**
     * 获取Redis句柄
     * @return bool|mixed
     */
    protected static function getClient()
    {
        if (static::$redisHandel) {
            return true;
        }

        try {
            $config = config('database.redis.default');

            static::$redisHandel = new MyRedisCodis();
            static::$redisHandel->pconnect($config['host'], $config['port']);
            if (!empty($config['password'])) {
                self::$redisHandel->auth($config['password']);
            }
        } catch (\Exception $e) {
            static::$redisHandel = false;
            return false;
        }
        return true;
    }

    public function __call($name, $arguments)
    {
        return static::__callStatic($name, $arguments);
    }

    /**
     * 执行
     * @param $name
     * @param $arguments
     * @return false|mixed
     */
    public static function __callStatic($name, $arguments)
    {
        self::getClient();
        $reConnect = false;
        while (true) {
            if (!empty(self::$redisHandel)) {
                try {
                    return call_user_func_array([
                        self::$redisHandel,
                        $name,
                    ], $arguments);
                } catch (\Throwable $e) {
                    if ($reConnect) {
                        return false;
                    }
                    self::$redisHandel = null;
                    self::getClient();
                    $reConnect = true;
                    continue;
                }
            } else {
                return false;
            }
        }
        return false;
    }

    /**
     * cache value encode
     * @param string|array|object $data
     * @return string
     */
    public static function dataEncode($data)
    {
        if (is_array($data) || is_object($data)) {
            return json_encode($data, JSON_UNESCAPED_UNICODE);
        }
        return $data;
    }

    /**
     * cache value decode
     * @param $data
     * @return string|array
     */
    public static function dataDecode($data)
    {
        if (empty($data)) {
            return $data;
        }
        $arr = json_decode($data, true);
        if (is_array($arr)) {
            return $arr;
        }
        return $data;
    }

    /**
     * set
     * @param string $key
     * @param string|array $value
     * @param int $ttl
     * @return bool
     */
    public static function wptSet(string $key, $value, int $ttl = self::ONE_DAY)
    {
        $value = static::dataEncode($value);
        return static::setex($key, $ttl, $value);
    }

    /**
     * get
     * @param string $key
     * @return string|array
     */
    public static function wptGet(string $key)
    {
        $value = static::get($key);
        return static::dataDecode($value);
    }
}
