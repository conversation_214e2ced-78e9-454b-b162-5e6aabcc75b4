<?php

namespace App\Cache;

class MyRedisCodis extends \Redis
{
    /**
     * 序列化操作
     * @param $data
     * @return string
     */
    private function serialize($data)
    {
        if (is_array($data) || is_object($data) || is_bool($data) || is_null($data)) {
            $data = serialize($data);
        }
        return $data;
    }

    /**
     * 反序列化，判断操作
     * @param $data
     * @return bool|mixed|null
     */
    private function unserialize($data)
    {
        if (is_string($data)) {
            if ($data == "N;") {
                return null;
            }

            if (substr($data, 1, 1) == ":") {
                if ($data == "b:0;") {
                    return false;
                }

                try {
                    $dataUnSerialize = unserialize($data);
                } catch (\Exception $exception) {
                    $dataUnSerialize = false;
                }

                if ($dataUnSerialize !== false) {
                    return $dataUnSerialize;
                }
            }
        }

        return $data;
    }

    /**
     * 批量设置
     * @param array $array [key1 => value1, key2 => value2]
     * @param       $ttl
     * @return bool|void
     * @throws \RedisException
     */
    public function mSet(array $array, $ttl = 86400)
    {
        // serialize data
        foreach ($array as $key => $value) {
            $array[$key] = $this->serialize($value);
        }

        $ret = parent::mset($array);
        $pipeline = parent::multi(self::PIPELINE);
        foreach ($array as $key => $v) {
            $pipeline->expire($key, $ttl);
        }

        $pipeline->exec();
        return $ret;
    }

    /**
     * 批量设置，如果key存在，不设置
     * @param array $array [key1 => value1, key2 => value2]
     * @param int $ttl
     * @return int
     * @throws \RedisException
     */
    public function mSetNx(array $array, $ttl = 86400)
    {
        // serialize data
        foreach ($array as $key => $value) {
            $array[$key] = $this->serialize($value);
        }

        $ret = parent::msetnx($array);
        $pipeline = parent::multi(self::PIPELINE);
        foreach ($array as $key => $v) {
            $pipeline->expire($key, $ttl);
        }

        $pipeline->exec();
        return $ret;
    }

    /**
     * 批量获取,自动根据传进来的ID排序
     * @param array $array 包含idGroup(键),keyGroup2个组
     * @param int $notCombine 不合并
     * @return array
     * @throws \RedisException
     */
    public function mGet(array $array, $notCombine = 0)
    {
        $list = parent::mget($array['keyGroup']);
        foreach ($list as $k => $v) {
            $list[$k] = $this->unserialize($v);
        }
        if ($notCombine) {
            return $list;
        } else {
            return array_combine($array['idGroup'], $list);
        }
    }

    //get 使用unserialize反序列化
    public function get($key)
    {
        $result = parent::get($key);
        return $this->unserialize($result);
    }

    //set 使用serialize序列化
    public function set($key, $data, $ttl = 86400)
    {
        $data = $this->serialize($data);
        if ($ttl) {
            return parent::setex($key, $ttl, $data);
        } else {
            return parent::set($key, $data);
        }
    }

    //setnx 使用serialize序列化
    public function setnx($key, $data, $ttl = 86400)
    {
        $data = $this->serialize($data);
        if ($ttl) {
            $ret = parent::setnx($key, $data);
            if ($ret) {
                parent::expire($key, $ttl);
            }
            return $ret;
        }
    }

    //hSet
    public function hSet($key, $subKey, $value, $ttl = 86400)
    {
        $value = $this->serialize($value);

        if (parent::exists($key)) {
            $ret = parent::hSet($key, $subKey, $value);
        } else {
            $ret = parent::hSet($key, $subKey, $value);
            if ($ttl) {
                parent::expire($key, $ttl);
            }
        }
        return $ret;
    }

    //hGet
    public function hGet($key, $subKey)
    {
        $result = parent::hGet($key, $subKey);
        return $this->unserialize($result);
    }

    public function hGetAll($key)
    {
        $result = parent::hGetAll($key);
        return $this->unserialize($result);
    }

    /**
     * 递增
     * @param $key
     * @param int $offset
     * @param int $ttl
     * @return mixed
     */
    public function incr($key, $offset = 1, $ttl = 86400)
    {
        $incr = parent::incrBy($key, $offset);
        if ($incr == $offset) {
            parent::expire($key, $ttl);
        }
        return $incr;
    }

    /**
     * 递增lua
     * @param $key
     * @param int $offset
     * @param int $ttl
     * @return mixed
     * @throws \RedisException
     */
    public function luaIncr($key, $offset = 1, $ttl = 86400)
    {
        $script = "local x = redis.call('INCRBY',KEYS[1],ARGV[1])
            if tonumber(x) == tonumber(ARGV[1]) then
                redis.call('EXPIRE',KEYS[1],ARGV[2])
            end
            return x";
        return parent::eval($script, [$key, $offset, $ttl], 1);
    }

    /**
     * 递减
     * @param $key
     * @param int $offset
     * @param int $ttl
     * @return mixed
     */
    public function decr($key, $offset = 1, $ttl = 86400)
    {
        $decr = parent::decrBy($key, $offset);
        if ($decr == -1 * $offset) {
            parent::expire($key, $ttl);
        }
        return $decr;
    }

    /**
     * 哈希递增
     * @param $key
     * @param $subKey
     * @param $offset
     * @param int $ttl
     * @return int
     * @throws \RedisException
     */
    public function hincr($key, $subKey, $offset = 1, $ttl = 86400)
    {
        $hincr = parent::hIncrBy($key, $subKey, $offset);
        if ($hincr == $offset) { //第一次插入设置过期时间
            parent::expire($key, $ttl);
        }
        return $hincr;
    }

    /**
     * 列表推送
     * @param $key
     * @param $value
     * @param int $ttl
     * @return mixed
     */
    public function lPush($key, $value, $ttl = 86400)
    {
        if (parent::exists($key)) {
            $ret = parent::lPush($key, $value);
        } else {
            $ret = parent::lPush($key, $value);
            if ($ttl) {
                parent::expire($key, $ttl);
            }
        }
        return $ret;
    }

    /**
     * 列表推送
     * @param $key
     * @param $value
     * @param int $ttl
     * @return mixed
     * @throws \RedisException
     */
    public function rPush($key, $value, $ttl = 86400)
    {
        if (parent::exists($key)) {
            $ret = parent::rPush($key, $value);
        } else {
            $ret = parent::rPush($key, $value);
            if ($ttl) {
                parent::expire($key, $ttl);
            }
        }
        return $ret;
    }

    /**
     * 批量插入数据到队列头部
     * @param     $key
     * @param     $values
     * @param int $ttl
     * @return bool|int
     * @throws \RedisException
     */
    public function mLPush($key, $values, $ttl = 86400)
    {
        $ret = parent::lPush($key, ...$values);
        parent::expire($key, $ttl);

        return $ret;
    }

    /**
     * 批量插入数据到队列尾部
     * @param     $key
     * @param     $values
     * @param int $ttl
     * @return bool|int
     * @throws \RedisException
     */
    public function mRPush($key, $values, $ttl = 86400)
    {
        $ret = parent::rPush($key, ...$values);
        parent::expire($key, $ttl);

        return $ret;
    }

    /**
     * 获取队列数据
     * @param string $key
     * @param int $start
     * @param int $end
     * @return array
     * @throws \RedisException
     */
    public function lRange($key, $start, $end)
    {
        return parent::lRange($key, $start, $end);
    }

    /**
     * 更新过期时间
     * @param string $key
     * @param int $ttl
     * @return bool
     */
    public function expire($key, $ttl = 86400)
    {
        if ($ttl) {
            return parent::expire($key, $ttl);
        }

        return false;
    }

    /**
     * hMSet 不会序列化
     * @param string $key
     * @param array $value
     * @param int $ttl
     * @return bool
     * @throws \RedisException
     */
    public function hMset($key, $value, $ttl = 86400)
    {
        if (parent::exists($key)) {
            $ret = parent::hMset($key, $value);
        } else {
            $ret = parent::hMset($key, $value);
            if ($ttl) {
                parent::expire($key, $ttl);
            }
        }
        return $ret;
    }

    /**
     * 批量设置有序集合
     * @param string $key
     * @param array $members [$score1, $member1, $score2, $member2, $score3, $member3]
     * @param        $ttl
     * @return int
     * @throws \RedisException
     */
    public function mZAdd(string $key, array $members, $ttl = 86400)
    {
        $ret = parent::zAdd($key, ...$members);
        parent::expire($key, $ttl);

        return $ret;
    }

    /**
     * @param $key
     * @return array|bool
     * @throws \RedisException
     */
    public function getMetaData($key)
    {
        $value = $this->get($key);
        if ($value) {
            return [
                'expire' => time() + parent::ttl($key),
                'data' => $value
            ];
        }
        return false;
    }

    /**
     * 哈希递增 如果有过期时间 每次重置
     * @param $key
     * @param $subKey
     * @param $offset
     * @param int $ttl
     * @return int
     * @throws \RedisException
     */
    public function hincrTTL($key, $subKey, $offset = 1, $ttl = 0)
    {
        $hincr = parent::hIncrBy($key, $subKey, $offset);
        if ($ttl > 0) {
            parent::expire($key, $ttl);
        }
        return $hincr;
    }

    /**
     * push队列 每次设置 都重置过期时间
     * @param $key
     * @param $value
     * @param int $ttl
     * @return bool|int
     * @throws \RedisException
     */
    public function rPushTTL($key, $value, $ttl = 0)
    {
        $ret = parent::rPush($key, $value);
        if ($ttl > 0 && $ret) {
            parent::expire($key, $ttl);
        }
        return $ret;
    }

    /**
     * push队列 每次设置 都重置过期时间
     * @param $key
     * @param $value
     * @param int $ttl
     * @return bool|int
     * @throws \RedisException
     */
    public function lPushTTL($key, $value, $ttl = 0)
    {
        $ret = parent::lPush($key, $value);
        if ($ttl > 0 && $ret) {
            parent::expire($key, $ttl);
        }
        return $ret;
    }

    /**
     * 调用原生方法
     * @param $name
     * @param $arguments
     * @return false|mixed
     * @throws \Exception
     */
    public function __call($name, $arguments)
    {
        // o = origin
        if (substr($name, 0, 1) == 'o') {
            $originActionName = lcfirst(substr($name, 1));
            $callback = "parent::{$originActionName}";
            if (is_callable($callback)) {
                return call_user_func_array($callback, $arguments);
            }

            throw new \Exception("redis原生方法不可用", -1);
        }

        throw new \Exception("redis原生方法不存在", -1);
    }
}
