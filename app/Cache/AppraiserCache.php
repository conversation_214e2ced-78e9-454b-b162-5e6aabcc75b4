<?php

namespace App\Cache;

use App\Constants\CacheConstant;

class AppraiserCache extends BaseCache
{
    const WASH_APPRAISER_LOCK  = 'wash_appraiser_lock_%s';

    // 海外图文已派单的鉴定师
    const OVERSEA_DISPATCH_APPRAISER = 'oversea_dispatch_appraiser';
    // 鉴定师已接单量
    const APPRAISER_DISPATCH_COUNT = 'appraiser_dispatch_count_%s_%s';

    public static function washAppraiserLock($userinfoId, $ttl = 5)
    {
        $res = self::incr(self::getKey(self::WASH_APPRAISER_LOCK, $userinfoId), 1,$ttl);
        return $res == 1;
    }


    /**
     * 海外图文已派单鉴定师
     *
     * @param int $userinfoId
     * @param int $score
     * @param int $ttl
     * @return int
     */
    public static function addOverseaDispatchAppraiser(
        int $userinfoId,
        int $score = 1,
        int $ttl = self::THREE_MONTH): int
    {
        $res =  self::zAdd(self::getKey(self::OVERSEA_DISPATCH_APPRAISER), $score, $userinfoId);
        if ($res) {
            self::expire(self::getKey(self::OVERSEA_DISPATCH_APPRAISER), $ttl);
        }
        return intval($res);
    }
    public static function getOverseaDispatchAppraiser(): array
    {
        return self::zRange(self::getKey(self::OVERSEA_DISPATCH_APPRAISER), 0, -1, false);
    }
    public static function delOverseaDispatchAppraiser(): int
    {
        return self::del(self::getKey(self::OVERSEA_DISPATCH_APPRAISER));
    }


    /**
     * 接单量+1
     *
     * @param int $userinfoId
     * @param string $date
     * @param int $offset
     * @param int $ttl
     * @return int
     */
    public static function addAppraiserDispatchCount(
        int $userinfoId,
        string $date,
        int $offset = 1,
        int $ttl = self::SEVEN_DAY
    ) {
        return self::incr(self::getKey(self::APPRAISER_DISPATCH_COUNT, $userinfoId, $date), $offset, $ttl);
    }

    /**
     * 获取接单量
     *
     * @param int $userinfoId
     * @param string $date
     * @return bool|mixed|string
     */
    public static function getAppraiserDispatchCount(
        int $userinfoId,
        string $date
    ) {
        return (int)self::get(self::getKey(self::APPRAISER_DISPATCH_COUNT, $userinfoId, $date));
    }
}