<?php

namespace App\Libraries;


/**
 * 统一锁库文件
 *
 * Class RedisRedLock
 */
class RedLock
{
    private $retryDelay;
    private $retryCount;
    private $clockDriftFactor = 0.01;
    private $quorum;
    private $servers = [];
    private $instances = [];

    public function __construct(array $servers, $retryDelay = 200, $retryCount = 3)
    {
        $this->servers = $servers;
        $this->retryDelay = $retryDelay;
        $this->retryCount = $retryCount;
        $this->quorum = min(count($servers), (count($servers) / 2 + 1));
    }

    private function __clone()
    {
        // do noting,forbid call clone func
    }

    private static $instance;

    /**
     * 获取锁单例
     *
     * 注意：多次获取单例，传参仅第一次有效，之后修改需要调用对应的set函数
     *
     * @param int $retryCount 重试次数
     * @param int $retryDelay 毫秒
     * @return RedLock
     */
    public static function getInstance($retryCount = 0, $retryDelay = 200)
    {
        if (!self::$instance instanceof self) {
            self::$instance = new self(
                [config('database.redis.default')],
                $retryDelay,
                $retryCount
            );
        }

        return self::$instance;
    }

    /**
     * 重新设置重试次数
     *
     * @param $count
     * @return $this
     */
    public function setRetryCount($count)
    {
        $this->retryCount = $count;

        return $this;
    }

    /**
     * 重新设置重试延迟时间
     *
     * @param int $delay 毫秒
     * @return $this
     */
    public function setRetryDelay($delay)
    {
        $this->retryCount = $delay;

        return $this;
    }

    /**
     * 获取锁
     *
     * @param string $resource 需要获取锁的资源，可以理解为需要加锁的key
     * @param int $ttl 加锁时长，单位秒
     * @return array|bool
     */
    public function lock($resource, $ttl)
    {
        $ttl = $ttl * 1000;
        $this->initInstances();
        $token = uniqid();
        $retry = $this->retryCount;
        do {
            $n = 0;
            $startTime = microtime(true) * 1000;
            foreach ($this->instances as $instance) {
                if ($this->lockInstance($instance, $resource, $token, $ttl)) {
                    $n++;
                }
            }
            // Add 2 milliseconds to the drift to account for Redis expires
            // precision, which is 1 millisecond, plus 1 millisecond min drift
            // for small TTLs.
            $drift = ($ttl * $this->clockDriftFactor) + 2;
            $validityTime = $ttl - (microtime(true) * 1000 - $startTime) - $drift;
            if ($n >= $this->quorum && $validityTime > 0) {
                return [
                    'validity' => $validityTime,
                    'resource' => $resource,
                    'token' => $token,
                    'ttl' => $ttl,
                ];
            }
            foreach ($this->instances as $instance) {
                $this->unlockInstance($instance, $resource, $token);
            }

            // Wait a random delay before to retry
            $delay = mt_rand(floor($this->retryDelay / 2), $this->retryDelay);
            usleep($delay * 1000);
            $retry--;
        } while ($retry > 0);

        return false;
    }

    /**
     * 释放锁
     * @param array $lock 获取到的锁
     */
    public function unlock(array $lock)
    {
        $this->initInstances();
        $resource = $lock['resource'];
        $token = $lock['token'];
        foreach ($this->instances as $instance) {
            $this->unlockInstance($instance, $resource, $token);
        }
    }

    /**
     * 刷新锁
     * @param array $lock
     * @return array|bool
     */
    public function refreshLock(array $lock)
    {
        $this->unlock($lock);

        return $this->lock($lock['resource'], $lock['ttl']);
    }

    private function initInstances()
    {
        if (empty($this->instances)) {
            foreach ($this->servers as $server) {
                $redis = new \Redis();
                $redis->connect($server['host'], $server['port'], $server['timeout'] ?? 0);
                $redis->auth($server['password']);
                $this->instances[] = $redis;
            }
        }
    }

    private function lockInstance($instance, $resource, $token, $ttl)
    {
        return $instance->set($resource, $token, ['NX', 'PX' => $ttl]);
    }

    private function unlockInstance($instance, $resource, $token)
    {
        $script = '
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return 0
            end
        ';

        return $instance->eval($script, [$resource, $token], 1);
    }
}
