<?php


namespace App\Libraries;


use App\Constants\LogConst;
use App\Utils\Singleton;
use Spin\Logger\Facades\Log;
use WptBus\Facades\Bus;
use WptBus\Service\Sky\Module\Media;
use WptBus\Service\Sky\Module\Request\MediaBaseParams;

class OverseaClient
{
    use Singleton;

    /**
     * 上传图片（url）
     * @param $imgLink
     * @param $keyName
     * @return array
     */
    public function uploadImageByLink($imgLink, $keyName = '')
    {
        $baseParams = $this->getMediaParams();  // 这里用tupian 无法自定义图片路径
        $result = Bus::sky()->media->uploadByLink($baseParams, Media::MEDIA_TYPE_IMG, $imgLink, $keyName);
        if (isset($result['code']) && $result['code'] != 0) {
            Log::info(LogConst::IMAGE_IDENT_SERVER, '上传图片到cos失败-uploadImageByLink', [
                'channel' => 'updateImgUrl',
                'desc' => '上传图片到cos失败-uploadImageByLink',
                'message' => ['result' => $result, 'imgLink' => $imgLink, 'key' => $keyName],
            ]);
        }
        return $result['data'] ?? [];
    }

    /**
     * @param $sceneName
     * @return MediaBaseParams
     */
    private function getMediaParams($sceneName = 'recognition')
    {
        $baseParams = new MediaBaseParams();
        $baseParams->source = 'server';
        $baseParams->businessName = 'heron';
        $baseParams->sceneName = $sceneName;
        return $baseParams;
    }
}
