<?php

namespace App\Service;

use App\Constants\LogConst;
use App\ErrCode\OrderErr;
use App\Exceptions\ErrException;
use App\Utils\ImageUtil;
use App\Utils\Singleton;
use Spin\Logger\Facades\Log;

class OrderSubmitService
{
    use Singleton;

    /**
     *
     * @param $field
     * @return void
     * @throws ErrException
     */
    public function switchCheck($field)
    {
        $fieldType = $field['fieldType'];
        $fieldName = $field['fieldName'];
        $inputValue = $field['fieldValue'];

        switch ($fieldType) {
            case 'textarea':
            case "input":
                $this->checkInput($field, $fieldName, $inputValue);
                break;
            case "dropDownList":
            case "popup":
                $this->checkPopup($field, $fieldName, $inputValue);
                break;
            case "singleSelect":
                $this->checkSingleSelect($field, $fieldName, $inputValue);
                break;
            case "textSelect":
            case "iconSelect":
                $this->checkIconSelect($field, $inputValue, $fieldName);
                break;
            case "voiceInput":
                $inputValue = $this->checkVoiceInput($field, $inputValue, $fieldName);
                break;
            case "evaluateAttr":
                break;
            default:
                throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "参数类型不对");
        }

        return $inputValue;
    }

    /**
     * 检测
     *
     * @param $field
     * @param $inputValue
     * @param $fieldName
     * @return void
     * @throws ErrException
     */
    private function checkIconSelect($field, $inputValue, $fieldName)
    {
        $fieldObject = get_property($field, 'fieldObject', []);
        $fieldObjectValue = array_column((array)$fieldObject, 'value');
        if (!in_array($inputValue, $fieldObjectValue) || $inputValue === null) {
            throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $fieldName . "'录入数据非法");
        }
    }

    /**
     * 录音
     *
     * @param $field
     * @param $inputValue
     * @param $inputName
     * @return array|mixed
     * @throws ErrException
     */
    private function checkVoiceInput($field, $inputValue,  $inputName)
    {
        Log::info(LogConst::IMAGE_IDENT_SERVER, "检查录音", ['inputValue' => $inputValue]);
        $isRequired = get_property($field, 'isRequired', 1);
        if (is_string($inputValue)) {
            $inputValue = json_decode($inputValue, true) ?: [];
        }
        if (!is_array($inputValue)) {
            $inputValue = [];
        }

        if ($isRequired && empty($inputValue)) {
            throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $inputName . "'不能为空");
        }
        if (count($inputValue) > 5) {
            throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $inputName . "'条数过多");
        }
        foreach ($inputValue as $item) {
            if (empty(get_property($item, 'duration', '')) ||
                empty(get_property($item, 'voiceUrl', ''))
            ) {
                throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $inputName . "'数据格式不正确");
            }
        }

        return $inputValue;
    }

    /**
     * 检测单选
     *
     * @param $field
     * @param $fieldName
     * @param $inputValue
     * @return void
     * @throws ErrException
     */
    private function checkSingleSelect($field, $fieldName, $inputValue)
    {
        $fieldOptions = get_property($field, 'optionsList', []);
        $isRequired = get_property($field, 'isRequired', 1);
        if (!empty($inputValue)) {
            $isRequired = 1;
        }
        $fieldOptions = is_array($fieldOptions) ? array_column($fieldOptions, 'optionName') : [];
        if ($isRequired && !in_array($inputValue, $fieldOptions)) {
            throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $fieldName . "'录入数据非法");
        }
    }

    /**
     * 检测输入
     *
     * @param $field
     * @param $fieldName
     * @param $inputValue
     * @return void
     * @throws ErrException
     */
    private function checkInput($field, $fieldName, $inputValue)
    {
        $isRequired = get_property($field, 'isRequired', 1);
        $maxLength = get_property($field, 'maxLength', 0);
        $minLength = get_property($field, 'minLength', 0);

        if ($isRequired && empty($inputValue)) {
            throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $fieldName . "'必填");
        }
        if (mb_strlen($inputValue) > $maxLength && $maxLength != 0) {
            throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $fieldName . "'长度超过限制");
        }

        if (mb_strlen($inputValue) < $minLength && $minLength != 0) {
            throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $fieldName . "'长度小于$minLength");
        }
    }

    /**
     * 检测下拉框
     *
     * @param $field
     * @param $fieldTitle
     * @param $inputValue
     * @return void
     * @throws ErrException
     */
    private function checkPopup($field, $fieldTitle, $inputValue)
    {
        $isRequired = get_property($field, 'isRequired', 1);
        if ($isRequired && empty($inputValue)) {
            throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $fieldTitle . "'必填");
        }

        $fieldOptions = get_property($field, 'optionsList');
        $fieldOptions = is_array($fieldOptions) ? array_column($fieldOptions, 'optionName') : [];
        if (!empty($fieldOptions) && !in_array($inputValue, $fieldOptions)) {
            throw new ErrException(OrderErr::ORDER_SUBMIT_PARAMS_ERR, "'" . $fieldTitle . "'必须是选择项内数据");
        }
    }
}
