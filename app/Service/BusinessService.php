<?php

namespace App\Service;

use App\Constants\CommonConst;
use App\Models\BusinessModel;
use App\Utils\Singleton;

class BusinessService
{
    use Singleton;

    public function add($name, $notifyUrl)
    {
        $model = new BusinessModel();
        $model->name = $name;
        $model->notify_url = $notifyUrl;
        $model->create_time = time();

        return $model->save();
    }

    public function getOneById($id): array
    {
        $fields = ['id', 'name', 'notify_url', 'create_time',];
        $business = BusinessModel::query()
            ->select($fields)
            ->where('id', $id)
            ->first();
        if (empty($business)) {
            return [];
        }

        return $business->toArray();
    }

    public function getOneByName($name): array
    {
        $fields = ['id', 'name', 'notify_url', 'create_time',];
        $business = BusinessModel::query()
            ->select($fields)
            ->where('name', $name)
            ->first();
        if (empty($business)) {
            return [];
        }

        return $business->toArray();
    }

    /**
     * @param $id
     * @param $params
     * @return int
     */
    public function updateById($id, $params)
    {
        if (!empty($params['name'])) {
            $update['name'] = $params['name'];
        }
        if (!empty($params['notifyUrl'])) {
            $update['notify_url'] = $params['notifyUrl'];
        }
        if (isset($params['state']) && $params['state'] != -1) {
            $update['state'] = $params['state'];
        }

        if (empty($update)) {
            return 0;
        }

        return BusinessModel::query()
            ->where('id', $id)
            ->update($update);
    }


    public function list($where = [], $page = 1, $pageSize = 20)
    {
        $fields = ['id', 'name', 'notify_url as notifyUrl', 'state', 'create_time as createTime',];
        $build = BusinessModel::query()
           ->select($fields);
        if (isset($where['state']) && $where['state'] != -1) {
            $build->where('state', $where['state']);
        }

        $total = $build->count();
        $list = $build->forPage($page, $pageSize)->get()->toArray();
        $isEnd = count($list) < $pageSize;

        return [
            'list' => $list,
            'isEnd' => $isEnd,
            'total' => $total,
            'page' => $page + 1,
            'pageSize' => $pageSize,
        ];
    }

    public function getAll()
    {
        $fields = ['id', 'name'];
        return BusinessModel::query()->select($fields)->get()->toArray();
    }

    /**
     * 根据id批量获取
     *
     * @param array $ids
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getListByIds(array $ids)
    {
        return BusinessModel::query()->whereIn('id', $ids)->get();
    }
}
