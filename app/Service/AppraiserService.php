<?php

namespace App\Service;

use App\Constants\CommonConst;
use App\Models\AppraiserModel;
use App\Params\Appariser\AppraiserParams;
use App\Utils\Singleton;
use Illuminate\Support\Str;

class AppraiserService
{
    use Singleton;

    // 下划线字段
    const FIELDS = [
        'userinfo_id as userinfoId',
        'nickname',
        'avatar',
        'work_type as workType',
        'is_leader as isLeader',
        'is_trainee as isTrainee',
        'description',
        'certificate',
        'signature_picture as signaturePicture',
        'join_time as joinTime',
        'affiliation_id as affiliationId',
        'affiliation_name as affiliationName',
        'rest_start_time as restStartTime',
        'rest_end_time as restEndTime',
        'state',
        'create_time as createTime',
    ];


    /**
     * 创建鉴定师
     *
     * @param AppraiserParams $params
     * @return bool
     */
    public function create(AppraiserParams $params): bool
    {
        $model = new AppraiserModel();
        $model->userinfo_id = $params->userinfoId;
        $model->nickname = $params->nickname;
        $model->avatar = $params->avatar;
        $model->work_type = $params->workType;
        //$model->is_leader = $params->isLeader;
        $model->is_trainee = $params->isTrainee;
        $model->description = $params->description;
        $model->personal_profile = $params->personalProfile;
        $model->certificate = $params->certificate;
        $model->signature_picture = $params->signaturePicture;
        $model->join_time = $params->joinTime;
        $model->affiliation_id = $params->affiliationId;
        $model->affiliation_name = $params->affiliationName;
        $model->create_time = time();

        return $model->save();
    }

    /**
     * 根据userinfoId删除 - 软删除
     *
     * @param $userinfoId
     * @return int
     */
    public function deleteByUserinfoId($userinfoId)
    {
        return AppraiserModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('state', CommonConst::STATE_ACTIVE)
            ->update([
                'state' => CommonConst::STATE_DISABLE,
            ]);
    }

    /**
     * 恢复用户 - 激活
     *
     * @param $userinfoId
     * @return bool|int
     */
    public function activeByUserinfoId($userinfoId)
    {
        return AppraiserModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('state', CommonConst::STATE_DISABLE)
            ->update([
                'state' => CommonConst::STATE_ACTIVE,
            ]);
    }

    /**
     * 鉴定师详情
     *
     * @param $userinfoId
     * @param bool $checkState 是否校验状态   默认校验
     * @return array
     */
    public function detail($userinfoId, bool $checkState = true)
    {
        $query = AppraiserModel::query()
            ->select(self::FIELDS)
            ->where('userinfo_id', $userinfoId);

        if ($checkState) {
            $query->where('state', CommonConst::STATE_ACTIVE);
        }

        $appraiser = $query->first();
        if (empty($appraiser)) {
            return [];
        }

        return $appraiser->toArray();
    }

    public function updateByUserinfoId($userinfoId, AppraiserParams $params)
    {
        $update = [
            'nickname' => $params->nickname,
            'avatar' => $params->avatar,
            'work_type' => $params->workType,
            //'is_leader' => $params->isLeader,
            'is_trainee' => $params->isTrainee,
            'description' => $params->description,
            'personal_profile' => $params->personalProfile,
            'certificate' => $params->certificate,
            'signature_picture' => $params->signaturePicture,
        ];
        if (!empty($params->joinTime)) {
            $update['join_time'] = $params->joinTime;
        }
        if (!empty($params->affiliationId)) {
            $update['affiliation_id'] = $params->affiliationId;
        }
        if (!empty($params->affiliationName)) {
            $update['affiliation_name'] = $params->affiliationName;
        }

        return AppraiserModel::query()
            ->where('userinfo_id', $userinfoId)
            ->update($update);
    }

    /**
     * 更新用户角色
     * 目前只支持更新isLeader和isTrainee
     *
     * @param $userinfoId
     * @param array $params
     * @return bool|int
     */
    public function updateRoleByUserinfoId($userinfoId, array $params = [])
    {
        $update = [];
        if (!empty($params['isLeader'])) {
            $update['is_leader'] = $params['isLeader'];
        }
        if (!empty($params['isTrainee'])) {
            $update['is_trainee'] = $params['isTrainee'];
        }

        if (empty($update)) {
            return false;
        }

        return AppraiserModel::query()
            ->where('userinfo_id', $userinfoId)
            ->update($update);
    }


    public function list($where, $page = 1, $pageSize = 20)
    {
        $build = AppraiserModel::query()
            ->select(self::FIELDS);
        if (!empty($where['userinfoId'])) {
            $build->where('userinfo_id', $where['userinfoId']);
        }
        if (!empty($where['workType'])) {
            $build->where('work_type', $where['workType']);
        }
        if (!empty($where['isLeader'])) {
            $build->where('is_leader', $where['isLeader']);
        }
        if (!empty($where['isTrainee'])) {
            $build->where('is_trainee', $where['isTrainee']);
        }

        $total = $build->count();
        $list = $build->forPage($page, $pageSize)->get()->toArray();
        $isEnd = count($list) < $pageSize;

        return [
            'list' => $list,
            'isEnd' => $isEnd,
            'total' => $total,
            'page' => $page + 1,
        ];
    }

    public function listByUserinfoIds(array $userinfoIds, array $fields = self::FIELDS): array
    {
        $build = AppraiserModel::query()
            ->select($fields)
            ->where('state', CommonConst::STATE_ACTIVE)
            ->whereIn('userinfo_id', $userinfoIds);

        return $build->get()->toArray();
    }

    public function getAll(): array
    {
        $build = AppraiserModel::query()
            ->select(self::FIELDS)
            ->where('state', CommonConst::STATE_ACTIVE);

        return $build->get()->toArray();
    }

}