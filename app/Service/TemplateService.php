<?php


namespace App\Service;


use App\Constants\CommonConst;
use App\Models\BusinessTemplateFieldModel;
use App\Models\BusinessTemplateModel;
use App\Utils\Singleton;

/**
 * 模板服务
 *
 * Class TemplateService
 * @package App\Service
 */
class TemplateService
{
    use Singleton;

    /**
     * 创建模板
     *
     * @param int $businessId
     * @param string $templateName
     * @param string $bizType
     * @return int
     */
    public function create(int $businessId, string $templateName, string $bizType)
    {
        $data = [
            'business_id' => $businessId,
            'template_name' => $templateName,
            'biz_type' => $bizType,
            'state' => CommonConst::STATE_ACTIVE,
            'is_deleted' => CommonConst::NOT_DELETED,
            'create_time' => time(),
        ];
        return BusinessTemplateModel::query()->insertGetId($data);
    }

    /**
     * 模板是否存在
     *
     * @param int $businessId
     * @param string $templateName
     * @return bool
     */
    public function exist(int $businessId, string $templateName)
    {
        return BusinessTemplateModel::query()
            ->where('business_id', $businessId)
            ->where('template_name', $templateName)
            ->count() > 0;
    }
    /**
     * 根据模板ID获取模板
     *
     * @param int $templateId
     * @return BusinessTemplateModel|object|null
     */
    public function getTemplateById(int $templateId)
    {
        return BusinessTemplateModel::query()->where('id', $templateId)->first();
    }

    /**
     * 获取模板详情
     *
     * @param int $templateId
     * @return array
     */
    public function getTemplateDetail(int $templateId)
    {
        $template = $this->getTemplateById($templateId);
        if (empty($template)) {
            return [];
        }
        $templateField = $this->getTemplateFieldByTemplateId($templateId);
        $templateFieldResultList = [];

        if (count($templateField)) {
            $fieldIds = array_column($templateField->toArray(), 'field_id');
            $fieldList = array_column(FieldService::getInstance()->getFieldByIds($fieldIds), null, 'id');
        }
        /** @var BusinessTemplateFieldModel @item **/
        foreach ($templateField as $item) {
            $fieldInfo = $fieldList[$item->field_id] ?? [];
            $templateFieldResultList[] = [
                'id' => $item->id,
                'parentId' => $item->parent_id,
                'parentOptionName' => $item->parent_option_name,
                'fieldId' => $item->field_id,
                'fieldType' => $fieldInfo['fieldType'] ?? '',
                'fieldName' => $fieldInfo['name'] ?? '',
                'fieldKey' => $item->field_key,
                'outputType' => $item->output_type,
                'placeholder' => $fieldInfo['placeholder'],
                'minLength' => $fieldInfo['minLength'] ?? 0,
                'maxLength' => $fieldInfo['maxLength'] ?? 0,
                'isRequired' => boolval($fieldInfo['isRequired'] ?? ''),
                'sort' => $item->sort,
                'optionsList' => $fieldInfo['optionsList'] ?? []
            ];
        }

        return [
            'id' => $template->id,
            'templateName' => $template->template_name,
            'bizType' => $template->biz_type,
            'state' => $template->state,
            'isDeleted' => $template->is_deleted,
            'fields' => $templateFieldResultList,
        ];
    }

    /**
     * 新增模板字段
     *
     * @param array $data
     * @return int
     */
    public function createTemplateField(array $data)
    {
        return BusinessTemplateFieldModel::query()->insertGetId($data);
    }

    /**
     * 根据模板id获取字段列表
     *
     * @param int $templateId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTemplateFieldByTemplateId(int $templateId)
    {
        return BusinessTemplateFieldModel::query()
            ->where('template_id', $templateId)
            ->where('is_deleted', CommonConst::NOT_DELETED)
            ->where('state', CommonConst::STATE_ACTIVE)
            ->get();
    }

    /**
     * 获取模板字段
     *
     * @param int $id
     * @return BusinessTemplateFieldModel|null
     */
    public function getTemplateFileById(int $id)
    {
        return BusinessTemplateFieldModel::query()
            ->where('id', $id)
            ->where('is_deleted', 0)
            ->first();
    }

    /**
     * 更新模板字段
     *
     * @param int $id
     * @param array $data
     * @return bool|int
     */
    public function updateTemplateFieldById(int $id, array $data)
    {
        return BusinessTemplateFieldModel::query()->where('id', $id)->update($data);
    }

    /**
     * 数据更新
     *
     * @param int $id
     * @param array $data
     * @return bool|int
     */
    public function updateById(int $id, array $data)
    {
        return BusinessTemplateModel::query()->where('id', $id)->update($data);
    }
}
