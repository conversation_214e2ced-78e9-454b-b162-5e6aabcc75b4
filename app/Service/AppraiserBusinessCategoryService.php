<?php

namespace App\Service;

use App\Models\AppraiserBusinessCategoryModel;
use App\Utils\Singleton;

class AppraiserBusinessCategoryService
{
    use Singleton;

    public function unBindBusinessCategory($userinfoId, $businessId)
    {
        return AppraiserBusinessCategoryModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('business_id', $businessId)
            ->delete();
    }

    public function bindBusinessCategory($userinfoId, $businessId, array $categoryIds): bool
    {
        $data = [];
        foreach ($categoryIds as $categoryId){
            $data[] = [
                'userinfo_id' => $userinfoId,
                'business_id' => $businessId,
                'category_id' => $categoryId,
                'create_time' => time(),
            ];
        }

        return AppraiserBusinessCategoryModel::query()->insert($data);
    }

    public function unBindBusinessCategoryBatch($userinfoId, array $businessIds = [])
    {
        $build = AppraiserBusinessCategoryModel::query()
            ->where('userinfo_id', $userinfoId);
        if (!empty($businessIds)) {
            $build->whereIn('business_id', $businessIds);
        }

        return $build->delete();
    }

    public function getListByUserinfoId($userinfoId, $where = [])
    {
        $build = AppraiserBusinessCategoryModel::query()
            ->where('userinfo_id', $userinfoId);
        if (!empty($where['business_id'])) {
            $build->where('business_id', $where['business_id']);
        }

        $list = $build->get();
        if (empty($list)) {
            return [];
        }
        return $list->toArray();
    }

    public function getListByUesrinfoIds($userinfoId, $where = [])
    {
        $build = AppraiserBusinessCategoryModel::query()
            ->whereIn('userinfo_id', $userinfoId);
        if (!empty($where['businessId'])) {
            $build->where('business_id', $where['businessId']);
        }

        $list = $build->get();
        if (empty($list)) {
            return [];
        }
        return $list->toArray();
    }

    public function getList($where) {
        $build = AppraiserBusinessCategoryModel::query();
        if (!empty($where['businessId'])) {
            $build->where('business_id', $where['businessId']);
        }
        if (!empty($where['categoryId'])) {
            $build->where('category_id', $where['categoryId']);
        }

        $list = $build->get();
        if (empty($list)) {
            return [];
        }

        return $list->toArray();
    }


}