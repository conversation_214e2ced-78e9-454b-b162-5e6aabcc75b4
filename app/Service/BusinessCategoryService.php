<?php

namespace App\Service;


use App\Constants\CommonConst;
use App\Models\BusinessCategoryModel;
use App\Utils\Singleton;

class BusinessCategoryService
{
    use Singleton;


    /**
     * 根据业务标识获取分类
     *
     * @param string $identifier
     * @param int $businessId
     * @return BusinessCategoryModel|null
     */
    public function getCategoryByIdentifier(string $identifier, int $businessId)
    {
        return BusinessCategoryModel::query()
            ->where('state', CommonConst::STATE_ACTIVE)
            ->where('category_identifier', $identifier)
            ->where('business_id', $businessId)
            ->first();
    }

    public function add($params)
    {
        $model = new BusinessCategoryModel();

        $model->business_id = $params['businessId'];
        $model->category_name = $params['categoryName'];
        $model->category_identifier = $params['categoryIdentifier'];
        $model->create_time = time();

        return $model->save();
    }

    public function getOneById($id)
    {
        $fields = ['id', 'business_id', 'category_name', 'category_identifier','state', 'create_time'];
        $info = BusinessCategoryModel::query()
            ->select($fields)
            ->where('id', $id)
            ->first();
        if (!empty($info)) {
            return $info->toArray();
        }
        return [];
    }

    public function getListByBusinessId($businessId)
    {
        $fields = ['id', 'business_id as businessId', 'category_name as categoryName', 'category_identifier as categoryIdentifier', 'state', 'create_time as createTime'];
        return BusinessCategoryModel::query()
            ->select($fields)
            ->where('business_id', $businessId)
            ->get()
            ->toArray();
    }

    public function getList()
    {
        $fields = ['id', 'business_id as businessId', 'category_name as categoryName', 'category_identifier as categoryIdentifier', 'state', 'create_time as createTime'];
        return BusinessCategoryModel::query()
            ->select($fields)
            ->get()
            ->toArray();
    }

    public function editById($id, $params)
    {
        $update = [
            'category_name' => $params['categoryName'],
            'category_identifier' => $params['categoryIdentifier'],
        ];
        if (isset($params['state'])) {
            $update['state'] = $params['state'];
        }

        return BusinessCategoryModel::query()
            ->where('id', $id)
            ->update($update);
    }

    /**
     * 根据id批量获取
     *
     * @param array $ids
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getListByIds(array $ids)
    {
        return BusinessCategoryModel::query()->whereIn('id', $ids)->get();
    }
}

