<?php

namespace App\Service;

use App\Utils\Vibranium;
use Spin\Logger\Facades\Log;

class AsyncService
{
    public static function syncIdentResultNotify($uri, $retry = 1, $delayTime = 0)
    {
        //发送异步任务
        $taskName = "sync-ident-result-notify";

        // 推送悟空任务
        $payload = [
            'php',
            '/data/www/image-ident-server.weipaitang.com/artisan',
            'notify:ident-result',
            (string)$uri,
            (string)$retry,
        ];
        $result = Vibranium::task('k8s-crond-image-ident-server', $taskName, $delayTime, $payload);
        Log::info(
            'asyncService',
            '投递鉴定结果通知',
            [
                'uri' => $uri,
                'retry' => $retry,
                'result' => $result,
                'resultStatus' => (boolean)$result,
            ]
        );
    }

    /**
     * 海外图片上传
     *
     * @param $uri
     * @param $retry
     * @param $delayTime
     * @return void
     */
    public static function syncOverseaImg($uri, $retry = 0, $delayTime = 0)
    {
        //发送异步任务
        $taskName = "sync-oversea-img";

        // 推送悟空任务
        $payload = [
            'php',
            '/data/www/image-ident-server.weipaitang.com/artisan',
            'sync:oversea-img',
            (string)$uri,
            (string)$retry,
        ];
        $result = Vibranium::task('k8s-crond-image-ident-server', $taskName, $delayTime, $payload);
        Log::info(
            'asyncService',
            '海外图片上传',
            [
                'uri' => $uri,
                'retry' => $retry,
                'result' => $result,
                'resultStatus' => (boolean)$result,
            ]
        );
    }
}
