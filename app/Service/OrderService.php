<?php


namespace App\Service;


use App\Constants\BusinessConst;
use App\Constants\OrderConst;
use App\Constants\TemplateConst;
use App\Models\OrderItemFieldModel;
use App\Models\OrderItemModel;
use App\Models\OrderModel;
use App\Params\Order\OrderParams;
use App\Utils\Singleton;

/**
 * 订单服务
 *
 * Class OrderService
 * @package App\Service
 */
class OrderService
{
    use Singleton;

    /**
     * 创建订单
     *
     * @param OrderParams $params
     * @param array $otherParams
     * @return int
     * @throws \Throwable
     */
    public function createOrder(OrderParams $params, array $otherParams = [])
    {
        $detailJson = [];
        if ($params->rejectUri) {
            $detailJson['rejectUri'] = $params->rejectUri;
        }
        $uri = $otherParams['uri'];
        $orderData = [
            'uri' => $uri,
            'userinfo_id' => $params->appraiserId,
            'business_master_no' => $params->businessMasterNo,
            'business_no' => $params->businessNo,
            'business_id' => $params->businessId,
            'category_id' => $params->categoryId,
            'category_identifier' => $params->categoryIdentifier,
            'input_template_id' => $params->inputTemplateId,
            'output_template_id' => $params->outputTemplateId,
            'end_time' => $params->endTime,
            'accept_time' => $params->appraiserId ? time() : 0,
            'cover' => $params->itemList[0]['imgs'][0] ?? '',
            'ident_truth' => 99,
            'sub_type' => $params->subType,
            'detail_json' => wpt_json_encode($detailJson),
            'state' => $params->appraiserId ? OrderConst::ORDER_STATE_WAIT_IDENTIFY : OrderConst::ORDER_STATE_WAIT_DISTRIBUTION,
            'create_time' => time(),
        ];
        $conn = OrderModel::query()->getConnection();
        $conn->beginTransaction();
        $orderId = OrderModel::query()->insertGetId($orderData);
        if (empty($orderId)) {
            $conn->rollBack();
            return 0;
        }

        // 创建物品
        foreach ($params->itemList as $key => $item) {
            $orderItemData = [
                'order_id' => $orderId,
                'order_uri' => $uri,
                'imgs' => is_array($item['imgs']) ? wpt_json_encode($item['imgs']) : $item['imgs'],
                'video' => is_array($item['video']) ? wpt_json_encode($item['video']) : $item['video'],
                'remark' => $item['remark'],
                'create_time' => time(),
            ];
            $orderItemId = OrderItemModel::query()->insertGetId($orderItemData);

            if (empty($orderItemId)) {
                $conn->rollBack();
                return 0;
            }
            $params->itemList[$key]['orderItemId'] = $orderItemId;
        }

        // 插入属性
        $fieldList = [];
        foreach ($params->itemList as $item) {
            foreach ($item['fieldList'] as $field) {
                $fieldList[] = [
                    'order_id' => $orderId,
                    'order_item_id' => $item['orderItemId'],
                    'biz_type' => OrderConst::BIZ_TYPE_INPUT,
                    'field_id' => $field['fieldId'],
                    'field_name' => $field['fieldName'],
                    'field_key' => $field['fieldKey'],
                    'field_value' => $field['fieldValue'],
                    'field_type' => $field['fieldType'],
                    'output_type' => OrderConst::OUTPUT_TYPE_ORDER_ITEM,
                    'create_time' => time(),
                ];
            }
        }

        if ($fieldList) {
           $fieldRet = OrderItemFieldModel::query()->insert($fieldList);
           if (empty($fieldRet)) {
               $conn->rollBack();
               return 0;
           }
        }
        $orderItemFieldList = [];
        foreach ($params->orderItem as $field) {
            $orderItemFieldList[] = [
                'order_id' => $orderId,
                'order_item_id' => 0,
                'biz_type' => OrderConst::BIZ_TYPE_INPUT,
                'field_id' => $field['fieldId'],
                'field_name' => $field['fieldName'],
                'field_key' => $field['fieldKey'],
                'field_value' => $field['fieldValue'],
                'field_type' => $field['fieldType'],
                'output_type' => OrderConst::OUTPUT_TYPE_ORDER,
                'create_time' => time(),
            ];
        }
        if ($orderItemFieldList) {
            $fieldRet = OrderItemFieldModel::query()->insert($orderItemFieldList);
            if (empty($fieldRet)) {
                $conn->rollBack();
                return 0;
            }
        }

        $conn->commit();
        return $orderId;
    }

    /**
     * 获取输入订单数据
     *
     * @param OrderModel $order
     * @return array
     */
    public function getOrderInputData(OrderModel $order)
    {
        $resultList = [];
        $orderItems = OrderItemModel::query()->where('order_id', $order->id)->get();
        $inputFieldList = $this->getFieldList($order->id, TemplateConst::BIZ_TYPE_INPUT);

        $template = TemplateService::getInstance()->getTemplateDetail($order->input_template_id);
        $templateFields = $template['fields'] ?? [];
        $inputFileConfig = array_column($templateFields, null, 'fieldKey');
        /** @var OrderItemModel $item */
        foreach ($orderItems as $item) {
            $fieldList = [];
            // 固定参数如果未配置拼接
            $fieldList[] = $this->buildInputFieldItem('imgs', json_decode($item->imgs, true), $order->business_id, $inputFileConfig['imgs'] ?? []);
            $fieldList[] = $this->buildInputFieldItem('video', json_decode($item->video, true), $order->business_id, $inputFileConfig['video'] ?? []);
            $fieldList[] = $this->buildInputFieldItem('remark', $item->remark, $order->business_id, $inputFileConfig['remark'] ?? []);

            foreach ($inputFieldList as $field) {
                if ($field['orderItemId'] == $item->id) {
                    $fieldList[] = $field;
                }
            }

            foreach ($fieldList as $key => $field) {
                if (empty($field['fieldValue'])) {
                    unset($fieldList[$key]);
                }
            }

            $resultList[] = array_values($fieldList);
        }

        $orderFieldList = [];
        foreach ($inputFieldList as $field) {
            if ($field['orderItemId'] == 0) {
                $orderFieldList[] = $field;
            }
        }
        return [
            'items' => $resultList,
            'order' => $orderFieldList,
        ];
    }

    /**
     * 获取订单输出数据
     *
     * @param OrderModel $order
     * @return array[]
     */
    public function getOrderOutputData(OrderModel $order)
    {
        if ($order->state != OrderConst::ORDER_STATE_COMPLETE) {
            return null;
        }
        $outputFieldList = $this->getFieldList($order->id, OrderConst::BIZ_TYPE_OUTPUT);
        if ($outputFieldList) {
            $fieldIds = array_column($outputFieldList, 'fieldId');
            array_multisort($fieldIds, SORT_ASC, $outputFieldList);
        }
        $orderItems = OrderItemModel::query()->where('order_id', $order->id)->get();

        $resultList = [];
        /** @var OrderItemModel $item */
        foreach ($orderItems as $item) {
            $fieldList = [];
            foreach ($outputFieldList as $field) {
                if ($field['orderItemId'] == $item->id) {
                    if ($field['fieldType'] == 'evaluateAttr') {
                        $field['fieldValue'] = json_decode($field['fieldValue'], true);
                    }
                    $fieldList[] = $field;
                }
            }

            $resultList[] = $fieldList;
        }

        $orderFieldList = [];
        foreach ($outputFieldList as $field) {
            if ($field['orderItemId'] == 0) {
                $orderFieldList[] = $field;
            }
        }

        //获取模板信息
        $template = TemplateService::getInstance()->getTemplateDetail($order->output_template_id);
        $templateFields = $template['fields'] ?? [];
        $identResultConfig = array_column($templateFields, null, 'fieldKey')['identResult'] ?? [];

        $identResultField = $this->buildOutputIdentResultItem($order->ident_result, $order->business_id, $identResultConfig);
        if ($identResultField) {
            $orderFieldList[] = $identResultField;
        }

        $identTruthField = $this->buildOutputIdentTruthItem($order->ident_truth, $order->business_id);
        if ($identTruthField) {
            $orderFieldList[] = $identTruthField;
        }

        return [
            'items' => $resultList,
            'order' => $orderFieldList,
        ];
    }

    /**
     * 构建输出结果字段
     *
     * @param $identResult
     * @param $businessId
     * @param $identResultConfig
     * @return array
     */
    private function buildOutputIdentResultItem($identResult, $businessId, $identResultConfig)
    {
        if (!$identResultConfig) {
            $fieldName = TemplateConst::OUTPUT_COMMON_RESULT[$businessId]['fieldName'] ?? '';
        }  else {
            $fieldName = $identResultConfig['fieldName'];
        }

        if (empty($fieldName)) {
            return [];
        }

        // 或者配置名称
        return [
            'bizType' => TemplateConst::BIZ_TYPE_OUTPUT,
            'orderItemId' => 0,
            'fieldId' => 0,
            'fieldName' => $fieldName,
            'fieldKey' => 'identResult',
            'fieldValue' => $identResult,
            'fieldType' => 'textarea',
            'outputType' => OrderConst::OUTPUT_TYPE_ORDER,
        ];
    }

    /**
     * 构建输出真值字段
     *
     * @param $identTruth
     * @param $businessId
     * @return array
     */
    private function buildOutputIdentTruthItem($identTruth, $businessId)
    {
        // 业务是否配置
        if ($identTruth == 99) {
            return [];
        }
        $fieldName = TemplateConst::OUTPUT_COMMON_TRUTH[$businessId]['fieldName'] ?? '';
        // 或者配置名称
        return [
            'bizType' => TemplateConst::BIZ_TYPE_OUTPUT,
            'orderItemId' => 0,
            'fieldId' => 0,
            'fieldName' => $fieldName,
            'fieldKey' => 'identTruth',
            'fieldValue' => $identTruth,
            'fieldValueDesc' => empty(TemplateConst::TRUTH_DESC[$businessId][$identTruth]) ? (TemplateConst::TRUTH_DESC['common'][$identTruth] ?? '') : TemplateConst::TRUTH_DESC[$businessId][$identTruth],
            'fieldType' => 'iconSelect',
            'outputType' => OrderConst::OUTPUT_TYPE_ORDER,
        ];
    }

    /**
     * 固定参数拼接
     *
     * @param $key
     * @param $value
     * @param $businessId
     * @param array $inputFieldConfig
     * @return array
     */
    private function buildInputFieldItem($key, $value, $businessId, array $inputFieldConfig = [])
    {
        if (empty($inputFieldConfig)) {
            $inputCommonConfig = TemplateConst::INPUT_COMMON_ITEM;
            $config = $inputCommonConfig[$businessId] ?? [];
            if (empty($config)) {
                $config = $inputCommonConfig['common'];
            }
            $inputFieldConfig = $config[$key];
        }
        return [
            'bizType' => TemplateConst::BIZ_TYPE_INPUT,
            'orderItemId' => 0,
            'fieldId' => 0,
            'fieldName' => $inputFieldConfig['fieldName'],
            'fieldKey' => $key,
            'fieldValue' => $value,
            'fieldType' => $inputFieldConfig['fieldType'],
            'outputType' => OrderConst::OUTPUT_TYPE_ORDER_ITEM,
        ];
    }

    /**
     * 获取提交参数
     *
     * @param OrderModel $order
     * @return array|mixed
     */
    public function getOrderSubmitParams(OrderModel $order)
    {
        $template = TemplateService::getInstance()->getTemplateDetail($order->output_template_id);
        $fields = $template['fields'] ?? [];

        // 增加常量配置
        $identResultConfig = array_column($fields, null, 'fieldKey')['identResult'] ?? [];
        $resultField = $this->getTemplateResult($order->business_id, $order->category_identifier, $identResultConfig);
        if ($resultField) {
            $fields = array_merge($fields, [$resultField]);
        }
        $truthField = $this->getTemplateTruth($order->business_id, $order->category_identifier);
        if ($truthField) {
            $fields = array_merge($fields, [$truthField]);
        }

        // 先按sort排序，再按id排序，如果id为0，则放到后面
        usort($fields, function($a, $b) {
            // 先按sort排序（如果sort存在）
            $sortA = isset($a['sort']) && !empty($a['sort']) ? $a['sort'] : 0;
            $sortB = isset($b['sort']) && !empty($b['sort']) ? $b['sort'] : 0;

            if ($sortA != $sortB) {
                return $sortA - $sortB;
            }

            // 如果sort相同，则按id排序，将id=0的放到后面
            $idA = $a['id'] ?? 0;
            $idB = $b['id'] ?? 0;

            // 如果两个都是id=0，保持原来的顺序
            if ($idA == 0 && $idB == 0) {
                return 0;
            }

            // 如果只有一个id=0，则将其放到后面
            if ($idA == 0) {
                return 1;
            }
            if ($idB == 0) {
                return -1;
            }

            // 如果都不是id=0，则按id升序排序
            return $idA - $idB;
        });

        return $fields;
    }

    private function getTemplateResult($businessId, $categoryIdentifier, $identResultConfig)
    {
        if ($identResultConfig) {
            return [];
        }

        $resultList = TemplateConst::OUTPUT_COMMON_RESULT;
        if (empty($resultList[$businessId])) {
            return [];
        }

        $businessResult = $resultList[$businessId];
        if (empty($businessResult[$categoryIdentifier]) && empty($businessResult['common'])) {
            return [];
        }

        $config = $businessResult[$categoryIdentifier] ?? $businessResult['common'];

        return [
            'id' => 0,
            'parentId' => 0,
            'parentOptionName' => '',
            'fieldId' => 0,
            'fieldType' => 'textarea',
            'minLength' => $config['minLength'] ?? 0,
            'maxLength' => $config['maxLength'] ?? 2000,
            'fieldName' => $config['fieldName'],
            'fieldKey' => 'identResult',
            'placeholder' => $config['placeholder'] ?? '',
            'outputType' => OrderConst::OUTPUT_TYPE_ORDER,
            'isRequired' => true,
            'sort' => $config['sort'] ?? 0,
            'optionsList' => [],
        ];
    }

    /**
     * 鉴定结果
     *
     * @param $businessId
     * @param $categoryIdentifier
     * @return array
     */
    private function getTemplateTruth($businessId, $categoryIdentifier)
    {
        $truthList = TemplateConst::OUTPUT_COMMON_TRUTH;
        if (empty($truthList[$businessId])) {
            return [];
        }
        $businessTruth = $truthList[$businessId];
        if (empty($businessTruth[$categoryIdentifier]) && empty($businessTruth['common'])) {
            return [];
        }

        $config = $businessTruth[$categoryIdentifier] ?? $businessTruth['common'];
        return [
            'id' => 0,
            'parentId' => 0,
            'parentOptionName' => '',
            'fieldId' => 0,
            'fieldType' => $businessId == BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE ? 'textSelect' : 'iconSelect',
            'fieldName' => $businessTruth['fieldName'],
            'fieldKey' => 'identTruth',
            'placeholder' => '',
            'maxLength' => 0,
            'outputType' => OrderConst::OUTPUT_TYPE_ORDER,
            'isRequired' => true,
            'sort' => $businessTruth['sort'] ?? 0,
            'optionsList' => [],
            'fieldObject' => $config,
        ];
    }

    /**
     * 获取订单字典
     *
     * @param int $orderId
     * @param string $bizType
     * @return array
     */
    public function getFieldList(int $orderId, string $bizType)
    {
        $list = OrderItemFieldModel::query()
            ->where('order_id', $orderId)
            ->where('biz_type', $bizType)
            ->get();
        $resultList = [];
        /** @var OrderItemFieldModel $item */
        foreach ($list as $item) {
            $fieldValue = $item->field_value;
            if ($item->field_key == 'voiceList') {
                $fieldValue = json_decode($fieldValue, true) ?: [];
            }
            $resultList[] = [
                'orderItemId' => $item->order_item_id,
                'bizType' => $item->biz_type,
                'fieldId' => $item->field_id,
                'fieldName' => $item->field_name,
                'fieldKey' => $item->field_key,
                'fieldValue' => $fieldValue,
                'fieldType' => $item->field_type,
                'outputType' => $item->output_type,
            ];
        }

        return $resultList;
    }

    /**
     * 根据uri获取订单详情
     *
     * @param string $uri
     * @return OrderModel|null
     */
    public function getOrderByUri(string $uri)
    {
        return OrderModel::query()->where('uri', $uri)->first();
    }

    /**
     * 根据id更新
     *
     * @param int $id
     * @param array $updateData
     * @return bool|int
     */
    public function updateById(int $id, array $updateData)
    {
        return OrderModel::query()->where('id', $id)->update($updateData);
    }
}
