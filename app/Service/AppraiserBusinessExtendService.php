<?php

namespace App\Service;

use App\Models\AppraiserBusinessExtendModel;
use App\Utils\Singleton;

class AppraiserBusinessExtendService
{
    use Singleton;

    const FIELD_KEY_MAP = [
        // 普通图文
        1 => [
            'offHours' => [
                'name' => '休息时间接单',
                'defaultVal' => 2, // 1开启 2关闭
            ],
        ],
        // 名家鉴定
        2 => [
            'identPrice' => [
                'name' => '鉴定费用',
                'defaultVal' => 0,
            ]
        ],
        // 小法庭
        3 => [
            'reviewType' => [
                'name' => '审验类型',
                'defaultVal' => 0,
            ],
            'identPrice' => [
                'name' => '鉴定费用',
                'defaultVal' => 0,
            ]
        ],
        // 海外图文
        4 => [
            'customerId' => [
                'name' => '海外APP用户id',
                'defaultVal' => 0,
            ],
            'isForeign' => [
                'name' => '是否海外鉴定师',
                'defaultVal' => 0,
            ],
            'isExpert' => [
                'name' => '是否专家',
                'defaultVal' => 0,
            ],
        ],
        // 寄售评估
        5 => [
        ],
        // 寄售查验
        6 => [
            'supportImageConsignment' => [
                'name' => '是否支持图文寄售查验',
                'defaultVal' => 0,
            ],
            'supportRealityConsignment' => [
                'name' => '是否支持实物寄售查验',
                'defaultVal' => 0,
            ],
            'canLiveRecheck' => [
                'name' => '是否支持寄售复审',
                'defaultVal' => 0,
            ],
            'canSecondRecheck' => [
                'name' => '是否支持寄售复核',
                'defaultVal' => 0,
            ],
        ],
    ];
    public function bindBusinessExtends($userinfoId, $businessId, $extends)
    {
        $data = [];
        foreach ($extends as $item) {
            // 校验field_key
            $fieldKey = $item['field_key'];
            if (!$this->checkFieldKey($businessId, $fieldKey)) {
                continue;
            }

            $data[] = [
                'userinfo_id' => $userinfoId,
                'business_id' => $businessId,
                'field_name' => $this->getFieldName($businessId, $fieldKey),
                'field_key' => $fieldKey,
                'field_val' => $item['field_val'],
                'create_time' => time(),
            ];
        }

        return AppraiserBusinessExtendModel::query()->insert($data);
    }

    public function unBindBusinessExtends($userinfoId, $businessId)
    {
        return AppraiserBusinessExtendModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('business_id', $businessId)
            ->delete();
    }

    /**
     * 批量解绑业务
     * @param $userinfoId
     * @param array $businessIds
     *
     */
    public function unBindBusinessExtendsBatch($userinfoId, array $businessIds = [])
    {
        $build = AppraiserBusinessExtendModel::query()
            ->where('userinfo_id', $userinfoId);
        if (!empty($businessIds)) {
            $build->whereIn('business_id', $businessIds);
        }

        return $build->delete();
    }

    public function getListByUserinfoId($userinfoId): array
    {
        $build = AppraiserBusinessExtendModel::query()
            ->where('userinfo_id', $userinfoId);

        $list = $build->get();
        if (empty($list)) {
            return [];
        }
        return $list->toArray();
    }

    public function getListByUserinfoIds($userinfoIds)
    {
        $build = AppraiserBusinessExtendModel::query()
            ->whereIn('userinfo_id', $userinfoIds);

        $list = $build->get();
        if (empty($list)) {
            return [];
        }
        return $list->toArray();
    }

    /**
     * 根据扩展属性值，获取数据
     *
     * @param array $userinfoIdList
     * @param $businessId
     * @param array $extendsParams
     * @return array
     */
    public function getListByField(array $userinfoIdList, $businessId, array $extendsParams): array
    {
        $list = AppraiserBusinessExtendModel::query()
            ->select(['id', 'userinfo_id', 'business_id', 'field_key', 'field_name', 'field_val', 'create_time'])
            ->whereIn('userinfo_id', $userinfoIdList)
            ->where('business_id', $businessId)
            ->get()->toArray();
        if (empty($list)) {
            return [];
        }

        $resultByUid = [];
        foreach ($list as $extend) {
            $resultByUid[$extend['userinfo_id']][$extend['field_key']] = $extend;
        }
        //dd($resultByUid);
        // 当前业务扩展字段配置
        $fillFieldList = [];
        $fieldKeys = $this->getFieldKeys($businessId);
        foreach ($fieldKeys as $fieldKey => $item) {
            $fillFieldList[$fieldKey] = [
                'business_id' => $businessId,
                'field_key' => $fieldKey,
                'field_name' => $item['name'],
                'field_val' => $item['defaultVal']
            ];
        }

        // 不在里面的用户，补充上
        $fillUserinfoIdList = array_diff($userinfoIdList, array_keys($resultByUid));
        foreach ($fillUserinfoIdList as $userinfoId) {
            $resultByUid[$userinfoId] = [];
        }
        //dd($resultByUid);

        // 给每个用户 补充全field_key
        foreach ($resultByUid as $userinfoId => $extends) {
            foreach ($fillFieldList as $fieldKey => $item) {
                if (!array_key_exists($fieldKey, $extends)) {
                    $resultByUid[$userinfoId][$fieldKey] = array_merge(['userinfo_id' => $userinfoId], $item);
                }
            }
        }

        // 没有需要extend条件，直接返回
        if (empty($extendsParams)) {
            return $resultByUid;
        }

        // 过滤出符合条件的extend
        $resultByUidFilter = [];
        foreach ($resultByUid as $userinfoId => $userExtends) {
            $resultByUidFilter[$userinfoId] = array_filter($userExtends, function ($userExtend) use ($userinfoId, $extendsParams) {
                return array_filter($extendsParams, function ($extendParam) use ($userExtend) {
                    return $userExtend['field_key'] == $extendParam['field_key'] && (string)$userExtend['field_val'] == (string)$extendParam['field_val'];
                });
            });
        }
        // 过滤出完全符合的用户
        $resultByUidFilter = array_filter($resultByUidFilter, function ($extend) use ($extendsParams) {
            return count($extend) == count($extendsParams);
        });


        // 最后拼接上其他默认属性
        $result = [];
        foreach ($resultByUidFilter as $userinfoId => $extends) {
            $result[$userinfoId] = array_values($resultByUid[$userinfoId]);
        }
        return $result;
    }

    /**
     * 校验field_key
     * @param $businessId
     * @param $fieldKey
     * @return bool
     */
    public function checkFieldKey($businessId, $fieldKey): bool
    {
        $fieldKeyMap = self::FIELD_KEY_MAP[$businessId] ?? [];
        if (in_array($fieldKey, array_keys($fieldKeyMap))) {
            return true;
        }

        return false;
    }
    public function getFieldName($businessId, $fieldKey)
    {
        return self::FIELD_KEY_MAP[$businessId][$fieldKey]['name'] ?? '';
    }

    public function getFieldKeys($businessId)
    {
        return self::FIELD_KEY_MAP[$businessId] ?? [];
    }
}