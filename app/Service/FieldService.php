<?php


namespace App\Service;


use App\Models\BusinessFieldModel;
use App\Models\BusinessFieldOptionsModel;
use App\Utils\Singleton;

/**
 * 字段(字典)服务
 *
 * Class FieldService
 * @package App\Service
 */
class FieldService
{
    use Singleton;

    /**
     * 创建字典
     *
     * @param array $fieldData
     * @param array $optionsData
     * @return int
     */
    public function create(array $fieldData, array $optionsData = [])
    {
        $fieldId = BusinessFieldModel::query()->insertGetId($fieldData);
        if (empty($fieldId)) {
            return 0;
        }
        if ($optionsData) {
            // 使用 array_map 添加 field_id 键
            $optionsData = array_map(function ($option) use ($fieldId) {
                $option['field_id'] = $fieldId; // 设置 field_id 的值
                return $option;
            }, $optionsData);

            BusinessFieldOptionsModel::query()->insert($optionsData);
        }

        return $fieldId;
    }


    /**
     * 批量获取
     *
     * @param array $ids
     * @return array
     */
    public function getFieldByIds(array $ids)
    {
        $fieldList = BusinessFieldModel::query()->whereIn('id', $ids)->get();
        $options = BusinessFieldOptionsModel::query()
            ->whereIn('field_id', $ids)
            ->get()
            ->groupBy('field_id');
        $resultList = [];
        foreach ($fieldList as $field) {
            $optionsList = [];
            if ($options->has($field->id)) {
                $recordsForFieldId = $options[$field->id];
                foreach ($recordsForFieldId as $record) {
                    $optionsList[] = [
                        'optionName' => $record->option_name,
                        'optionImg' => $record->option_img,
                    ];
                }
            }
            $resultList[] = [
                'id' => $field->id,
                'name' => $field->name,
                'fieldKey' => $field->field_key,
                'fieldType' => $field->field_type,
                'placeholder' => $field->placeholder,
                'minLength' => $field->min_length,
                'maxLength' => $field->max_length,
                'bizType' => $field->biz_type,
                'isRequired' => $field->is_required,
                'state' => $field->state,
                'isDeleted' => $field->is_deleted,
                'optionsList' => $optionsList,
            ];
        }

        return $resultList;
    }

    /**
     * 获取字段配置
     *
     * @param int $fieldId
     * @return array
     */
    public function getFieldById(int $fieldId): array
    {
        $field = BusinessFieldModel::query()->where('id', $fieldId)->first();
        if (empty($field)) {
            return [];
        }
        $optionsList = [];
        $options = BusinessFieldOptionsModel::query()->where('field_id', $fieldId)->get();
        /** @var BusinessFieldOptionsModel $item * */
        foreach ($options as $item) {
            $optionsList[] = [
                'optionName' => $item->option_name,
                'optionImg' => $item->option_img,
            ];
        }
        return [
            'id' => $fieldId,
            'name' => $field->name,
            'fieldKey' => $field->field_key,
            'fieldType' => $field->field_type,
            'placeholder' => $field->placeholder,
            'maxLength' => $field->max_length,
            'bizType' => $field->biz_type,
            'isRequired' => $field->is_required,
            'state' => $field->state,
            'isDeleted' => $field->is_deleted,
            'optionsList' => $optionsList,
        ];
    }

    /**
     * 更新
     *
     * @param int $id
     * @param array $data
     * @return bool|int
     */
    public function updateFieldById(int $id, array $data)
    {
        return BusinessFieldModel::query()->where('id', $id)->update($data);
    }
}
