<?php

namespace App\Service\Community;

use App\Models\Community\AppraiserModel;
use App\Utils\Singleton;

class AppraiserService
{
    use Singleton;

    /**
     * 获取社区的评估师列表
     *
     * @param $communityId
     * @return array
     */
    public function getAllCommunityAppraiser(): array
    {
        $appraiserList = AppraiserModel::query()
            ->select(['userinfoId', 'avatar', 'nickname', 'description'])
            ->where('isDeleted', 0)
            ->where('appraiserLevel', 1)
            ->get();
        if (empty($appraiserList)) {
            return [];
        }

        return $appraiserList->toArray();
    }

    public function getListByUserinfoIds(array $userinfoIdList): array
    {
        $appraiserList = AppraiserModel::query()
            ->leftJoin('appraiser_extend', 'appraiser_extend.appraiser_id', '=', 'appraiser.id')
            ->select([
                'appraiser.id',
                'appraiser.userinfoId',
                'appraiser.avatar',
                'appraiser.nickname',
                'appraiser.description',    // 介绍
                'appraiser.profile',    // 个人简介
                'appraiser.portrait_photo', // 形象照
                'appraiser.appraiser_type', // (1全职,2兼职)
                'appraiser_extend.trainee_state', //  1非练习生 2练习生',
            ])
            ->where('isDeleted', 0)
            ->where('appraiserLevel', 1)
            ->whereIn('userinfoId', $userinfoIdList)
            ->get();
        if (empty($appraiserList)) {
            return [];
        }

        foreach ($appraiserList as &$appraiserItem) {
            $appraiserItem['avatar'] = combineImgUrl($appraiserItem['avatar']);
            $appraiserItem['portrait_photo'] = combineImgUrl($appraiserItem['portrait_photo']);
        }

        return $appraiserList->toArray();
    }

}