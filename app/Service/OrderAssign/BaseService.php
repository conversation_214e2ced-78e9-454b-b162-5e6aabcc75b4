<?php

namespace App\Service\OrderAssign;

use App\Constants\AppraiserConst;
use App\Constants\CommonConst;
use App\Constants\LogConst;
use App\Logic\Inner\AppraiserDispatchLogic;
use App\Models\AppraiserBusinessCategoryModel;
use App\Models\AppraiserBusinessExtendModel;
use App\Service\AppraiserService;
use Spin\Logger\Facades\Log;

class BaseService
{


    private function getMatchBizAndCategoryList($businessId, $category, $extendParams)
    {
        // 符合业务类目的鉴定师
        $where = [
            ["field_key" => "businessId", "field_val" => $businessId],
            ["field_key" => "category", "field_val" => $category],
            //["field_key" => "supportImageConsignment", "field_val" => 1],
        ];
        // 拼接上其他参数
        foreach ($extendParams as $key => $val) {
            $where[] = ["field_key" => $key, "field_val" => $val];
        }

        $appraiserList = AppraiserDispatchLogic::getInstance()->list($where);
        Log::info(LogConst::ORDER_ASSIGN, '搜索满足权限的鉴定师列表', ['where' => $where, 'result' => $appraiserList]);

        if (empty($appraiserList['list'])) {
            return [];
        }

        $userinfoIdList = array_column($appraiserList['list'], 'userinfoId');
        Log::info(LogConst::ORDER_ASSIGN, '搜索满足权限的鉴定师Id列表', ['where' => $where, 'result' => $userinfoIdList]);

        return $userinfoIdList;
    }

    /**
     * 过滤鉴定师 - 全职or兼职
     *
     * @param $userinfoIdList
     * @param $appraiserTypeParams
     * @return array
     */
    private function filterAppraiserByWorkType($userinfoIdList, $appraiserTypeParams)
    {
        $userinfoList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIdList);
        Log::info(LogConst::ORDER_ASSIGN, 'wortType过滤鉴定师列表', ['userinfoIdList' => $userinfoIdList, 'appraiserTypeParams' => $appraiserTypeParams, 'userinfoList', 'result' => $userinfoList]);

        // 根据条件过滤
        $userinfoList = array_filter($userinfoList, function ($item) use ($appraiserTypeParams) {
            // 留下全职
            if (($appraiserTypeParams['workType'] ?? -1) == AppraiserConst::IS_FULL_TIME) {
                return $item['workType'] == AppraiserConst::IS_FULL_TIME;
            } elseif (($appraiserTypeParams['workType'] ?? -1) == AppraiserConst::IS_PART_TIME) {
                return $item['workType'] == AppraiserConst::IS_PART_TIME;
            }
        });
        Log::info(LogConst::ORDER_ASSIGN, 'workType过滤鉴定师列表-全职or兼职', ['appraiserTypeParams' => $appraiserTypeParams, 'result' => $userinfoList]);

        if (empty($userinfoList)) {
            return [];
        }

        $userinfoIdList = array_column($userinfoList, 'userinfoId');
        Log::info(LogConst::ORDER_ASSIGN, 'workType过滤鉴定师列表userinfoId-全职or兼职', ['result' => $userinfoIdList]);
        return $userinfoIdList;
    }

    /**
     * 过滤鉴定师 -是否新人(实习)
     *
     * @param $userinfoIdList
     * @param $appraiserTypeParams
     * @return array
     */
    private function filterAppraiserByTrainee($userinfoIdList, $appraiserTypeParams)
    {
        $userinfoList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIdList);
        Log::info(LogConst::ORDER_ASSIGN, 'isTrainee过滤鉴定师列表', ['appraiserTypeParams' => $appraiserTypeParams, 'userinfoIdList' => $userinfoIdList]);

        // 根据条件过滤
        $isTrainee = -1;
        $userinfoList = array_filter($userinfoList, function ($item) use ($appraiserTypeParams, &$isTrainee) {
            // 留下全职
            if (($appraiserTypeParams['isTrainee'] ?? -1) == AppraiserConst::IS_TRAINEE) {
                $isTrainee = true;
                return $item['isTrainee'] == AppraiserConst::IS_TRAINEE;
            } elseif (($appraiserTypeParams['isTrainee'] ?? -1) == AppraiserConst::IS_NOT_TRAINEE) {
                $isTrainee = false;
                return $item['isTrainee'] == AppraiserConst::IS_NOT_TRAINEE;
            }
        });
        Log::info(LogConst::ORDER_ASSIGN, 'isTrainee过滤鉴定师列表-' . $isTrainee ? "学徒" : "非学徒", ['appraiserTypeParams' => $appraiserTypeParams, 'result' => $userinfoList]);

        //dd($userinfoList);
        if (empty($userinfoList)) {
            return [];
        }

        $userinfoIdList = array_column($userinfoList, 'userinfoId');
        Log::info(LogConst::ORDER_ASSIGN, 'isTrainee过滤鉴定师列表userinfoId-' . $isTrainee ? "学徒" : "非学徒", ['appraiserTypeParams' => $appraiserTypeParams, 'result' => $userinfoIdList]);

        return $userinfoIdList;
    }

    /**
     * 检验鉴定师的类目和业务权限
     *
     * @param $userinfoId
     * @param $businessId
     * @param $category
     * @param $extendParams
     * @return array|mixed
     */
    private function filterAppraiserBizAndCategory($userinfoId, $businessId, $category, $extendParams)
    {
        // 是否符合类目和实物权限
        $matchCategory = AppraiserBusinessCategoryModel::query()
            ->where('state', CommonConst::STATE_ACTIVE)
            ->where('userinfo_id', $userinfoId)
            ->where('business_id', $businessId)
            ->where('category_id', $category)
            ->first();
        Log::info(LogConst::ORDER_ASSIGN, '检验鉴定师的类目和业务权限', ['userinfoId' => $userinfoId, 'businessId' => $businessId, 'category' => $category, 'extendParams' => $extendParams, 'result' => $matchCategory]);
        if (empty($matchCategory)) {
            return [];
        }

        //  是否寄售下实物or图文权限
        $extendList = AppraiserBusinessExtendModel::query()
            ->where('state', CommonConst::STATE_ACTIVE)
            ->where('userinfo_id', $userinfoId)
            ->where('business_id', $businessId)
            ->get()
            ->toArray();

        // 其他额外属性判断
        if (!empty($extendParams)) {
            $matchNum = 0;
            foreach ($extendParams as $key => $val) {
                foreach ($extendList as $extend) {
                    if ($extend['field_key'] == $key && (string)$extend['field_val'] == (string)$val) {
                        $matchNum ++;
                    }
                }
            }
            Log::info(LogConst::ORDER_ASSIGN, '检验鉴定师的其他权限', ['userinfoId' => $userinfoId, 'businessId' => $businessId, 'extendParams' => $extendParams, 'matchNum' => $matchNum]);

            if ($matchNum != count($extendParams)) {
                return [];
            }
        }

        return $userinfoId;
    }

    public function getMatchBizAndCategoryImageList($businessId, $category)
    {
        $extendParams = [
            'supportImageConsignment' => 1
        ];
        return $this->getMatchBizAndCategoryList($businessId, $category, $extendParams);
    }
    public function getMatchBizAndCategoryRealityList($businessId, $category)
    {
        $extendParams = [
            'supportRealityConsignment' => 1
        ];
        return $this->getMatchBizAndCategoryList($businessId, $category, $extendParams);
    }
    public function getMatchBizAndCategoryRealityRecheckList($businessId, $category)
    {
        $extendParams = [
            'supportRealityConsignment' => 1,
            'canLiveRecheck' => 1,  // 复审权限
        ];
        return $this->getMatchBizAndCategoryList($businessId, $category, $extendParams);
    }

    /**
     * 检验鉴定师的类目和业务权限 - 图文
     *
     * @param $userinfoId
     * @param $businessId
     * @param $category
     * @return array|mixed
     */
    public function filterAppraiserBizAndCategoryImage($userinfoId, $businessId, $category)
    {
        $extendParams = [
            'supportImageConsignment' => 1, // 图文查验权限
        ];
        return $this->filterAppraiserBizAndCategory($userinfoId, $businessId, $category, $extendParams);
    }
    public function filterAppraiserBizAndCategoryReality($userinfoId, $businessId, $category)
    {
        $extendParams = [
            'supportRealityConsignment' => 1
        ];
        return $this->filterAppraiserBizAndCategory($userinfoId, $businessId, $category, $extendParams);
    }

    /**
     * 过滤鉴定师 - 全职
     *
     * @param $userinfoIdList
     * @return array
     */
    public function filterAppraiserFullTime($userinfoIdList): array
    {
        $appraiserTypeParams = [
            'workType' => AppraiserConst::IS_FULL_TIME
        ];
        return $this->filterAppraiserByWorkType($userinfoIdList, $appraiserTypeParams);
    }
    public function filterAppraiserPartTime($userinfoIdList)
    {
        $appraiserTypeParams = [
            'workType' => AppraiserConst::IS_PART_TIME
        ];
        return $this->filterAppraiserByWorkType($userinfoIdList, $appraiserTypeParams);
    }

    /**
     * 过滤鉴定师 - 新手(实习)
     *
     * @param $userinfoIdList
     * @return array
     */
    public function filterAppraiserIsTrainee($userinfoIdList)
    {
        $appraiserTypeParams = [
            'isTrainee' => AppraiserConst::IS_TRAINEE
        ];
        return $this->filterAppraiserByTrainee($userinfoIdList, $appraiserTypeParams);
    }
    public function filterAppraiserNotTrainee($userinfoIdList)
    {
        $appraiserTypeParams = [
            'isTrainee' => AppraiserConst::IS_NOT_TRAINEE
        ];
        return $this->filterAppraiserByTrainee($userinfoIdList, $appraiserTypeParams);
    }

}