<?php

namespace App\Service\OrderAssign;

use App\Cache\AppraiserCache;
use App\Constants\AppraiserConst;
use App\Constants\BusinessConst;
use App\Constants\LogConst;
use App\Logic\Inner\AppraiserDispatchLogic;
use App\Models\AppraiserBusinessCategoryModel;
use App\Models\AppraiserBusinessExtendModel;
use App\Models\AppraiserBusinessModel;
use App\Models\AppraiserGroupCategoryRelationModel;
use App\Service\AppraiserGroupMemberService;
use App\Service\AppraiserGroupScheduleService;
use App\Service\AppraiserService;
use App\Utils\Singleton;
use Spin\Logger\Facades\Log;

class OrderAssignService extends BaseService
{

    use Singleton;


    /**
     * 图文-全职（当日非休息 & 工作量>0 & 类目符合）
     *
     * @param $businessId
     * @param $category
     *
     */
    public function getImageFullTimeTeacher($businessId, $category, $date)
    {
        // 当前业务类目、开启寄售图文查验
        $userinfoIdList = $this->getMatchBizAndCategoryImageList($businessId, $category);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 全职鉴定师
        $userinfoIdList = $this->filterAppraiserFullTime($userinfoIdList);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 有排班（组长不在排班里面,肯定查不到）
        $userinfoIdList = $this->getFullTimeScheduleTeacher($userinfoIdList, $date);
        if (empty($userinfoIdList)) {
            return [];
        }

        return $userinfoIdList;
    }

    /**
     * 实物-全职（日期内非休息 & 工作量>0 & 类目符合）
     *
     * @param $businessId
     * @param $category
     * @param $date
     * @return array
     */
    public function getRealityFullTimeTeacher($businessId, $category, $date)
    {
        $userinfoIdList = $this->getMatchBizAndCategoryRealityList($businessId, $category);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 全职鉴定师
        $userinfoIdList = $this->filterAppraiserFullTime($userinfoIdList);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 有排班（组长不在排班里面）
        $userinfoIdList = $this->getFullTimeScheduleTeacher($userinfoIdList, $date);
        if (empty($userinfoIdList)) {
            return [];
        }

        // todo 随机返回一个
        return $userinfoIdList;
    }


    /**
     * 实物 全职-指定范围[分组] (次日非休息 & 类目符合)
     *
     * @param $groupUserinfoId
     * @param $date
     * @return array
     */
    public function getRealityFullTimeAssignGroupTeacher($groupUserinfoId, $businessId, $category, $date)
    {
        // 获取全部组员
        $groupMemberList = AppraiserGroupMemberService::getInstance()->getListByUserinfoId($groupUserinfoId);
        $groupMemberUidList = array_column($groupMemberList, 'member_uid');
        //var_dump($groupMemberUidList);

        // 留下全职鉴定师
        $userinfoIdList = $this->filterAppraiserFullTime($groupMemberUidList);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 符合业务类目的
        $appraiserBusinessList = AppraiserBusinessCategoryModel::query()
            ->whereIn('userinfo_id', $userinfoIdList)
            ->where('business_id', $businessId)
            ->where('category_id', $category)
            ->get()
            ->toArray();
        $userinfoIdList = array_column($appraiserBusinessList, 'userinfo_id');
        // 有寄售实物权限
        $matchRealityList = AppraiserBusinessExtendModel::query()
            ->whereIn('userinfo_id', $userinfoIdList)
            ->where('business_id', $businessId)
            ->where('field_key', 'supportRealityConsignment')
            ->where('field_val', 1)
            ->get()
            ->toArray();
        if (empty($matchRealityList)) {
            return [];
        }
        $userinfoIdList = array_column($matchRealityList, 'userinfo_id');

        // 排班,不校验工作量
        return $this->getFullTimeScheduleTeacherWithNoDispatchCount($userinfoIdList, $date);
    }


    /**
     * 图文-兼职（当日非休息 & 工作量>0 & 类目符合）
     *
     * @param $businessId
     * @param $category
     * @return array
     */
    public function getImagePartTimeTeacher($businessId, $category, $date)
    {
        // 符合业务类目、开启寄售图文查验
        $userinfoIdList = $this->getMatchBizAndCategoryImageList($businessId, $category);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 留下兼职鉴定师
        $userinfoIdList = $this->filterAppraiserPartTime($userinfoIdList);

        // 兼职鉴定排班量
        return $this->getPartTimeScheduleTeacher($userinfoIdList, $date);
    }



    /**
     * 图文-指定鉴定师优先 收货人(当日非休息 & 工作量>0 & 类目符合)
     *
     * @param $businessId
     * @param $category
     * @return array
     */
    public function getImageAssignFirstTeacher($userinfoId, $businessId, $category, $date)
    {
        // 是否符合业务类目、图文权限
        $userinfoId = $this->filterAppraiserBizAndCategoryImage($userinfoId, $businessId, $category);
        if (empty($userinfoId)) {
            return [];
        }

        // 鉴定师信息
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            return [];
        }

        //全职鉴定师排班工作量判断
        if ($appraiser['workType'] == AppraiserConst::IS_FULL_TIME) {
            // 有排班
            return $this->getFullTimeScheduleTeacher([$userinfoId], $date);
        }

        // 兼职鉴定师排班工作量判断
        if ($appraiser['workType'] == AppraiserConst::IS_PART_TIME) {
            return $this->getPartTimeScheduleTeacher([$userinfoId], $date);
        }

        return [];
    }

    /**
     * 实物-指定鉴定师优先 收货人(次日非休息 & 工作量>0 & 类目符合)
     *
     * @param $userinfoId
     * @param $businessId
     * @param $category
     * @param $date
     * @return array|void
     */
    public function getRealityAssignFirstTeacher($userinfoId, $businessId, $category, $date)
    {
        // 业务类目 & 实物寄售查验
        $userinfoId = $this->filterAppraiserBizAndCategoryReality($userinfoId, $businessId, $category);
        if (empty($userinfoId)) {
            return [];
        }

        // 鉴定师信息
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            return [];
        }

        // 实物只考虑全职
        // 全职鉴定师排班工作量判断
        if ($appraiser['workType'] == AppraiserConst::IS_FULL_TIME) {
            // 有排班
            return $this->getFullTimeScheduleTeacher([$userinfoId], $date);
        }

        return [];
    }

    /**
     * 实物-指定鉴定师优先-无接单量判断（次日非休息 & 类目符合）
     *
     * @param $userinfoId
     * @param $businessId
     * @param $category
     * @param $date
     * @return array|void
     */
    public function getRealityAssignFirstNoDispatchCountTeacher($userinfoId, $businessId, $category, $date)
    {
        // 是否符合业务类目、实物权限
        $userinfoId = $this->filterAppraiserBizAndCategoryReality($userinfoId, $businessId, $category);
        if (empty($userinfoId)) {
            return [];
        }

        // 鉴定师信息
        $appraiser = AppraiserService::getInstance()->detail($userinfoId);
        if (empty($appraiser)) {
            return [];
        }

        //todo 这里只要全职
        //全职鉴定师排班 & 无工作量判断
        if ($appraiser['workType'] == AppraiserConst::IS_FULL_TIME) {
            // 有排班
            return $this->getFullTimeScheduleTeacherWithNoDispatchCount([$userinfoId], $date);
        }

        return [];
    }


    /**
     * 实物-全职学徒or非学徒（次日非休息 & 工作量>0 & 类目符合）
     *
     * @param $userinfoId
     * @param $businessId
     * @param $category
     * @param $date
     * @return array|void
     */
    public function getRealityFullTimeIfTraineeTeacher($businessId, $category, $date, $isTrainee = true)
    {
        // 业务类目 & 寄售实物查验权限
        $userinfoIdList = $this->getMatchBizAndCategoryRealityList($businessId, $category);

        // 留下全职鉴定师
        $userinfoIdList = $this->filterAppraiserFullTime($userinfoIdList);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 留下学徒or非学徒
        if ($isTrainee) {
            $userinfoIdList = $this->filterAppraiserIsTrainee($userinfoIdList);
        } else {
            $userinfoIdList = $this->filterAppraiserNotTrainee($userinfoIdList);
        }
        if (empty($userinfoIdList)) {
            return [];
        }

        // 有排班（组长不在排班里面）
        return $this->getFullTimeScheduleTeacher($userinfoIdList, $date);
    }

    /**
     * 实物-全职-复审 (日期非休息 & 工作量>0 & 类目符合)
     * @param $businessId
     * @param $category
     * @return array
     * @throws \App\Exceptions\ErrException
     */
    public function getRealityFullTimeRecheckTeacher($businessId, $category, $date)
    {
        // 业务类目 & 实物权限 & 复审权限
        $userinfoIdList = $this->getMatchBizAndCategoryRealityRecheckList($businessId, $category);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 留下全职
        $userinfoIdList = $this->filterAppraiserFullTime($userinfoIdList);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 有排班（组长不在排班里面）
        return $this->getFullTimeScheduleTeacher($userinfoIdList, $date);
    }

    /**
     * 实物-全职-复审（类目符合）
     *
     * @param $businessId
     * @param $category
     * @return array
     * @throws \App\Exceptions\ErrException
     */
    public function getRealityFullTimeRecheckTeacherWithNoDispatchCount($businessId, $category)
    {
        // 业务类目 & 复审权限 & 实物权限
        $userinfoIdList = $this->getMatchBizAndCategoryRealityRecheckList($businessId, $category);
        if (empty($userinfoIdList)) {
            return [];
        }

        // 留下全职
        return $this->filterAppraiserFullTime($userinfoIdList);
    }

    /**
     * 组长（兜底类目符合）
     *
     * @param $businessId
     * @param $category
     * @return array
     */
    public function getDefaultLeaderTeacher($businessId, $category)
    {
        // todo 区分图文or实物权限?? 组长身份没有实物和图文

        // 获取当前业务、当前类目的组长
        $groupList = AppraiserGroupCategoryRelationModel::query()
            ->where('is_deleted', 0)
            ->where('business_id', $businessId)
            ->where('category_id', $category)
            ->get()
            ->toArray();
        if (empty($groupList)) {
            Log::info(LogConst::ORDER_ASSIGN, "组长不存在", ['businessId' => $businessId, 'category' => $category]);
            return [];
        }

        // 取组长id
        $userinfoIdList = array_column($groupList, 'userinfo_id');
        Log::info(LogConst::ORDER_ASSIGN, "兜底组长", ['businessId' => $businessId, 'category' => $category, 'result' => $userinfoIdList]);

        return $userinfoIdList;
    }

    /**
     * 最后兜底 - 李文瑞
     *
     * @return int
     */
    public function getDefaultLastTeacher()
    {
        $userinfoId = is_test() ? 100320267 : 4777450;   // 4777450李文瑞
        Log::info(LogConst::ORDER_ASSIGN, "最后兜底指定鉴定师", ['userinfoId' => $userinfoId]);

        return $userinfoId;
    }

    /**
     * 组长 - 强制指定（无视类目符合）
     * @param $userinfoId  //组长id，唯一表示一个组
     * @return mixed
     */
    public function getForceAssignLeaderTeacher($userinfoId)
    {
        // 无视组长的配置类目
        return $userinfoId;
    }

    /**
     * 排班逻辑-图文全职（有排班且有派单量）
     * 说明：全职鉴定师看排班表，兼职鉴定师无排班看派单量
     *
     * @param $userinfoIdList
     * @param $date
     * @return array
     */
    private function getFullTimeScheduleTeacher($userinfoIdList, $date)
    {
        // 排班情况
        $scheduleList = AppraiserGroupScheduleService::getInstance()->getListByMemberUidsAndAssignmentDate($userinfoIdList, [$date]);
        Log::info(LogConst::ORDER_ASSIGN, "全职排班情况", ['userinfoIdList' => $userinfoIdList, 'date' => $date, 'scheduleList' => $scheduleList[$date] ?? []]);
        if (empty($scheduleList[$date])) {
            return [];
        }

        // 留下工作量可接单的
        $userinfoIdList = [];
        foreach ($scheduleList[$date] as $schedule) {
            //$res = AppraiserCache::addAppraiserDispatchCount($schedule['userinfoId'], $date, 10);
            $hasDispatchCount = AppraiserCache::getAppraiserDispatchCount($schedule['userinfoId'], $date);
            if ($hasDispatchCount < $schedule['assignedVolume']) {
                $userinfoIdList[] = $schedule['userinfoId'];
            }
        }
        Log::info(LogConst::ORDER_ASSIGN, "全职排班-还有工作量", ['date' => $date, 'result' => $userinfoIdList]);

        return $userinfoIdList;
    }

    /**
     * 排班逻辑-图文全职（有排班且无派单量判断）
     *
     * @param $userinfoIdList
     * @param $today
     * @return array
     */
    private function getFullTimeScheduleTeacherWithNoDispatchCount($userinfoIdList, $date)
    {
        // 排班情况
        $scheduleList = AppraiserGroupScheduleService::getInstance()->getListByMemberUidsAndAssignmentDate($userinfoIdList, [$date]);
        Log::info(LogConst::ORDER_ASSIGN, "排班情况", ['userinfoIdList' => $userinfoIdList, 'date' => $date, 'scheduleList' => $scheduleList[$date] ?? []]);
        if (empty($scheduleList[$date])) {
            return [];
        }

        // 有排班的鉴定师，不需判断工作量
        $userinfoIdList = [];
        foreach ($scheduleList[$date] as $schedule) {
            $userinfoIdList[] = $schedule['userinfoId'];
        }
        Log::info(LogConst::ORDER_ASSIGN, "排班-无工作量判断", ['date' => $date, 'result' => $userinfoIdList]);

        return $userinfoIdList;
    }

    /**
     * 派单逻辑-兼职（有派单量）
     * 说明：全职鉴定师无排班看派单量
     *
     * @param $userinfoIdList
     * @param $today
     * @return array
     */
    private function getPartTimeScheduleTeacher($userinfoIdList, $date, $businessId = BusinessConst::BUSINESS_TYPE_CONSIGN_VERIFY)
    {
        // 留下不休息的
        $userinfoList = AppraiserService::getInstance()->listByUserinfoIds($userinfoIdList);

        $checkTime = $this->getRestCheckTime($date);
        $userinfoList = array_filter($userinfoList, function ($item) use ($checkTime) {
            return !($item['restStartTime'] < $checkTime && $checkTime < $item['restEndTime']);
        });
        Log::info(LogConst::ORDER_ASSIGN, "排班逻辑-兼职-不休息", ['userinfoIdList' => $userinfoIdList, 'date' => $date, 'result' => $userinfoList]);

        if (empty($userinfoList)) {
            return [];
        }
        $userinfoIdList = array_column($userinfoList, 'userinfoId');
        $logUserinfoIdList = $userinfoIdList;

        // 留下有排班的且有剩余工作量
        $appraiserBusinessList = AppraiserBusinessModel::query()
            ->where('state', 1)
            ->whereIn('userinfo_id', $userinfoIdList)
            ->where('business_id', $businessId)
            ->where('dispatch_count', '>', 0)
            ->get()
            ->toArray();
        //dd($appraiserBusinessList);

        $userinfoIdList = [];
        foreach ($appraiserBusinessList as $item) {
            $dispatchCount = $item['dispatch_count'];
            $hasDispatchCount = AppraiserCache::getAppraiserDispatchCount($item['userinfo_id'], $date);
            if ($hasDispatchCount < $dispatchCount) {
                $userinfoIdList[] = $item['userinfo_id'];
            }
        }

        Log::info(LogConst::ORDER_ASSIGN, "排班逻辑-兼职-存在工作量", ['userinfoIdList' => $logUserinfoIdList, 'date' => $date, 'result' => $userinfoIdList]);

        // 随机返回一个
        return $userinfoIdList;
    }

    private function getPartTimeScheduleTeacherWithNoDispatchCount($userinfoIdList, $businessId = BusinessConst::BUSINESS_TYPE_CONSIGN_VERIFY)
    {
        //$date = date('Y-m-d');
        $appraiserBusinessList = AppraiserBusinessModel::query()
            ->where('state', 1)
            ->whereIn('userinfo_id', $userinfoIdList)
            ->where('business_id', $businessId)
            ->where('dispatch_count', '>', 0)
            ->get()
            ->toArray();
        //dd($appraiserBusinessList);
        $userinfoIdList = [];
        foreach ($appraiserBusinessList as $item) {
            $userinfoIdList[] = $item['userinfo_id'];
        }

        // 随机返回一个
        return $userinfoIdList;
    }

    /**
     * 兼职是否休息时间搓
     *
     * @param $givenDate
     * @return int
     */
    private function getRestCheckTime($givenDate): int
    {
        //$givenDate = '2025-05-19'; // 这里可以替换为任意日期，如果是今天则写当前日期

        if ($givenDate == date('Y-m-d')) {
            // 如果是今天，获取当前时刻的时间戳
            $checkTime = time();
        } else {
            // 如果不是今天，获取指定日期当前时刻的时间戳
            $currentTime = date('H:i:s');
            $checkTime = strtotime($givenDate . ' ' . $currentTime);
        }

       return $checkTime;
    }


}