<?php

namespace App\Service;

use App\Models\AppraiserGroupCategoryRelationModel;
use App\Models\AppraiserGroupMembersRelationModel;
use App\Utils\Singleton;

class AppraiserGroupMemberService
{
    use Singleton;

    public function add($userinfoId, $memberUids)
    {
        $memberList = [];
        foreach ($memberUids as $memberUid) {
            $memberList[] = [
                'userinfo_id' => $userinfoId,
                'member_uid' => $memberUid,
                'create_time' => time(),
            ];

        }

       return AppraiserGroupMembersRelationModel::query()->insert($memberList);
    }

    /**
     * 删除整个小组
     *
     * @param $userinfoId
     * @return bool|int
     */
    public function delByUserinfoId($userinfoId)
    {
        return AppraiserGroupMembersRelationModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('is_deleted', 0)
            ->update(['is_deleted' => 1]);
    }

    /**
     * 删除某个组员
     *
     * @param $memberUid
     * @return bool|int
     */
    public function delByMemberUid($memberUid)
    {
        return AppraiserGroupMembersRelationModel::query()
            ->where('member_uid', $memberUid)
            ->where('is_deleted', 0)
            ->update(['is_deleted' => 1]);
    }

    /**
     * 获取组员
     *
     * @param $userinfoId
     * @return array
     */
    public function getListByUserinfoId($userinfoId)
    {
        $list = AppraiserGroupMembersRelationModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('is_deleted', 0)
            ->get();
        if (empty($list)) {
            return [];
        }
        return $list->toArray();
    }

    public function getOneByMemberUid($memberUid)
    {
        $item = AppraiserGroupMembersRelationModel::query()
            ->where('member_uid', $memberUid)
            ->where('is_deleted', 0)
            ->orderBy('id', 'desc')
            ->first();
        if (empty($item)) {
            return [];
        }
        return $item->toArray();
    }

    public function getListAll()
    {
        return AppraiserGroupMembersRelationModel::query()
            ->where('is_deleted', 0)
            ->get()
            ->toArray();
    }

}