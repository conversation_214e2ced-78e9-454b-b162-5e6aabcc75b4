<?php

namespace App\Service;

use App\Constants\LogConst;
use App\Models\AppraiserBillModel;
use App\Models\AppraiserModel;
use App\Models\CommissionConfigModel;
use App\Models\CommissionDetailModel;
use App\Models\OrderModel;
use App\Utils\Singleton;
use Spin\Logger\Facades\Log;

class OrderCommissionService
{
    use Singleton;

    /**
     * 订单佣金
     *
     * @param $businessId
     * @param $businessCategoryId
     * @param $userinfoId
     * @param $orderId
     * @param $orderAmount
     * @param $subType
     * @return array|void
     */
    public function orderCommission($businessId, $businessCategoryId, $userinfoId, $orderId, $orderAmount, $subType)
    {
        $config = $this->getCommissionConfig($businessId, $businessCategoryId, $userinfoId, $subType);
        if (empty($config)) {
            return [];
        }

        $commissionAmount = 0;
        // 固定金额
        if ($config->commission_type == 1) {
            $commissionAmount = $config->commission_amount;
        } elseif ($config->commission_type == 2) {
            $commissionAmount = $orderAmount * $config->commission_rate / 100;
        }

        if ($commissionAmount <= 0) {
            Log::error("orderCommission", "佣金金额为0", ['arg' => func_get_args(), 'config' => $config]);
            return [];
        }

        $saveData = [
            'userinfo_id' => $userinfoId,
            'business_id' => $businessId,
            'order_id' => $orderId,
            'commission_type' => $config->commission_type,
            'commission_amount' => $commissionAmount,
            'commission_rate' => $config->commission_rate,
            'order_amount' => $orderAmount,
            'is_settlement' => 0,
            'create_time' => time(),
        ];

        CommissionDetailModel::query()->insert($saveData);
    }

    /**
     * 佣金结算
     *
     * @param $orderId
     * @return array|void
     * @throws \Throwable
     */
    public function orderCommissionSettlement($orderId)
    {
        $detail = CommissionDetailModel::query()->where('order_id', $orderId)->first();
        if (empty($detail)) {
            return [];
        }
        if ($detail->is_settlement == 1) {
            return [];
        }

        // 获取用户金额
        $appraiser = AppraiserModel::query()
            ->where('userinfo_id', $detail->userinfo_id)
            ->orderBy('id', 'desc')
            ->first();
        if (empty($appraiser)) {
            Log::error(LogConst::IMAGE_IDENT_SERVER, '获取鉴定师失败', $detail);
            return;
        }

        $billData = [
            'userinfo_id' => $detail->userinfo_id,
            'correlation_id' => $detail->order_id,
            'transaction_type' => 1,
            'bill_type' => 1,
            'amount' => $detail->commission_amount,
            'before_amount' => $appraiser->total_amount,
            'after_amount' => $detail->commission_amount + $appraiser->total_amount,
            'create_time' => time(),
        ];
        $conn = OrderModel::query()->getConnection();
        $conn->beginTransaction();
        $billId = AppraiserBillModel::query()->insertGetId($billData);
        if (!$billId) {
            $conn->rollBack();
            Log::error(LogConst::IMAGE_IDENT_SERVER, '添加鉴定师账单失败', $billData);
            return;
        }
        $updateRes = AppraiserModel::query()
            ->where('id', $appraiser->id)
            ->update([
                'total_amount' => $detail->commission_amount + $appraiser->total_amount,
                'total_commission' => $appraiser->total_commission + $detail->commission_amount,
            ]);

        if (!$updateRes) {
            $conn->rollBack();
            Log::error(LogConst::IMAGE_IDENT_SERVER, '更新鉴定师余额失败', $billData);
            return;
        }

        $detail->is_settlement = 1;
        $detail->save();
        $conn->commit();
    }

    /**
     * 获取佣金配置
     *
     * @param $businessId
     * @param $businessCategoryId
     * @param $userinfoId
     * @param $subType
     * @return CommissionConfigModel
     */
    private function getCommissionConfig($businessId, $businessCategoryId, $userinfoId, $subType = 0)
    {
        $query = CommissionConfigModel::query()
            ->where('state', 1)
            ->where('is_deleted', 0)
            ->where('business_id', $businessId)
            ->where('business_category_id', $businessCategoryId)
            ->where('userinfo_id', $userinfoId);
        if ($subType) {
            $query->where('sub_type', $subType);
        }
        $config = $query->first();
        if (empty($config)) {
            return [];
        }

        return $config;
    }
}
