<?php

namespace App\Service;

use App\Constants\CommonConst;
use App\Models\AppraiserBusinessModel;
use App\Utils\Singleton;

class AppraiserBusinessService
{
    use Singleton;


    public function getListByBusinessId($businessId )
    {
        $appraiserBusiness = AppraiserBusinessModel::query()
            ->where('business_id', $businessId)
            ->get();
        if (empty($appraiserBusiness)) {
            return [];
        }

        return $appraiserBusiness->toArray();
    }
    public function getListByUserinfoId($userinfoId)
    {
        $list = AppraiserBusinessModel::query()
            ->where('userinfo_id', $userinfoId)
            ->get();
        if (empty($list)) {
            return [];
        }

        return $list->toArray();
    }

    public function getListByUserinfoIds($userinfoIds)
    {
        $list = AppraiserBusinessModel::query()
            ->where('state', CommonConst::STATE_ACTIVE)
            ->whereIn('userinfo_id', $userinfoIds)
            ->get();
        if (empty($list)) {
            return [];
        }

        return $list->toArray();
    }


    public function bindBusiness($userinfoId, $businessId, $dispatchCount = -1)
    {
        // 已绑定
        $appraiserBusiness = AppraiserBusinessModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('business_id', $businessId)
            ->first();
        if (!empty($appraiserBusiness)) {
            // 更新派单量
            if ($dispatchCount >= 0) {
                AppraiserBusinessModel::query()
                    ->where('id', $appraiserBusiness->id)
                    ->update([
                        'dispatch_count' => $dispatchCount,
                    ]);
            }
            return 1;
        }

        $model = new AppraiserBusinessModel();
        $model->userinfo_id = $userinfoId;
        $model->business_id = $businessId;
        $model->dispatch_count = max($dispatchCount, 0);
        $model->create_time = time();
        return $model->save();
    }

    public function unBindBusiness($userinfoId, $businessId)
    {
        // 无绑定
        $build = AppraiserBusinessModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('business_id', $businessId);
        if (empty($build->first())) {
            return 1;
        }

        return $build->delete();
    }

    public function bindBusinessBatch($userinfoId, $params)
    {
        $data = [];
        foreach ($params as $businessId) {
            $data[] = ['userinfo_id' => $userinfoId, 'business_id' => $businessId, 'create_time' => time()];
        }

        return AppraiserBusinessModel::insert($data);
    }

    public function unBindBusinessBatch($userinfoId, $businessIds = [])
    {
        $build = AppraiserBusinessModel::query()
            ->where('userinfo_id', $userinfoId);
        if (!empty($businessIds)) {
            $build->whereIn('business_id', $businessIds);
        }

        return $build->delete();
    }
}