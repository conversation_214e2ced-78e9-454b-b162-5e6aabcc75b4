<?php

namespace App\Service;

use App\Models\AppraiserGroupModel;
use App\Utils\Singleton;

class AppraiserGroupService
{
    use Singleton;

    public function getByUserinfoId($userinfoId)
    {
        $group = AppraiserGroupModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('is_deleted', 0)
            ->first();
        if (!$group) {
            return [];
        }
        return $group->toArray();
    }

    public function isLeader($userinfoId)
    {
        $group = AppraiserGroupService::getInstance()->getByUserinfoId($userinfoId);
        $isLeader = false;
        if (!empty($group)) {
            $isLeader = true;
        }

        return $isLeader;
    }


    public function delByUserinfoId($userinfoId)
    {
        return AppraiserGroupModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('is_deleted', 0)
            ->update(['is_deleted' => 1]);
    }

    public function add($params)
    {
       $model = new AppraiserGroupModel();
       $model->userinfo_id = $params['userinfoId'];
       $model->create_time = time();
       return $model->save();
    }

}