<?php

namespace App\Service;

use App\Models\AppraiserGroupCategoryRelationModel;
use App\Models\AppraiserGroupSchedulingModel;
use App\Utils\Singleton;
use Carbon\Carbon;

class AppraiserGroupScheduleService
{
    use Singleton;

    public function getOne($userinfoId, $memberUid, $assignmentDate)
    {
        $schedule = AppraiserGroupSchedulingModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('member_uid', $memberUid)
            ->where('assignment_date', $assignmentDate)
            ->where('is_deleted', 0)
            ->first();
        if (!$schedule) {
           return [];
        }

        return $schedule->toArray();
    }

    public function getListByUserinfoId($userinfoId)
    {
        return AppraiserGroupSchedulingModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('is_deleted', 0)
            ->get()
            ->toArray();
    }
    public function getListByMemberUidsAndAssignmentDate($userinfoIdList, $assignmentDateList)
    {
        $scheduleList = AppraiserGroupSchedulingModel::query()
            ->where('is_deleted', 0)
            ->whereIn('member_uid', $userinfoIdList)
            ->whereIn('assignment_date', $assignmentDateList)
            ->where('assigned_volume', '>', 0)
            ->get()
            ->toArray();
        if (empty($scheduleList)) {
            return [];
        }

        $result = [];
        foreach ($scheduleList as $schedule) {
            $result[$schedule['assignment_date']][] = [
                'userinfoId' => $schedule['member_uid'],
                'assignedVolume' => $schedule['assigned_volume']
            ];
        }
        return $result;
    }

    public function add($params)
    {
        $schedule = new AppraiserGroupSchedulingModel();
        $schedule->userinfo_id = $params['userinfoId'];
        $schedule->member_uid = $params['memberUid'];
        $schedule->assignment_date = $params['assignmentDate'];
        $schedule->assigned_volume = $params['assignedVolume'];
        $schedule->create_time = time();
        return $schedule->save();
    }

    public function editAssignedVolume($userinfoId, $memberUid, $assignmentDate, $assignedVolume)
    {
         return AppraiserGroupSchedulingModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('member_uid', $memberUid)
            ->where('assignment_date', $assignmentDate)
            ->update(['assigned_volume' => $assignedVolume]);
    }

    public function delByUserinfoId($userinfoId)
    {
        return AppraiserGroupSchedulingModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('is_deleted', 0)
            ->update(['is_deleted' => 1]);
    }

    public function delByMemberUid($userinfoId)
    {
        return AppraiserGroupSchedulingModel::query()
            ->where('member_uid', $userinfoId)
            ->where('is_deleted', 0)
            ->update(['is_deleted' => 1]);
    }

    public function delByUserinfoIdAndMemberUids($userinfoId, $memberUids)
    {
        return AppraiserGroupSchedulingModel::query()
            ->where('is_deleted', 0)
            ->where('userinfo_id', $userinfoId)
            ->whereIn('member_uid', $memberUids)
            ->update(['is_deleted' => 1]);
    }

    public function getListMap($startTime, $endTime)
    {
        $q = AppraiserGroupSchedulingModel::query()
            ->where('is_deleted', 0);
        if ($startTime > 0) {
            $startDate = Carbon::createFromTimestamp($startTime)->toDateString();
            $q->where('assignment_date', '>=', $startDate);
        }
        if ($endTime > 0) {
            $endDate = Carbon::createFromTimestamp($endTime)->toDateString();
            $q->where('assignment_date', '<=', $endDate);
        }

        $schedulingList = $q->orderBy('id')->get()->toArray();
        if (empty($schedulingList)) {
            return [];
        }

        $schedulingMap = [];
        foreach ($schedulingList as $item) {
            $key = $item['userinfo_id'] . '_' . $item['member_uid'] . '_' . $item['assignment_date'];
            $schedulingMap[$key] = [
                'userinfoId' => $item['userinfo_id'],
                'memberUid' => $item['member_uid'],
                'assignmentDate' => $item['assignment_date'],
                'assignedVolume' => $item['assigned_volume'],
            ];
        }

        return $schedulingMap;
    }
}