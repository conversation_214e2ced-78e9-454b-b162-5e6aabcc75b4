<?php

namespace App\Service;

use App\Constants\BusinessConst;
use App\Models\AppraiserGroupCategoryRelationModel;
use App\Utils\Singleton;

class AppraiserGroupCategoryService
{
    use Singleton;

    public function add($userinfoId, $categoryIds)
    {
        $businessCategoryList = [];
        foreach ($categoryIds as $businessId => $categoryIdList) {
            foreach ($categoryIdList as $categoryId) {
                $businessCategoryList[] = [
                    'userinfo_id' => $userinfoId,
                    'business_id' => $businessId,
                    'category_id' => $categoryId,
                ];
            }
        }

       return AppraiserGroupCategoryRelationModel::query()->insert($businessCategoryList);
    }

    public function getListByUserinfoId($userinfoId)
    {
        return AppraiserGroupCategoryRelationModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('is_deleted', 0)
            ->get()
            ->toArray();
    }

    public function delByUserinfoId($userinfoId)
    {
        return AppraiserGroupCategoryRelationModel::query()
            ->where('userinfo_id', $userinfoId)
            ->where('is_deleted', 0)
            ->update(['is_deleted' => 1]);
    }

}