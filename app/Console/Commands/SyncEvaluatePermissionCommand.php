<?php

namespace App\Console\Commands;

use App\Constants\BusinessConst;
use App\Logic\AppraiserBusinessLogic;
use App\Models\AppraiserBusinessModel;
use App\Models\Community\AppraiserExtendModel;
use App\Models\Community\AppraiserModel;
use Illuminate\Console\Command;

class SyncEvaluatePermissionCommand extends Command
{
    protected $signature = 'sync:evaluate-permission {userinfoId?}';
    protected $description = '同步社区的鉴定师-寄售权限数据';

    const BUSINESS_ID_MAP = [
        5 => [
            "玉翠珠宝" => 30001,
            "钱币邮票" => 1923,
            "文玩杂项" => 30002,
            "紫砂陶瓷" => 30003,
            "木质珠串" => 30005,
            "文房书画" => 30013,
        ],

        6 => [
            '玉翠珠宝' => 1,
            '文玩杂项' => 3,
            '木质珠串' => 5,
            '紫砂陶瓷' => 4,
            '文房书画' => 7,
            '钱币邮票' => 2,
        ],
    ];

    const PERMISSION_MAP = [
        'dev' => [
            11900274 => "文玩杂项,钱币邮票",
            18 => "钱币邮票",
            11900258 => "文房书画",
            11900280 => "玉翠珠宝",
            //11900281 => "紫砂陶瓷",
            11900262 => "木质珠串,紫砂陶瓷",
            2 => "玉翠珠宝",
            100260134 => "钱币邮票",
            12 => "紫砂陶瓷",
            100280018 => "文房书画",
            100320267 => "文玩杂项",
            100320642 => "玉翠珠宝",
            100320628 => "钱币邮票",
            100320731 => "紫砂陶瓷",
            100280017 => "木质珠串",
            11720029 => "文玩杂项",
            11742588 => "玉翠珠宝",
            8620002 => "文房书画",
            8700120 => "钱币邮票",
            100000088 => "玉翠珠宝",
            100320372 => "紫砂陶瓷",
            100220082 => "文玩杂项",
            100220085 => "玉翠珠宝",
            11740228 => "钱币邮票",
            100540035 => "紫砂陶瓷",
            12260002 => "文房书画",
            12260004 => "木质珠串",
            11740300 => "玉翠珠宝",
            11740202 => "文玩杂项",
            11742266 => "紫砂陶瓷",
            100560086 => "木质珠串",
            8611288 => "文玩杂项",
            100560304 => "玉翠珠宝",
            100540160 => "文房书画",
            8611516 => "钱币邮票",
            100280039 => "玉翠珠宝",
            100320037 => "紫砂陶瓷",
            100600132 => "文玩杂项",
            8622426 => "玉翠珠宝",
        ],


        // 0506 生产环境开通寄售权限的人
        'prod' => [
            //50124814 => "文玩杂项,紫砂陶瓷",  // 刘轩成离职
            67347471 => "文玩杂项,木质珠串",
            45687904 => "钱币邮票",
            41576711 => "钱币邮票",
            42602585 => "钱币邮票",
            89631646 => "紫砂陶瓷",
            2966167 => "紫砂陶瓷",
            20827 => "文玩杂项",
            22513952 => "文房书画",
            // 82784657 => "钱币邮票",  // 付柏程离职
            428 => "玉翠珠宝",
            154162610 => "玉翠珠宝",
            155515885 => "玉翠珠宝",
            2166846 => "紫砂陶瓷",
            153158422 => "玉翠珠宝",
            153655457 => "玉翠珠宝",
            154592861 => "玉翠珠宝",
            152166268 => "文玩杂项",
            72475634 => "紫砂陶瓷",
            10378673 => "紫砂陶瓷",
            152163603 => "文玩杂项",
            152094257 => "钱币邮票",
            163705947 => "紫砂陶瓷",
            50853940 => "紫砂陶瓷",
            33806381 => "文玩杂项",
            37995712 => "文玩杂项",
            1178999 => "紫砂陶瓷",
            118323649 => "紫砂陶瓷",
            20141243 => "文玩杂项",
            52011791 => "紫砂陶瓷",
            9955711 => "钱币邮票",
            165031776 => "文玩杂项",
            46359268 => "钱币邮票",
            1789043 => "钱币邮票",
            66298616 => "紫砂陶瓷",
            100435548 => "紫砂陶瓷",
            113413261 => "玉翠珠宝",
            83529286 => "玉翠珠宝",
            160196065 => "玉翠珠宝",
            543446 => "紫砂陶瓷",
            4777450 => "玉翠珠宝",
            47770314 => "玉翠珠宝",
            8725259 => "文玩杂项",
            24836059 => "钱币邮票",
            47039332 => "玉翠珠宝",
            102520 => "文玩杂项,木质珠串",
            133796497 => "紫砂陶瓷",
            52796939 => "钱币邮票",
            90472 => "木质珠串,玉翠珠宝",
            49608442 => "玉翠珠宝",
            48114382 => "紫砂陶瓷",
            42210729 => "钱币邮票",
            8388776 => "钱币邮票",
            482163 => "文玩杂项",
            47673325 => "玉翠珠宝",
            685315 => "文玩杂项",
            47307401 => "文玩杂项",
            1398496 => "紫砂陶瓷",
            46708151 => "钱币邮票",
            46066285 => "钱币邮票",
            17321716 => "玉翠珠宝",
            5605698 => "钱币邮票",
            474108 => "文房书画,文玩杂项",
            8595118 => "紫砂陶瓷",
            51330885 => "文玩杂项",
            83686 => "玉翠珠宝",
            608837 => "钱币邮票",
            3938779 => "文玩杂项",
            52910536 => "玉翠珠宝",
            10471614 => "紫砂陶瓷",
            54793162 => "钱币邮票",
            615502 => "钱币邮票",
            149048417 => "玉翠珠宝",
            148984074 => "玉翠珠宝",
            105889074 => "文玩杂项,木质珠串",
            16346657 => "钱币邮票",
            43976346 => "文玩杂项",
            28361912 => "文玩杂项",
            114237116 => "文玩杂项",
            176331456 => "文玩杂项",    // iOS审核用，追风大叔
            84420952 => "文玩杂项", //测试账号
            84420932 => "文玩杂项"  //测试账号

        ]
    ];
    public function handle()
    {
        $userinfoId = $this->argument('userinfoId');
        $pingguPermissionMap = [];
        $chayanPermissionMap = [];
        $envKey = is_test() ? 'dev' : 'prod';


        // 组装权限数据
        foreach (self::PERMISSION_MAP[$envKey] as $userId => $categoryNames) {
            // 有传入只用传入的
            if (!empty($userinfoId) && $userId != $userinfoId) {
                $this->warn("$userId 传入鉴定师，不执行");
                continue;
            }

            // 鉴定师不存在退出
            $exist = \App\Models\AppraiserModel::query()
                ->where('userinfo_id', $userId)
                ->where('state', 1)
                ->first();
            if (empty($exist)) {
                $this->error('鉴定师不存在,userId:' . $userId);
                continue;
            }

            $categoryNameArr = explode(',', $categoryNames);

            // 替换评估
            $pingguCategoryIdArr = [];
            $chayanCategoryIdArr = [];
            foreach ($categoryNameArr as $categoryName) {
                $pingguCategoryIdArr[] = self::BUSINESS_ID_MAP[5][$categoryName];
                $chayanCategoryIdArr[] = self::BUSINESS_ID_MAP[6][$categoryName];
            }

            $pingguPermissionMap[$userId] = $pingguCategoryIdArr;
            $chayanPermissionMap[$userId] = $chayanCategoryIdArr;
        }

        //dd($pingguPermissionMap, $chayanPermissionMap);

        // 处理寄售评估权限
        foreach ($pingguPermissionMap as $userinfoId => $category) {
            $params = [
                'state' => 1,   //直接开启
                'userinfoId' => $userinfoId,
                'businessId' => BusinessConst::BUSINESS_TYPE_CONSIGN_EVALUATE,
                'category' => $category,
               // 'extend' => 1,
            ];
            // 获取其他属性
            $res = AppraiserBusinessLogic::getInstance()->edit($params);
            $this->info(sprintf('business:5, userinfoId: %s, category:%s, res: %s', $userinfoId, json_encode($category), $res));
        }

        // 处理寄售查验权限
        foreach ($chayanPermissionMap as $userinfoId => $category) {
            // 查询社区数据
            $appraiserInfo = $this->getCommunityAppraiser($userinfoId);
            if (empty($appraiserInfo)) {
                return ;
            }
            $appraiser = $appraiserInfo['appraiser'];
            $appraiserExtend = $appraiserInfo['appraiserExtend'];
//dd($appraiser, $appraiserExtend);

            // 组装扩展信息
            $extend = [];
            $supportImageConsignment = $appraiserExtend['support_image_consignment'] == 1 ? 1 : 0;
            $supportRealityConsignment = $appraiserExtend['support_reality_consignment'] == 1 ? 1 : 0;
            $extend[] = ['field_name' => '是否支持图文寄售查验', 'field_key' => 'supportImageConsignment', 'field_val' => $supportImageConsignment];
            $extend[] = ['field_name' => '是否支持实物寄售查验', 'field_key' => 'supportRealityConsignment', 'field_val' => $supportRealityConsignment];
            $extend[] = ['field_name' => '是否支持寄售复审', 'field_key' => 'canLiveRecheck', 'field_val' => $appraiser['can_live_recheck']];
            $extend[] = ['field_name' => '是否支持寄售复核', 'field_key' => 'canSecondRecheck', 'field_val' => $appraiser['can_second_recheck']];

            $params = [
                'state' => 1,   //直接开启
                'userinfoId' => $userinfoId,
                'businessId' => BusinessConst::BUSINESS_TYPE_CONSIGN_VERIFY,
                'category' => $category,
                'extend' => $extend,
                'orderAcceptanceLimit' => $appraiserExtend['order_acceptance_limit'],   //接单量
                //'isTrainee' => $appraiserExtend['trainee_state'] == 2 ? 1 : 2,  // 是否新人 1是 2否
            ];
            $res1 = AppraiserBusinessLogic::getInstance()->edit($params);

            // todo 休息时间线上数据没有设置，不洗

            $this->info(sprintf('business:6, userinfoId: %s, category:%s, res1: %s', $userinfoId, json_encode($category), $res1));

        }
    }

    private function getCommunityAppraiser($userinfoId)
    {
        $appraiser = AppraiserModel::query()
            ->where('isDeleted', 0)
            ->where('appraiserLevel', 1)
            ->where('userinfoId', $userinfoId)
            ->first();
        if (empty($appraiser)) {
            $this->error(sprintf('appraiser is empty userinfoId: %s', $userinfoId));
            return [];
        }

        $appraiser = $appraiser->toArray();
        $appraiserExtend = AppraiserExtendModel::query()
            ->where('appraiser_id', $appraiser['id'])
            ->first();
        if (empty($appraiserExtend)) {
            $this->error(sprintf('appraiserExtend is empty appraiser_id: %s', $appraiser['id']));
            return [];
        }
        $appraiserExtend = $appraiserExtend->toArray();

        return [
            'appraiser' => $appraiser,
            'appraiserExtend' => $appraiserExtend,
        ];
    }
}