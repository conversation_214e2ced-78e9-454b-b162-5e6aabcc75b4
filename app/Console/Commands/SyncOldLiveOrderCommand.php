<?php

namespace App\Console\Commands;

use App\Constants\LogConst;
use App\Libraries\RedLock;
use App\Service\SyncOrderService;
use Illuminate\Console\Command;
use Spin\Logger\Facades\Log;

class SyncOldLiveOrderCommand extends Command
{
    protected $signature = 'sync:old-live-order {identUri} {comeFrom} {identProcess}';
    protected $description = '同步历史订单数据';

    public function handle()
    {

        $identUri = $this->argument('identUri');
        $comeFrom = $this->argument('comeFrom');
        $identProcess = $this->argument('identProcess');
        Log::info('sync:old-live-order', '开始同步历史订单数据', [
            'identUri' => $identUri,
            'comeFrom' => $comeFrom,
            'identProcess' => $identProcess,
        ]);
        $this->syncOrder($identUri, $comeFrom, $identProcess);
    }

    private function syncOrder($identUri, $comeFrom, $identProcess)
    {
        // 订单不需要处理的状态 40:等待鉴定 50:鉴定完成 80:取消
        if (!in_array($identProcess, [40, 50, 80, 60])) {
            Log::info(LogConst::IMAGE_IDENT_SERVER, '订单状态不在同步范围', ['uri' => $identUri]);
            return;
        }
        $lock = RedLock::getInstance()->lock('sync-old-order-' . $identUri, 5);
        if (!$lock) {
            Log::info(LogConst::IMAGE_IDENT_SERVER, '脚本被锁请求频繁', ['uri' => $identUri]);
            return;
        }
        if ($comeFrom == 120) {
            SyncOrderService::getInstance()->syncFwz($identUri);
        }

        // 解锁
        RedLock::getInstance()->unlock($lock);
    }
}
