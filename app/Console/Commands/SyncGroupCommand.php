<?php

namespace App\Console\Commands;

use App\Models\Community\AppraiserExtendModel;
use App\Models\Community\AppraiserGroupMembersRelationModel;
use App\Models\Community\AppraiserGroupModel;
use App\Models\Community\AppraiserGroupSchedulingModel;
use App\Models\Community\AppraiserModel;
use App\Service\AppraiserGroupService;
use Illuminate\Console\Command;

class SyncGroupCommand extends Command
{

    protected $signature = 'sync:group';
    protected $description = '同步社区的鉴定师-分组和排班信息-(生产环境不同步数据，进行配置)';

    public function handle()
    {
        // 1、获取组长
        $appraiserGroupList = AppraiserGroupModel::query()
            ->where('delete_time',0)
            ->get()
            ->toArray();
        if (empty($appraiserGroupList)) {
            $this->error('没有鉴定师组长');
            return [];
        }
        // 组长的userinfoId
        $appraiserIds = array_column($appraiserGroupList, 'appraiser_id');
        $appraiserList = AppraiserModel::query()
            ->whereIn('id', $appraiserIds)
            ->get()
            ->toArray();
        $groupUserinfoIds = array_column($appraiserList, 'userinfoId');

        // 清空组长数据
        \App\Models\AppraiserGroupModel::query()->where('is_deleted', 0)->update(['is_deleted' => 1]);
        // 组长写入新表
        $newGroupList = [];
        foreach ($groupUserinfoIds as $groupUserinfoId) {
            $newGroupList[] = [
                'userinfo_id' => $groupUserinfoId,
                'is_deleted' => 0,
                'create_time' => time()
            ];
        }
        $res = \App\Models\AppraiserGroupModel::query()
            ->insert($newGroupList);


        //2、获取组员
        $groupIds = array_column($appraiserGroupList, 'id');
        //dd($groupIds);
        $appraiserGroupMemberList = AppraiserGroupMembersRelationModel::query()
            ->where('delete_time',0)
            ->whereIn('appraiser_group_id', $groupIds)
            ->orderBy('appraiser_group_id', 'asc')
            ->get()
            ->toArray();
        //dd($appraiserGroupMemberList);

        $newAppraiserGroupMemberList = [];
        foreach ($appraiserGroupMemberList as $appraiserGroupMember) {
           $appraiserGroupId = $appraiserGroupMember['appraiser_group_id'];
           $appraiserId = $appraiserGroupMember['appraiser_id'];
           //var_dump($appraiserGroupId, $appraiserId);
           $appraiserGroup = AppraiserGroupModel::query()->where('id', $appraiserGroupId)->first();
           $appraiserGroupUserinfoId = AppraiserModel::query()->where('id', $appraiserGroup->appraiser_id)->first()->userinfoId;
           //dd($appraiserGroupUserinfoId);
           $memberUserinfoId = AppraiserModel::query()->where('id', $appraiserId)->first()->userinfoId;
           //dd($appraiserGroupUserinfoId, $memberUserinfoId);
            $newAppraiserGroupMemberList[] = [
                'userinfo_id' => $appraiserGroupUserinfoId,
                'member_uid' => $memberUserinfoId,
                'create_time' => time(),
            ];
            $this->info("组员迁移: group_id:$appraiserGroupId, appraiser_id:$appraiserId --> $appraiserGroupUserinfoId, $memberUserinfoId");
        }
        // 写入新表
        \App\Models\AppraiserGroupMembersRelationModel::query()->where('is_deleted', 0)->update(['is_deleted' => 1]);
        \App\Models\AppraiserGroupMembersRelationModel::query()->insert($newAppraiserGroupMemberList);


        // 3、获取排班信息
        $startDate = '2025-05-01';
        $appraiserGroupSchedulingList = AppraiserGroupSchedulingModel::query()
            ->where('delete_time',0)
            ->where('assignment_date', '>=', $startDate)
            ->orderBy('assignment_date', 'asc')
            ->get()
            ->toArray();
        $newAppraiserGroupSchedulingList = [];
        foreach ($appraiserGroupSchedulingList as $appraiserGroupScheduling) {
            $appraiserGroupId = $appraiserGroupScheduling['appraiser_group_id'];
            $appraiserId = $appraiserGroupScheduling['appraiser_id'];
            $assignmentDate = $appraiserGroupScheduling['assignment_date'];
            $assignedVolume = $appraiserGroupScheduling['assigned_volume'];

            //$this->info("排班迁移开始: group_id:$appraiserGroupId, appraiser_id:$appraiserId");
            $appraiserGroup = AppraiserGroupModel::query()->where('id', $appraiserGroupId)->where('delete_time', 0)->first();
            if (empty($appraiserGroup)) {
                $this->warn("组长删除: group_id:$appraiserGroupId,不存在");
                continue;
            }

            $appraiserGroupUserinfoId = AppraiserModel::query()->where('id', $appraiserGroup->appraiser_id)->first()->userinfoId;
            $memberUserinfoId = AppraiserModel::query()->where('id', $appraiserId)->first()->userinfoId;
            $newAppraiserGroupSchedulingList[] = [
                'userinfo_id' => $appraiserGroupUserinfoId,
                'member_uid' => $memberUserinfoId,
                'assignment_date' => $assignmentDate,
                'assigned_volume' => $assignedVolume,
            ];
            $this->info("排班迁移: group_id:$appraiserGroupId, appraiser_id:$appraiserId , assignment_date:$assignmentDate, assigned_volume:$assignedVolume --> $appraiserGroupUserinfoId, $memberUserinfoId");
        }
        //dd($newAppraiserGroupSchedulingList);
        \App\Models\AppraiserGroupSchedulingModel::query()->insert($newAppraiserGroupSchedulingList);
    }

}