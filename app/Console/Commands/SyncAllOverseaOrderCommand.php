<?php

namespace App\Console\Commands;

use App\Models\Jzg\IdentLiveOrderModel;
use App\Models\OrderItemFieldModel;
use App\Models\OrderItemModel;
use App\Models\OrderModel;
use App\Service\SyncOrderService;
use Illuminate\Console\Command;
use Spin\Logger\Facades\Log;

class SyncAllOverseaOrderCommand extends Command
{
    protected $signature = 'sync:oversea-order';
    protected $description = '海外图文历史订单同步';

    public function handle()
    {
        $this->updateOversea();
    }


    private function syncOversea()
    {
        $orderList = IdentLiveOrderModel::query()
            ->select(['identProcess', 'identUri'])
            ->where('identProcess', 60)
            ->where('comeFrom', 110)
            ->get();

        Log::info('sync-oversea-order', '开始同步', ['count' => count($orderList)]);
        foreach ($orderList as $order) {
            SyncOrderService::getInstance()->syncOversea($order->identUri);
            $this->info("处理完成:" . $order->identUri);
        }
    }

    private function updateOversea()
    {
        $orderList = OrderModel::query()
            ->where('id', '>=', 6791)
            ->where('business_id', 4)
            ->get();

        foreach ($orderList as $order) {
            $oldOrder = SyncOrderService::getInstance()->getOldOrderData($order->uri);
            if (empty($oldOrder)) {
                continue;
            }

            // 处理item
            $orderItem = OrderItemModel::query()->where('order_id', $order->id)->first();
            if (empty($orderItem)) {
                continue;
            }

            $profileJson = json_decode($oldOrder['profileJson'], true);

            // 更新remark
            $orderItem->remark = $profileJson['identRemarkOri'] ?? '';
            $orderItem->save();

            // 判断field
            $itemField = OrderItemFieldModel::query()->where('order_id', $order->id)
                ->where('field_key', 'remarkTranslate')
                ->first();
            if (empty($itemField)) {
                // 保存下输入字段
                $inputFieldMap = [
                    'remarkTranslate' => 'identRemark'
                ];
                SyncOrderService::getInstance()->saveInputItemField(4, $order->id, $inputFieldMap, $profileJson, $orderItem->id);
            }
        }
    }
}
