<?php

namespace App\Console\Commands;

use App\Models\BusinessModel;
use Illuminate\Console\Command;

class InitBusinessCommand extends Command
{
    protected $signature = 'init-business';
    protected $description = '初始化业务名称';

    public function handle()
    {
        $business = [
            1 => ['普通图文', ''],
            2 => ['名家鉴定', ''],
            3 => ['小法庭', ''],
            4 => ['海外图文', env('OVER_SEA_HOST') . '/inside/sync-result-new'],//https://identify-api-t.wpt.la/inside/sync-result-new
            5 => ['寄售评估', ''],
            6 => ['寄售查验', ''],
        ];


        $insert = [];
        foreach ($business as $id => $item) {
            $insert[] = [
                'id'=> $id,
                'name' => $item[0],
                'notify_url' => $item[1],
                'state'=>1,
                'create_time' => time(),
            ];
        }
        BusinessModel::query()->delete();
        BusinessModel::query()->insert($insert);
        $this->info('业务名称初始化成功');
    }

}