<?php

namespace App\Console\Commands;

use App\Client\Bus\BusUser;
use App\Client\SDK\CommunityAppraiser;
use App\Logic\SyncAppraiserLogic;
use App\Models\Community\AppraiserExtendModel;
use App\Models\Community\AppraiserModel;
use Illuminate\Console\Command;

class WashCommunityToAppraiserCommand extends Command
{
    protected $signature = 'wash:community-appraiser {--userinfoId=0} {--appraiserId=0} {--all=0} {--business=}';
    protected $description = '社区数据鉴定师洗入新表';

    public function handle()
    {
        $userinfoId = $this->option('userinfoId');
        $appraiserId = $this->option('appraiserId');
        $business = $this->option('business');  // 不传则全部业务
        $all = $this->option('all');

        // appraiser_extend表传入
        if (!empty($appraiserId)) {
            $userinfoId = AppraiserModel::query()
                ->where('id', $appraiserId)
                ->value('userinfoId');
        }

        // 单个用户处理
        if (empty($all)) {
            if (empty($userinfoId)) {
                $this->error('userinfoId is empty');
                return;
            }

            $this->singleHandle($userinfoId, $business);
        } else {
            // 批量处理
            $this->batchHandle($business);
        }
    }

    public function singleHandle($userinfoId, $business)
    {
        // 查询社区的鉴定师数据
        $appraiser = AppraiserModel::query()
            ->where('isDeleted', 0)
            ->where('appraiserLevel', 1)
            ->where('userinfoId', $userinfoId)
            ->first();
        if (empty($appraiser)) {
            $this->error(sprintf('appraiser is empty userinfoId: %s', $userinfoId));
            return;
        }

        $appraiser = $appraiser->toArray();
        $appraiserExtend = AppraiserExtendModel::query()
            ->where('appraiser_id', $appraiser['id'])
            ->first();
        if (empty($appraiserExtend)) {
            $this->error(sprintf('appraiserExtend is empty appraiser_id: %s', $appraiser['id']));
            return;
        }
        $appraiserExtend = $appraiserExtend->toArray();
        //dd($appraiser, $appraiserExtend, $business);

        $res = SyncAppraiserLogic::getInstance()->syncAppraiser($appraiser, $appraiserExtend, $business);
        $this->info(sprintf('已处理 userinfoId: %s, business: %s', $appraiser['userinfoId'], $business ?:"全部"));
    }

    private function batchHandle($business)
    {
        // 查询社区的鉴定师数据
        $num = 1;
        AppraiserModel::query()
            ->where('isDeleted', 0)
            ->where('appraiserLevel', 1)
            ->chunkById(10, function ($appraisers) use (&$num, $business) {
                foreach ($appraisers as $appraiser) {
                    $appraiser = $appraiser->toArray();
                    $appraiserExtend = AppraiserExtendModel::query()
                        ->where('appraiser_id', $appraiser['id'])
                        ->first();
                    if (empty($appraiserExtend)) {
                        $this->error(sprintf('appraiserExtend is empty appraiser_id: %s', $appraiser['id']));
                        continue;
                    }
                    $appraiserExtend = $appraiserExtend->toArray();
                    $res = SyncAppraiserLogic::getInstance()->syncAppraiser($appraiser, $appraiserExtend, $business);
                    $this->info(sprintf('已处理 id:%s, userinfoId: %s, business: %s, res: %s', $appraiser['id'], $appraiser['userinfoId'], $business ?:"全部", $res));
                    $this->info(sprintf('已处理 %s 条', $num++));
                }
            });
    }

}