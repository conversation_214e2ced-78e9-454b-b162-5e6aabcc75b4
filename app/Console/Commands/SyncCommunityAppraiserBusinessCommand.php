<?php

namespace App\Console\Commands;

use App\Cache\AppraiserCache;
use App\Logic\AppraiserBusinessLogic;
use App\Logic\SyncAppraiserLogic;
use App\Models\Community\AppraiserExtendModel;
use App\Models\Community\AppraiserModel;
use App\Params\Appariser\AppraiserParams;
use App\Service\AppraiserService;
use Illuminate\Console\Command;
use Spin\Logger\Facades\Log;

class SyncCommunityAppraiserBusinessCommand extends Command
{
    protected $signature = 'sync:community-appraiser-business {appraiserId?} {businessName?}';
    protected $description = '同步社区的鉴定师-业务权限数据';

    protected $prefix = 'sync-community-appraiser';

    public function handle()
    {
        $appraiserId = $this->argument('appraiserId');
        $businessName = $this->argument('businessName');

        if (empty($appraiserId)) {
            $this->error('appraiserId不能为空');
        }

        $userinfoId = AppraiserModel::query()
                ->where('id', $appraiserId)
                ->value('userinfoId');
        if (empty($userinfoId)) {
            $this->error('userinfoId不能为空');
            return [];
        }

        // 防并发 加锁
        $lock = AppraiserCache::washAppraiserLock($userinfoId, 3);
        if (!$lock) {
            $this->info('正在同步中，防止并发先sleep..');
            sleep(3);
        }

        $appraiser = AppraiserModel::query()
            ->where('id', $appraiserId)
            ->first();
        if (empty($appraiser)) {
            $this->error(sprintf('userinfoId: %s, 鉴定师不存在', $userinfoId));
            return [];
        }
        $appraiser = $appraiser->toArray();

        $this->info(sprintf('userinfoId: %s, appraiserId: %s, businessName: %s, 开始同步', $userinfoId, $appraiserId, $businessName));
        // 订阅同步过来，需要进行删除操作
        if (!empty($appraiser['isDeleted'])) {
            SyncAppraiserLogic::getInstance()->deleteAppraiser($userinfoId);
            Log::info($this->prefix, '删除鉴定师', $userinfoId);
            $this->error(sprintf('userinfoId: %s, 鉴定师已删除', $userinfoId));
            return [];
        }

        $appraiserExtend = AppraiserExtendModel::query()
            ->where('appraiser_id', $appraiserId)
            ->first();
        if (empty($appraiserExtend)) {
            return [];
        }
        $appraiserExtend = $appraiserExtend->toArray();

        // dd($appraiser, $appraiserExtend);
        SyncAppraiserLogic::getInstance()->syncAppraiser($appraiser, $appraiserExtend, $businessName);
        $this->info(sprintf('userinfoId: %s, appraiserId: %s, businessName: %s, 鉴定师已同步', $userinfoId, $appraiserId, $businessName));
    }

}