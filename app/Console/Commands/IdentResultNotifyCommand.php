<?php

namespace App\Console\Commands;

use App\Constants\OrderConst;
use App\Logic\Inner\OrderLogic;
use App\Service\AsyncService;
use App\Service\BusinessService;
use App\Service\OrderService;
use GuzzleHttp\Client;
use Illuminate\Console\Command;
use Spin\Logger\Facades\Log;

class IdentResultNotifyCommand extends Command
{
    protected $signature = 'notify:ident-result {uri} {retry=1}';
    protected $description = '推送结果';

    public function handle()
    {
        $uri = $this->argument('uri');
        $retryCount = intval($this->argument('retry'));
        Log::info("notify:ident-result", '开始推送结果', ['uri' => $uri, 'retryCount' => $retryCount]);
        $this->notify($uri, $retryCount);
    }

    private function notify(string $uri, int $retryCount)
    {
        $order = OrderService::getInstance()->getOrderByUri($uri);
        if (empty($order)) {
            Log::error("notify:ident-result", "订单不存在", ['uri' => $uri]);
            return;
        }
        if ($order->state != OrderConst::ORDER_STATE_COMPLETE) {
            Log::error("notify:ident-result", "订单状态不正确", ['uri' => $uri, 'state' => $order->state]);
            return;
        }

        // 获取业务
        $business = BusinessService::getInstance()->getOneById($order->business_id);
        if (empty($business) || empty($business['notify_url'])) {
            return;
        }

        $detail = OrderLogic::getInstance()->simpleDetail($uri);

        try {
            $client = new Client();
            $response = $client->post($business['notify_url'], [
                'headers' => [
                    'Content-Type' => 'application/json', // 设置请求头为JSON类型
                ],
                'json' => $detail, // 将数据作为JSON格式传递给请求体
            ]);
            $result = $response->getBody()->getContents();
            $resultArr = json_decode($result, true);
            Log::info('notify:ident-result', '推送结果', ['result' => $result, 'post' => $detail, 'url' => $business['notify_url']]);
        } catch (\Throwable $e) {
            $resultArr = [];
            Log::error('notify:ident-result', '发送请求发生错误', ['e' => $e->getMessage()]);
        }

        if (empty($resultArr) || !isset($resultArr['code']) || $resultArr['code'] != 0) {
            if ($this->isNeedReTry($resultArr, $retryCount)) {
                AsyncService::syncIdentResultNotify($uri, $retryCount + 1, (time() + $retryCount * 5 * 60) * 1000);
            }
        }
    }

    private function isNeedReTry($resultArr, $retryCount)
    {
        $code = $resultArr['code'] ?? -10000;
        $msg = $resultArr['msg'] ?? '';

        // 已接受重复推送 不重试
        if ($code == -1 && $msg == "该订单已接收过鉴定结果") {
            return false;
        }

        // 重试计数
        if ($retryCount > 5) {
            return false;
        }

        return true;
    }
}
