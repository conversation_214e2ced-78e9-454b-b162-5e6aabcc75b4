<?php

namespace App\Console\Commands;

use App\Models\Jzg\IdentLiveOrderModel;
use App\Service\SyncOrderService;
use Illuminate\Console\Command;
use Spin\Logger\Facades\Log;

class SyncAllFwzOrderCommand extends Command
{
    protected $signature = 'sync:fwz-order';
    protected $description = '服务站图文历史订单同步';

    public function handle()
    {
        $this->syncFwz();
    }

    private function syncFwz()
    {
        $orderList = IdentLiveOrderModel::query()
            ->select(['identProcess', 'identUri'])
            ->where('comeFrom', 120)
            ->get();

        Log::info('sync-fwz-order', '开始同步', ['count' => count($orderList)]);
        foreach ($orderList as $order) {
            SyncOrderService::getInstance()->syncFwz($order->identUri);
            $this->info("处理完成:" . $order->identUri);
        }
    }

}
