<?php

namespace App\Console\Commands;

use App\Libraries\OverseaClient;
use App\Models\OrderItemModel;
use App\Models\OrderModel;
use App\Service\AsyncService;
use App\Service\OrderService;
use Illuminate\Console\Command;
use Spin\Logger\Facades\Log;

class SyncOverseaImgCommand extends Command
{
    protected $signature = 'sync:oversea-img {uri} {runCount=0}';
    protected $description = '海外图片上传';

    public function handle()
    {
        $uri = $this->argument('uri');
        $runCount = $this->argument('runCount');
        $this->syncImg($uri, $runCount);
    }

    public function syncImg($uri, $runCount)
    {
        $order = OrderService::getInstance()->getOrderByUri($uri);
        if (empty($order)) {
            return;
        }
        if ($order->business_id != 4) {
            return;
        }
        $orderItem = OrderItemModel::query()->where('order_id', $order->id)->first();
        if (empty($orderItem)) {
            return;
        }

        $imgList = json_decode($orderItem->imgs, true);

        $detailJson = json_decode($order->detail_json, true);
        $imgCnList = empty($detailJson['cnImgList']) ? [] : $detailJson['cnImgList'];
        if (empty($imgCnList[0])) {
            $frontImgRet = OverseaClient::getInstance()->uploadImageByLink($imgList[0]);
            Log::info('SyncOverseaOrderImgCommand', '正面上传图片返回', $frontImgRet);
            if ($frontImgRet) {
                $imgCnList[0] = combineImgUrl($frontImgRet['filename']);
            }
        }

        if (empty($imgCnList[1])) {
            $backImgRet = OverseaClient::getInstance()->uploadImageByLink($imgList[1]);
            Log::info('SyncOverseaOrderImgCommand', '反面上传图片返回', $backImgRet);
            if ($backImgRet) {
                $imgCnList[1] = combineImgUrl($backImgRet['filename']);
            }
        }

        Log::info('SyncOverseaOrderImgCommand', '保存数据', $imgCnList);
        if ($imgCnList) {
            $detailJson['cnImgList'] = $imgCnList;
            OrderModel::query()
                ->where('id', $order->id)
                ->update(['detail_json' => wpt_json_encode($detailJson)]);
        }

        // 拉取失败 重试一次
        if (count($imgCnList) < 2 && $runCount < 3) {
            $runCount++;
            AsyncService::syncOverseaImg($uri, $runCount);
        }
    }
}
