<?php

namespace App\Console\Commands;

use App\Models\AppraiserGroupSchedulingModel;
use App\Service\AppraiserGroupScheduleService;
use Illuminate\Console\Command;
use Spin\Logger\Facades\Log;

class GenerateGroupSchedulingCommand extends Command
{
    protected $signature = 'generate:group-scheduling';
    protected $description = '复制创建下一周期的鉴定师排期 - 定时任务';


    public function handle()
    {
        // 获取当前时间周一和周日日期
        $mondayAndSunday = $this->getMondayAndSunday();
        $monday = $mondayAndSunday['monday'];
        $sunday = $mondayAndSunday['sunday'];

        // 获取当前周的鉴定师排期
        $schedulingList = AppraiserGroupSchedulingModel::query()
            ->select( 'userinfo_id', 'member_uid', 'assignment_date', 'assigned_volume')
            ->where('is_deleted', 0)
            ->where('assignment_date', '>=', $monday)
            ->where('assignment_date', '<=', $sunday)
            ->get()
            ->toArray();
        if (empty($schedulingList)) {
            $this->info('没有需要复制的数据');
            return;
        }

        $newSchedulingList = [];
        foreach ($schedulingList as $scheduling) {
            $after7days = date('Y-m-d', strtotime($scheduling['assignment_date'] . '+7 days'));

            // 有设置排班，使用设置的
            $exist = AppraiserGroupSchedulingModel::query()
                ->where('is_deleted', 0)
                ->where('userinfo_id', $scheduling['userinfo_id'])
                ->where('member_uid', $scheduling['member_uid'])
                ->where('assignment_date', $after7days)
                ->first();
            // 存在
            if (!empty($exist)) {
                // 额度大于0，使用现在的，跳过复制
                if ($exist->assigned_volume > 0) {
                    $this->info("userinfoId:  {$scheduling['userinfo_id']} memberUid: {$scheduling['member_uid']} assignmentDate: {$after7days} 已经存在，跳过复制");
                    continue;
                }

                // 额度等于0。删除该条记录，重新创建
                if ($exist->assigned_volume == 0) {
                    AppraiserGroupSchedulingModel::query()
                        ->where('id', $exist->id)
                        ->update(['is_deleted' => 1]);
                    $this->warn("userinfoId:  {$scheduling['userinfo_id']} memberUid: {$scheduling['member_uid']} assignmentDate: {$after7days} 单量为空，删除");
                }
            }

            // 组装
            $scheduling['assignment_date'] = $after7days;
            $scheduling['create_time'] = time();
            $newSchedulingList[] = $scheduling;
        }

        if (empty($newSchedulingList)) {
            $this->error('没有需要创建的数据');
            return;
        }

        // 创建
        AppraiserGroupSchedulingModel::query()->insert($newSchedulingList);
        $this->info(sprintf('%s - %s 下一周排班复制完成！数量：%s', $monday, $sunday, count($newSchedulingList)));
        Log::info('GenerateGroupSchedulingCommand', '下一周排班复制完成',
            [
                'model' => $monday,
                'sunday' => $sunday,
                'newSchedulingList' => $newSchedulingList
            ]
        );
    }

    private function getMondayAndSunday(): array
    {
        // 获取当前日期
        $currentDate = new \DateTime();

        // 获取当前日期是一周中的第几天（0表示周日，1表示周一，以此类推）
        $dayOfWeek = $currentDate->format('w');

        // 计算周一的日期
        $monday = clone $currentDate;
        $monday->modify('-' . ($dayOfWeek - 1) . ' days');

        // 计算周日的日期
        $sunday = clone $currentDate;
        $sunday->modify('+' . (7 - $dayOfWeek) . ' days');

        return [
            'monday' => $monday->format('Y-m-d'),
            'sunday' => $sunday->format('Y-m-d')
        ];
    }
}