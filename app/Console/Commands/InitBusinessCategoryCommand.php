<?php

namespace App\Console\Commands;

use App\Models\AppraiserBusinessCategoryModel;
use App\Models\BusinessCategoryModel;
use Illuminate\Console\Command;

class InitBusinessCategoryCommand extends Command
{
    protected $signature = 'init-businessCategory';
    protected $description = '初始化业务名称';

    public function handle()
    {
        $arr = [
            1 => [
                [1, "玉翠珠宝", 901],
                [2, "钱币", 902],
                [3, "文玩杂项", 903],
                [4, "紫砂陶瓷", 904],
                [5, "木质珠串", 905],
                [6, "文房书画", 907],
            ],
            2 => [
                [7, "白酒", 1921],
                [8, "钱币", 1923],
                [9, "玉翠珠宝", 30001],
                [10, "青铜杂项", 30002],
                [11, "瓷器", 30003],
                [12, "箱包", 30004],
                [13, "木制品", 30005],
                [14, "名表", 30006],
                [15, "陨石", 30007],
                [16, "古籍善本", 30010],
                [17, "民俗藏品", 30011],
                [18, "奇石印章", 30012],
                [19,"书画", 30013],
                [20, "奢侈品饰品", 30014],
                [21, "牙骨角雕", 30015],
            ],
            // 小法庭
            3 => [
                [22, "古钱币", 9001],
                [23, "机制币", 9909],
                [24, "书法", 2011],
                [25, "国画", 2012],
                [26, "铜器", 909],
                [27, "古珠", 908],
                [28, "古玉", 907],
                [29, "瓷器", 4909],
            ],
            // 海外图文
            4 => [
                [30, "钱币", 1923],
                [31, "纸币", 1924],
            ],
            // 寄售评估
            5 => [
                [32, "玉翠珠宝", 30001],
                [33, "钱币", 1923],
                [34, "文玩杂项", 30002],
                [35, "紫砂陶瓷", 30003],
                [36, "木质珠串", 30005],
                [37, "文房书画", 30013],
            ],
            // 寄售查验
            6 => [
                [38, "玉翠珠宝", 1],
                [39, "文玩杂项", 3],
                [40, "木质珠串", 5],
                [41, "紫砂陶瓷", 4],
                [42, "文房书画", 7],
                [43, "钱币邮票", 2],
            ]
        ];

        $data = [];
        foreach ($arr as $businessId => $itemArr) {
            foreach ($itemArr as $item) {
                // 当前业务下存在分类
                $exist = BusinessCategoryModel::query()->where("business_id", $businessId)->where("category_identifier", $item[2])->exists();
                if ($exist) {
                    // 更新
                    $res = BusinessCategoryModel::query()->where("business_id", $businessId)->where("category_identifier", $item[2])->update([
                        "category_name" => $item[1]
                    ]);
                    if ($res) {
                        $this->info(sprintf("%s-%s-%s 更新成功", $businessId, $item[1], $item[2]));
                    }
                    continue;
                }

                // 不存在则新增
                $data[] = [
                    "id" => $item[0],
                    "business_id" => $businessId,
                    "category_name" => $item[1],
                    "category_identifier" => $item[2],
                    'create_time' => time(),
                ];
            }
        }

        if (empty($data)) {
            $this->info("没有需要新增的数据");
            return ;
        }

        BusinessCategoryModel::query()->insert($data);
        $this->info("数据插入完成");
    }

}