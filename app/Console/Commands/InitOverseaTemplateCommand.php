<?php

namespace App\Console\Commands;

use App\Constants\BusinessConst;
use App\Models\BusinessCategoryModel;
use App\Models\BusinessFieldModel;
use App\Models\BusinessFieldOptionsModel;
use App\Models\BusinessTemplateFieldModel;
use App\Models\BusinessTemplateModel;
use App\Service\BusinessCategoryService;
use Illuminate\Console\Command;

class InitOverseaTemplateCommand  extends Command
{
    protected $signature = 'init:oversea-template';
    protected $description = '初始化海外图文模板';

    public function handle()
    {
        $this->initOversea();
    }

    public function initOversea()
    {
        // 创建输入和输出模版
        $inputTemplateData = [
            'business_id' => BusinessConst::BUSINESS_TYPE_IMAGE_OVERSEAS,
            'template_name' => '海外图文输入模板',
            'biz_type' => 'input',
            'state' => 1,
            'create_time' => time(),
        ];
        $inputTemplateId = BusinessTemplateModel::query()->insertGetId($inputTemplateData);

        $outputTemplateData = [
            'business_id' => BusinessConst::BUSINESS_TYPE_IMAGE_OVERSEAS,
            'template_name' => '海外图文输出模板',
            'biz_type' => 'output',
            'state' => 1,
            'create_time' => time(),
        ];
        $outputTemplateId = BusinessTemplateModel::query()->insertGetId($outputTemplateData);


        // 创建输入field
        $imageField = [
            'business_id' => 4,
            'name' => '鉴定物品图片',
            'field_key' => 'imgs',
            'field_type' => 'imageList',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $imageFieldId = BusinessFieldModel::query()->insertGetId($imageField);

        $videoField = [
            'business_id' => 4,
            'name' => '鉴定物品视频',
            'field_key' => 'video',
            'field_type' => 'videoList',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $videFileId = BusinessFieldModel::query()->insertGetId($videoField);

        $remarkField = [
            'business_id' => 4,
            'name' => '用户备注',
            'field_key' => 'remark',
            'field_type' => 'textarea',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $remarkFieldId = BusinessFieldModel::query()->insertGetId($remarkField);

        $remarkTranslateField = [
            'business_id' => 4,
            'name' => '用户备注翻译',
            'field_key' => 'remarkTranslate',
            'field_type' => 'textarea',
            'placeholder' => '',
            'max_length' => 0,//todo
            'biz_type' => 'input',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $remarkTranslateFieldId = BusinessFieldModel::query()->insertGetId($remarkTranslateField);

        $rejectReasonField = [
            'business_id' => 4,
            'name' => '驳回理由',
            'field_key' => 'rejectReason',
            'field_type' => 'textarea',
            'placeholder' => '',
            'max_length' => 50,
            'biz_type' => 'input',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $rejectReasonFieldId = BusinessFieldModel::query()->insertGetId($rejectReasonField);

        $inputTemplateFieldData = [
            [
                'template_id' => $inputTemplateId,
                'field_id' => $imageFieldId,
                'field_key' => 'imgs',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $videFileId,
                'field_key' => 'video',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $remarkFieldId,
                'field_key' => 'remark',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $remarkTranslateFieldId,
                'field_key' => 'remarkTranslate',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $rejectReasonFieldId,
                'field_key' => 'rejectReason',
                'output_type' => 2,
                'create_time' => time(),
            ],
        ];
        BusinessTemplateFieldModel::query()->insert($inputTemplateFieldData);
        $this->info("输入模版id绑定field完成");


        // 创建输出field
        $this->handleOutputField($outputTemplateId);

        BusinessCategoryModel::query()
            ->where('business_id', 4)
            ->update([
            'input_template_id' => $inputTemplateId,
            'output_template_id' => $outputTemplateId,
        ]);
    }

    private function handleOutputField($outputTemplateId)
    {
        $countryField = [
            'business_id' => 4,
            'name' => '国家',
            'field_key' => 'country',
            'field_type' => 'input',
            'placeholder' => '下拉选项或者手动输入',
            'max_length' => 100,
            'biz_type' => 'output',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $countryFieldId = BusinessFieldModel::query()->insertGetId($countryField);

        $titleField = [
            'business_id' => 4,
            'name' => '名称',
            'field_key' => 'title',
            'field_type' => 'input',
            'placeholder' => '模糊输入名称',
            'max_length' => 100,
            'biz_type' => 'output',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $titleFieldId = BusinessFieldModel::query()->insertGetId($titleField);

        $faceValueField = [
            'business_id' => 4,
            'name' => '面额',
            'field_key' => 'faceValue',
            'field_type' => 'input',
            'placeholder' => '例：50',
            'max_length' => 15,
            'biz_type' => 'output',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $faceValueFieldId = BusinessFieldModel::query()->insertGetId($faceValueField);

        $faceValueUnitField = [
            'business_id' => 4,
            'name' => '面额单位',
            'field_key' => 'faceValueUnit',
            'field_type' => 'input',
            'placeholder' => '例：Cents',
            'max_length' => 15,
            'biz_type' => 'output',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $faceValueUnitFieldId = BusinessFieldModel::query()->insertGetId($faceValueUnitField);

        $yearsField = [
            'business_id' => 4,
            'name' => '年份',
            'field_key' => 'years',
            'field_type' => 'input',
            'placeholder' => '请输入年份',
            'max_length' => 30,
            'biz_type' => 'output',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $yearsFieldId = BusinessFieldModel::query()->insertGetId($yearsField);

        $perthMintField = [
            'business_id' => 4,
            'name' => '铸币厂',
            'field_key' => 'perthMint',
            'field_type' => 'input',
            'placeholder' => '例：Australia, Canberra',
            'max_length' => 30,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $perthMintFieldId = BusinessFieldModel::query()->insertGetId($perthMintField);

        $mintProcessField = [
            'business_id' => 4,
            'name' => '铸币工艺',
            'field_key' => 'mintProcess',
            'field_type' => 'singleSelect',
            'placeholder' => '',
            'max_length' => 30,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $mintProcessFieldId = BusinessFieldModel::query()->insertGetId($mintProcessField);
        $mintProcessOptions = [
            [
                'field_id' => $mintProcessFieldId,
                'option_name' => 'Proof',
                'create_time' => time(),
            ],
            [
                'field_id' => $mintProcessFieldId,
                'option_name' => 'Regular',
                'create_time' => time(),
            ],
            [
                'field_id' => $mintProcessFieldId,
                'option_name' => 'Sample',
                'create_time' => time(),
            ]
        ];
        BusinessFieldOptionsModel::query()->insert($mintProcessOptions);

        $gradeField = [
            'business_id' => 4,
            'name' => '等级',
            'field_key' => 'grade',
            'field_type' => 'input',
            'placeholder' => '例：MS',
            'max_length' => 50,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $gradeFieldId = BusinessFieldModel::query()->insertGetId($gradeField);

        $scoreField = [
            'business_id' => 4,
            'name' => '分值',
            'field_key' => 'score',
            'field_type' => 'input',
            'placeholder' => '例：70',
            'max_length' => 50,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $scoreFieldId = BusinessFieldModel::query()->insertGetId($scoreField);

        $lowValuationField = [
            'business_id' => 4,
            'name' => '最低估值',
            'field_key' => 'lowValuation',
            'field_type' => 'input',
            'placeholder' => '例：10',
            'max_length' => 50,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $lowValuationFieldId = BusinessFieldModel::query()->insertGetId($lowValuationField);

        $highValuationField = [
            'business_id' => 4,
            'name' => '最高估值',
            'field_key' => 'highValuation',
            'field_type' => 'input',
            'placeholder' => '例：100',
            'max_length' => 50,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $highValuationFieldId = BusinessFieldModel::query()->insertGetId($highValuationField);

        $finalContentField = [
            'business_id' => 4,
            'name' => '最终名称',
            'field_key' => 'finalContent',
            'field_type' => 'input',
            'placeholder' => '',
            'max_length' => 200,
            'biz_type' => 'output',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $finalContentFieldId = BusinessFieldModel::query()->insertGetId($finalContentField);

        $identResultField = [
            'business_id' => 4,
            'name' => '钱币评价',
            'field_key' => 'identResult',
            'field_type' => 'textarea',
            'placeholder' => '请输入评价',
            'max_length' => 2000,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $identResultFieldId = BusinessFieldModel::query()->insertGetId($identResultField);

        $voiceListField = [
            'business_id' => 4,
            'name' => '语音录入',
            'field_key' => 'voiceList',
            'field_type' => 'voiceInput',
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $voiceListFieldId = BusinessFieldModel::query()->insertGetId($voiceListField);

        $outTemplateFieldData = [
            [
                'template_id' => $outputTemplateId,
                'field_id' => $countryFieldId,
                'field_key' => 'country',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $titleFieldId,
                'field_key' => 'title',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $faceValueFieldId,
                'field_key' => 'faceValue',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $faceValueUnitFieldId,
                'field_key' => 'faceValueUnit',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $yearsFieldId,
                'field_key' => 'years',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $perthMintFieldId,
                'field_key' => 'perthMint',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $mintProcessFieldId,
                'field_key' => 'mintProcess',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $gradeFieldId,
                'field_key' => 'grade',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $scoreFieldId,
                'field_key' => 'score',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $lowValuationFieldId,
                'field_key' => 'lowValuation',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $highValuationFieldId,
                'field_key' => 'highValuation',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $finalContentFieldId,
                'field_key' => 'finalContent',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $identResultFieldId,
                'field_key' => 'identResult',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $voiceListFieldId,
                'field_key' => 'voiceList',
                'output_type' => 1,
                'create_time' => time(),
            ],
        ];
        BusinessTemplateFieldModel::query()->insert($outTemplateFieldData);
        $this->info("输出模版id绑定field完成");
    }
}