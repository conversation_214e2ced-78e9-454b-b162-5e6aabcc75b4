<?php

namespace App\Console\Commands;

use App\Models\BusinessCategoryModel;
use App\Models\BusinessFieldModel;
use App\Models\BusinessTemplateFieldModel;
use App\Models\BusinessTemplateModel;
use Illuminate\Console\Command;

class InitFwzTemplateCommand extends Command
{
    protected $signature = 'init:fwz-template';
    protected $description = '初始化服务站模板';

    public int $businessId = 5;
    public function handle()
    {
        $this->initFwz();
    }

    private function initFwz()
    {
        $imageField = [
            'business_id' => $this->businessId,
            'name' => '宝贝图片',
            'field_key' => 'imgs',
            'field_type' => 'imageList',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $imageFieldId = BusinessFieldModel::query()->insertGetId($imageField);

        $videoField = [
            'business_id' => $this->businessId,
            'name' => '宝贝视频',
            'field_key' => 'video',
            'field_type' => 'videoList',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $videFileId = BusinessFieldModel::query()->insertGetId($videoField);

        $sizeField = [
            'business_id' => $this->businessId,
            'name' => '尺寸',
            'field_key' => 'size',
            'field_type' => 'input',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $sizeFieldId = BusinessFieldModel::query()->insertGetId($sizeField);

        $weightField = [
            'business_id' => $this->businessId,
            'name' => '重量',
            'field_key' => 'weight',
            'field_type' => 'input',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $weightFieldId = BusinessFieldModel::query()->insertGetId($weightField);

        $ageField = [
            'business_id' => $this->businessId,
            'name' => '年代',
            'field_key' => 'age',
            'field_type' => 'input',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $ageFieldId = BusinessFieldModel::query()->insertGetId($ageField);

        $materialField = [
            'business_id' => $this->businessId,
            'name' => '镶嵌材料',
            'field_key' => 'material',
            'field_type' => 'input',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];

        $materialFieldId = BusinessFieldModel::query()->insertGetId($materialField);

        $descriptionField = [
            'business_id' => $this->businessId,
            'name' => '宝贝描述',
            'field_key' => 'description',
            'field_type' => 'textarea',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $descriptionFieldId = BusinessFieldModel::query()->insertGetId($descriptionField);

        $identTitleField = [
            'business_id' => $this->businessId,
            'name' => '标题',
            'field_key' => 'identTitle',
            'field_type' => 'textarea',
            'placeholder' => '',
            'max_length' => 0,
            'biz_type' => 'input',
            'is_required' => 1,
            'create_time' => time(),
        ];
        $identTitleFieldId = BusinessFieldModel::query()->insertGetId($identTitleField);

        $evaluateField = [
            'business_id' => $this->businessId,
            'name' => '属性信息',
            'field_key' => 'evaluate',
            'field_type' => 'evaluateAttr',
            'placeholder' => '',
            'min_length' => 0,
            'max_length' => 0,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $evaluateFieldId = BusinessFieldModel::query()->insertGetId($evaluateField);

        $priceField = [
            'business_id' => $this->businessId,
            'name' => '宝贝评估价',
            'field_key' => 'price',
            'field_type' => 'input',
            'placeholder' => '',
            'min_length' => 0,
            'max_length' => 0,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $priceFieldId = BusinessFieldModel::query()->insertGetId($priceField);

        $identResultField = [
            'business_id' => $this->businessId,
            'name' => '评估结论',
            'field_key' => 'identResult',
            'field_type' => 'textarea',
            'placeholder' => '请输入评估结论',
            'min_length' => 0,
            'max_length' => 200,
            'biz_type' => 'output',
            'is_required' => 0,
            'create_time' => time(),
        ];
        $identResultFieldId = BusinessFieldModel::query()->insertGetId($identResultField);


        $inputTemplateData = [
            'business_id' => $this->businessId,
            'template_name' => '服务站输入模板',
            'biz_type' => 'input',
            'state' => 1,
            'create_time' => time(),
        ];
        $inputTemplateId = BusinessTemplateModel::query()->insertGetId($inputTemplateData);

        $outputTemplateData = [
            'business_id' => $this->businessId,
            'template_name' => '服务站输出模板',
            'biz_type' => 'output',
            'state' => 1,
            'create_time' => time(),
        ];
        $outputTemplateId = BusinessTemplateModel::query()->insertGetId($outputTemplateData);

        $inputTemplateFieldData = [
            [
                'template_id' => $inputTemplateId,
                'field_id' => $imageFieldId,
                'field_key' => 'imgs',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $videFileId,
                'field_key' => 'video',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $sizeFieldId,
                'field_key' => 'size',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $weightFieldId,
                'field_key' => 'weight',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $ageFieldId,
                'field_key' => 'age',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $materialFieldId,
                'field_key' => 'material',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $descriptionFieldId,
                'field_key' => 'description',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $inputTemplateId,
                'field_id' => $identTitleFieldId,
                'field_key' => 'identTitle',
                'output_type' => 1,
                'create_time' => time(),
            ]
        ];

        BusinessTemplateFieldModel::query()->insert($inputTemplateFieldData);

        $outputTemplateFieldData = [
            [
                'template_id' => $outputTemplateId,
                'field_id' => $evaluateFieldId,
                'field_key' => 'evaluate',
                'output_type' => 2,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $priceFieldId,
                'field_key' => 'price',
                'output_type' => 1,
                'create_time' => time(),
            ],
            [
                'template_id' => $outputTemplateId,
                'field_id' => $identResultFieldId,
                'field_key' => 'identResult',
                'output_type' => 1,
                'create_time' => time(),
            ],
        ];

        BusinessTemplateFieldModel::query()->insert($outputTemplateFieldData);

        BusinessCategoryModel::query()->where('business_id', $this->businessId)->update([
            'input_template_id' => $inputTemplateId,
            'output_template_id' => $outputTemplateId,
        ]);
    }
}
