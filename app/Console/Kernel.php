<?php

namespace App\Console;

use App\Console\Commands\Demo;
use App\Console\Commands\GenerateGroupSchedulingCommand;
use App\Console\Commands\IdentResultNotifyCommand;
use App\Console\Commands\InitBusinessCategoryCommand;
use App\Console\Commands\InitBusinessCommand;
use App\Console\Commands\InitFwzTemplateCommand;
use App\Console\Commands\InitOverseaTemplateCommand;
use App\Console\Commands\SyncAllFwzOrderCommand;
use App\Console\Commands\SyncAllOverseaOrderCommand;
use App\Console\Commands\SyncCommunityAppraiserBusinessCommand;
use App\Console\Commands\SyncEvaluatePermissionCommand;
use App\Console\Commands\SyncGroupCommand;
use App\Console\Commands\SyncOldLiveOrderCommand;
use App\Console\Commands\SyncOverseaImgCommand;
use App\Console\Commands\WashCommunityToAppraiserCommand;
use Spin\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * 需要运行的脚本 直接注册即可 无需任何多余操作
     * 无需添加task start 和 task end
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Demo::class,
        InitBusinessCommand::class,
        InitBusinessCategoryCommand::class,
        SyncCommunityAppraiserBusinessCommand::class,
        WashCommunityToAppraiserCommand::class,
        IdentResultNotifyCommand::class,
        SyncOverseaImgCommand::class,
        SyncOldLiveOrderCommand::class,
        SyncAllOverseaOrderCommand::class,
        InitOverseaTemplateCommand::class,
        SyncEvaluatePermissionCommand::class,
        SyncGroupCommand::class,
        GenerateGroupSchedulingCommand::class,
        InitFwzTemplateCommand::class,
        SyncAllFwzOrderCommand::class,
    ];
}
