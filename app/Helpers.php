<?php
declare(strict_types=1);


use App\ConstDir\BaseConst;
use App\Utils\DeviceUtil;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

if (!function_exists('arrayToObject')) {
    function arrayToObject($arr)
    {
        return json_decode(json_encode($arr));
    }
}

if (!function_exists('arraySortByKey')) {
    /**
     * 二维数组按固定的唯一值元素排序
     * @param $array
     * @param $keys
     * @param $sort
     * @return array
     */
    function arraySortByKey($array, $keys, $sort = 'asc')
    {
        $array = json_decode(json_encode($array), true);

        $keysValue = $newArray = array();

        foreach ($array as $k => $v) {
            $keysValue[$k] = $v[$keys];
        }
        if ($sort == 'asc') {
            asort($keysValue);
        } else {
            arsort($keysValue);
        }
        reset($keysValue);
        foreach ($keysValue as $k => $v) {
            $newArray[$k] = $array[$k];
        }

        return array_values($newArray);
    }
}

if (!function_exists('get_property')) {
    function get_property($obj, $property, $default = null)
    {
        if (!$obj) {
            return $default;
        }
        if (is_string($obj)) {
            $obj = json_decode($obj, true);
        }
        if (is_object($obj)) {
            return property_exists($obj, $property) || isset($obj->$property) ? $obj->$property : $default;
        }

        // 注意⚠：️ 当 $obj[$property] 的值为 NULL 时 isset 返回的是 false。
        // 也就是说，当 $obj 中存在 $property 但是值为 NULL 时，不会返回 NULL，而是返回 $default
        return isset($obj[$property]) ? $obj[$property] : $default;
    }
}

if (!function_exists('fenToYuan')) {
    /**
     * 金额分转元
     * 转化金额 100010分  ->   1000.10 元
     * @param $money
     * @return string
     */
    function fenToYuan($money)
    {
        if (empty($money)) {
            return 0;
        }
        $res = bcdiv((string)$money, '100', 2);
        return $money % 100 == 0 ? (int)$res : $res;
    }
}

if (!function_exists('convert2cents')) {
    /**
     * 金额元转分
     * 转化金额 1000.10 元  ->   100010分
     * @param $money
     * @return int
     */
    function convert2cents($money): int
    {
        return (int)bcmul((string)$money, '100');
    }
}


/**
 * @将对象转化为数组
 * @$obj
 *
 */
if (!function_exists('objectToArray')) {
    function objectToArray($obj)
    {
        $arr = is_object($obj) ? get_object_vars($obj) : $obj;
        if (is_array($arr)) {
            return array_map(__FUNCTION__, $arr);
        } else {
            return $arr;
        }
    }
}

if (!function_exists('wpt_json_encode')) {
    /**
     * @param $arr
     * @return false|string
     */
    function wpt_json_encode($arr)
    {
        return json_encode($arr, JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES);
    }
}

if (!function_exists('is_https')) {
    function is_https(): bool
    {
        if (get_property($_SERVER, 'HTTPS', '') !== 'off'
            || get_property($_SERVER, 'HTTP_X_CLIENT_PROTO', '') === 'https'
            || get_property($_SERVER, 'HTTP_X_FORWARDED_PROTO', '') === 'https'
            || get_property($_SERVER, 'HTTP_FRONT_END_HTTPS', '') !== 'off') {
            return true;
        }

        return false;
    }
}

if (!function_exists('get_env_array')) {
    function get_env_array($key, $isRand = false)
    {
        $value = env($key);
        if (!is_null($value)) {
            $value = explode(',', $value);
            if ($isRand) {
                shuffle($value);
            }
        }

        return $value;
    }
}

if (!function_exists('array_only')) {
    function array_only($array, $keys): array
    {
        return array_intersect_key($array, array_flip((array) $keys));
    }
}

if (!function_exists('get_value')) {
    /**
     * 获取值
     *
     * 1) 两个参数 第一个为参数 第二个为默认值
     * 2) 三个参数 数组或对象 第一个为参数 第二个为键 第三个为默认值
     * 3) 四个参数 数组或对象 第一个为参数 第二个为键 第三个为默认值 第四个为回调函数意在对结果进行进一步处理
     *
     * @param $_data
     * @param $_key
     * @param null $_default_value
     * @return mixed|null
     */
    function get_value()
    {
        $args = func_get_args();
        $value = null;
        switch (count($args)) {
            case 2:
                //单一变量
                list($_data, $_default_value) = $args;
                $value = $_default_value;
                if (!is_null($_data)) {
                    $value = $_data;
                }
                break;
            case 3:
                //数组或对象
                list($_data, $_key, $_default_value) = $args;
                $value = $_default_value;
                if (is_array($_data)) {
                    $value = $_data[$_key] ?? $value;
                } elseif (is_object($_data)) {
                    $value = property_exists($_data, $_key) ? $_data->$_key : $value;
                }
                break;
            case 4:
                //数组或对象+回调函数
                list($_data, $_key, $_default_value, $_closure) = $args;
                $value = $_default_value;
                if (is_array($_data)) {
                    $value = $_data[$_key] ?? $value;
                } elseif (is_object($_data)) {
                    $value = property_exists($_data, $_key) ? $_data->$_key : $value;
                }
                $value = ('Closure' == get_class($_closure)) ? $_closure($value) : $value;
                break;
            default:
                break;
        }

        return $value;
    }
}

if (! function_exists('starts_with')) {
    /**
     * Determine if a given string starts with a given substring.
     *
     * @param  string  $haystack
     * @param  string|array  $needles
     * @return bool
     */
    function starts_with($haystack, $needles)
    {
        return Str::startsWith($haystack, $needles);
    }
}

if (!function_exists('sub_str')) {
    /**
     * 截取UTF-8编码下字符串的函数
     *
     * @param   string $str 被截取的字符串
     * @param   int $length 截取的长度
     * @param   bool $append 是否附加省略号
     *
     * @return  string
     */
    function sub_str($str, $length = 0, $append = true)
    {
        $str = str_replace("\n", '', str_replace("\r", '', trim($str)));
        $strlength = strlen($str);

        if ($length == 0 || $length >= $strlength) {
            return $str;
        } elseif ($length < 0) {
            $length = $strlength + $length;
            if ($length < 0) {
                $length = $strlength;
            }
        }

        if (function_exists('mb_substr')) {
            $newstr = mb_substr($str, 0, $length, 'utf-8');
        } elseif (function_exists('iconv_substr')) {
            $newstr = iconv_substr($str, 0, $length, 'utf-8');
        } else {
            $newstr = substr($str, 0, $length);
        }

        if ($append && $str != $newstr) {
            $newstr .= '...';
        }

        return $newstr;
    }
}

//价格格式化 向下取整
if (!function_exists('int_format')) {
    function int_format($val)
    {
        return intval(number_format((float)$val, 2, ".", ""));
    }
}

//价格格式化 向上取整，当有上限不能超上限
if (!function_exists('ceil_format')) {
    function ceil_format($val, $limit = null)
    {
        $val = ceil(number_format((float)$val, 2, ".", ""));
        if (!is_null($limit)) {
            $val = min($val, $limit);
        }
        return $val;
    }
}

//千分位 价格格式化 如果没有小数部分，则舍去，如果有小数部分 保留两位 单位元
if (!function_exists('thousand_format')) {
    function thousand_format($val)
    {
        $val = convert2cents($val);
        if ($val % 100 == 0) {
            $val = number_format((float)fenToYuan($val));
        } elseif ($val % 10 == 0) {
            $val = number_format((float)fenToYuan($val), 1);
        } else {
            $val = number_format((float)fenToYuan($val), 2);
        }
        return $val;
    }
}

if (!function_exists("is_json")) {
    /**
     * 验证一个字符串是否为json
     * @param string $string
     * @return bool
     */
    function is_json(string $string) :bool
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
}


if (!function_exists("multi_array_to_simple")) {
    /**
     * 递归将树状数组转成平级数组
     * 输入： [["id" => 1, "child" => ["id" => 2, "child" => ["id" => 3, "child" => []]]]]
     * 输出： [["id" => 1], ["id" => 2], ["id" => 3]]
     * @param array $array
     * @param string $key
     * @param array $newArray
     * @return array
     */
    function multi_array_to_simple(array $array, string $key = 'child', array $newArray = []): array
    {
        foreach ($array as $item) {
            if (!empty($item[$key])) {
                $newArray = multi_array_to_simple($item[$key], $key, $newArray);
            }

            unset($item[$key]);
            $newArray[] = $item;
        }

        return $newArray;
    }
}

if (!function_exists("line_to_hump")) {
    /**
     * 下划线转驼峰
     * @param string $key
     * @param string $separator
     * @return string
     */
    function line_to_hump(string $key, string $separator = '_'): string
    {
        $words = $separator . str_replace($separator, " ", $key);

        return ltrim(str_replace(" ", "", ucwords($words)), $separator);
    }
}

if (!function_exists('isGray')) {
    /**
     * 判断当前是否在灰度环境
     *
     * @return bool
     * <AUTHOR>
     */
    function isGray()
    {
        return env('ENV') === 'gray';
    }
}

if (!function_exists("equal")) {
    /**
     * @param $a
     * @param $b
     * @return bool
     */
    function equal($a, $b): bool
    {
        return $a == $b;
    }
}

if (!function_exists("isScene")) {
    /**
     * @param $scene
     * @param $profileScene
     * @return bool
     */
    function isScene($scene, $profileScene): bool
    {
        $sceneArr = explode(';', $profileScene);
        if (in_array($scene, $sceneArr)) {
            return true;
        }
        return false;
    }
}

if (!function_exists("isIntersectScene")) {
    /**
     * 注意保留原来的key
     * @param $sceneArr
     * @param $profileScene
     * @return array
     */
    function isIntersectScene($sceneArr, $profileScene): array
    {
        $profileSceneArr = explode(';', $profileScene);
        return array_intersect($sceneArr, $profileSceneArr);
    }
}

if (!function_exists('filterJSON')) {
    //把会导致json_decode失败的特殊字符替换成空，例子：长乐�无求
    function filterJSON($data, $br = false)
    {
        if ($br) {
            $data = preg_replace(
                '/[\x00-\x08\x10\x0B\x0C\x0E-\x19\x7F]' .
                '|[\x00-\x7F][\x80-\xBF]+' .
                '|\n+' .
                '|([\xC0\xC1]|[\xF0-\xFF])[\x80-\xBF]*' .
                '|[\xC2-\xDF]((?![\x80-\xBF])|[\x80-\xBF]{2,})' .
                '|[\xE0-\xEF](([\x80-\xBF](?![\x80-\xBF]))|(?![\x80-\xBF]{2})|[\x80-\xBF]{3,})/S',
                '',
                $data
            );
        } else {
            $data = preg_replace(
                '/[\x00-\x08\x10\x0B\x0C\x0E-\x19\x7F]' .
                '|[\x00-\x7F][\x80-\xBF]+' .
                '|([\xC0\xC1]|[\xF0-\xFF])[\x80-\xBF]*' .
                '|[\xC2-\xDF]((?![\x80-\xBF])|[\x80-\xBF]{2,})' .
                '|[\xE0-\xEF](([\x80-\xBF](?![\x80-\xBF]))|(?![\x80-\xBF]{2})|[\x80-\xBF]{3,})/S',
                '',
                $data
            );
        }
        return preg_replace('/\xE0[\x80-\x9F][\x80-\xBF]|\xED[\xA0-\xBF][\x80-\xBF]/S', '', $data);
    }
}



if (!function_exists('gToKg')) {
    /**
     * 克转化为千克
     * 转化 1011克  ->   1.01千克
     * @param $g
     * @return string
     */
    function gToKg($g)
    {
        if (empty($g)) {
            return 0;
        }
        $res = bcdiv((string)$g, (string)BaseConst::G_TO_KG, 2);
        return $g % 1000 == 0 ? (int)$res : $res;
    }
}


if (!function_exists('getWukongTagWechat')) {
    /**
     * @return string
     */
    function getWukongTagWechat()
    {
        $tag = strtolower(env('ENV', '')) == 'gray' ? '-gray' : '';
        return 'k8s-crond-api' . $tag;
    }
}

if (!function_exists('getMillisecond')) {
    /**
     * @return string
     */
    function getMillisecond()
    {
        list($s1, $s2) = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
    }
}

if (!function_exists('is_gray')) {
    /**
     * 判断是否是灰度环境
     *
     * @return string
     */
    function is_gray()
    {
        return strtolower(env('ENV', '')) == 'gray';
    }
}

if (!function_exists('is_test')) {
    /**
     * 判断是否是测试环境
     *
     * @return string
     */
    function is_test()
    {
        return !is_gray() && !is_prod();
    }
}


if (!function_exists('is_prod')) {
    /**
     * 判断是否是测试环境
     *
     * @return bool
     */
    function is_prod()
    {
        return strtolower(env('ENV', '')) == 'prod';
    }
}

if (!function_exists('isCli')) {
    /**
     * Run from cli mode
     * @return bool
     */
    function isCli(): bool
    {
        return PHP_SAPI == 'cli';
    }
}

if (!function_exists('buildRandomStr')) {
    /**
     * @param $len
     * @param $prefix
     * @param $withDateTime
     * @return string
     */
    function buildRandomStr($len = 32, $prefix = "", $withDateTime = 'ymdHis'): string
    {
        $chars = "abcdefghijklmnopqrstuvwxyz0123456789";
        // 随机清洗
        $chars = str_shuffle($chars);
        $str = "";
        $secondPart = "";
        if ($withDateTime) {
            $secondPart = date($withDateTime);
        }
        $strLength = $len - strlen($prefix . $secondPart);
        for ($i = 0; $i < $strLength; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $prefix . $secondPart . $str;
    }
}

if (! function_exists('array_first')) {
    /**
     * Return the first element in an array passing a given truth test.
     *
     * @param  array  $array
     * @param  callable|null  $callback
     * @param  mixed  $default
     * @return mixed
     */
    function array_first($array, callable $callback = null, $default = null)
    {
        return Arr::first($array, $callback, $default);
    }
}

if (!function_exists('get_img_url')) {
    /**
     * 获取图片地址
     * @param string $img
     * @param string $cdnUrl
     * @param string $oldCdnUrl
     * @return string
     */
    function get_img_url($img = "", $cdnUrl = "", $oldCdnUrl = "http://cdn.weipaitang.com/static/")
    {
        if (!$img) {
            return "";
        }
        if (strstr($img, $cdnUrl) || strstr($img, $oldCdnUrl) || strstr($img, "weipaitang.com/")) {
            return $img;
        }
        if (strstr($img, "/")) {
            return $cdnUrl . $img;
        }
        return $oldCdnUrl . $img;
    }
}


if (! function_exists('array_get')) {
    /**
     * Get an item from an array using "dot" notation.
     *
     * @param  \ArrayAccess|array  $array
     * @param  string  $key
     * @param  mixed   $default
     * @return mixed
     */
    function array_get($array, $key, $default = null)
    {
        return Arr::get($array, $key, $default);
    }
}

if (! function_exists('array_forget')) {
    /**
     * Remove one or many array items from a given array using "dot" notation.
     *
     * @param  array  $array
     * @param  array|string  $keys
     * @return void
     */
    function array_forget(&$array, $keys)
    {
        return Arr::forget($array, $keys);
    }
}

if (! function_exists('array_set')) {
    /**
     * Set an array item to a given value using "dot" notation.
     *
     * If no key is given to the method, the entire array will be replaced.
     *
     * @param  array   $array
     * @param  string  $key
     * @param  mixed   $value
     * @return array
     */
    function array_set(&$array, $key, $value)
    {
        return Arr::set($array, $key, $value);
    }
}

if (! function_exists('array_pluck')) {
    /**
     * Pluck an array of values from an array.
     *
     * @param  array   $array
     * @param  string|array  $value
     * @param  string|array|null  $key
     * @return array
     */
    function array_pluck($array, $value, $key = null)
    {
        return Arr::pluck($array, $value, $key);
    }
}

if (!function_exists('checkIsWechatApp')) {
    /**
     * 检查是否是微信小程序
     *
     * @return bool
     * <AUTHOR>
     */
    function checkIsWechatApp()
    {
        //小程序新版本
        if (DeviceUtil::PLATFORM_WECHAT_APP == DeviceUtil::getPlatform() || DeviceUtil::isWechatSmallProgram()) {
            return true;
        }

        return false;
    }
}

if (!function_exists('mb_trim')) {

    function mb_trim($string, $trimChars = '\s')
    {
        return preg_replace('/^[' . $trimChars . ']*(?U)(.*)[' . $trimChars . ']*$/u', '\\1', $string);
    }
}

//图片拼接
if (!function_exists('combineImgUrl')) {
    function combineImgUrl($image, $size = 0)
    {
        if (empty($image)) {
            return '';
        }
        if (preg_match('/http:|https:/i', $image)) {
            return $image;
        }
        $url = '';
        $cdnUrl = config('app.CDNURL');
        // 判断$image字段是否含有jzg/，是则表明为新版存储方式,直接在前面拼接domain就好了
        if (preg_match('/\//i', $image)) {
            //去掉第一个/
            if (strpos($image, '/') === 0) {
                $image = substr($image, 1);
            }
            $url .= $cdnUrl . $image;
        } else {
            $url = $cdnUrl . 'img/' . $image;
        }
        if ($size > 0) {
            $url .= '/w/' . $size;
        }
        return $url;
    }
}

if (!function_exists('combineMogrImgUrl')) {
    function combineMogrImgUrl($image, $action = 'crop', $width = 420, $height = 0, $position = 'center')
    {
        if (empty($image)) {
            return '';
        }
        if (stripos($image, 'weipaitang.com') === false) {
            return $image;
        }
        //?imageMogr2/crop/400x400/gravity/center
        $image .= '?imageMogr2/';
        switch ($action) {
            case 'crop':
                $image .= 'crop/' . $width . 'x' . $height . 'gravity/' . $position;
                break;
            case 'iradius':
                $image .= 'iradius/' . $width;
                break;
            default;
        }

        return $image;
    }
}

if (!function_exists('read_file')) {
    /**
     * 读取文件
     * @param string $fileName
     * @param callable $fn
     * @return void
     */
    function read_file(string $fileName, callable $fn)
    {
        if (empty($fileName)) {
            return;
        }
        $file = fopen($fileName, 'r');
        while (!feof($file)) {
            $line = fgets($file);
            if (empty($line)) {
                continue;
            }

            $fn($line);
        }
    }
}