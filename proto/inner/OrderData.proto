syntax = "proto3";

package order_data;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service OrderData {

    rpc GetBusinessOrderData (InputGetBusinessOrderData) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/order-data/get-business-order-data"
        };
    }

    rpc Center (InputCenter) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/order-data/center"
        };
    }
}

message InputGetBusinessOrderData {}
message InputCenter {}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}
