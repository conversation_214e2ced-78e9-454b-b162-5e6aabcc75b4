syntax = "proto3";

package field;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service Field {

    rpc List (InputList) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/field/list"
        };
    }

    rpc Create (InputCreate) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/field/create"
            body: "*"
        };
    }

    rpc Edit (InputEdit) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/field/edit"
            body: "*"
        };
    }

    rpc Deleted (InputDeleted) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/field/deleted"
            body: "*"
        };
    }

    rpc Enable (InputEnable) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/field/enable"
            body: "*"
        };
    }

}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

message InputList {}

message InputCreate {}

message InputEdit {}

message InputDeleted {}

message InputEnable {}
