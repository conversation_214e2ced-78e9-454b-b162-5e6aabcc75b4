syntax = "proto3";

// 服务名
package appraiser_dispatch;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service AppraiserDispatch {
    // 获取派单鉴定师列表
    rpc List (InputList) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/appraiser-dispatch/list"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

message InputList {
}