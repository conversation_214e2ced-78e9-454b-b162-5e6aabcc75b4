syntax = "proto3";

package appraiser_group;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service AppraiserGroup {
    // 获取排班信息
    rpc ScheduleInfo (InputScheduleInfo) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/appraiser-group/schedule-info"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

message InputScheduleInfo {}