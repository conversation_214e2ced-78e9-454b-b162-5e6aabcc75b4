syntax = "proto3";

package template;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service Template {

    rpc List (InputList) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/template/list"
        };
    }

    rpc Create (InputCreate) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/template/create"
            body: "*"
        };
    }

    rpc Edit (InputEdit) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/template/edit"
            body: "*"
        };
    }

    rpc Detail (InputDetail) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/template/detail"
            body: "*"
        };
    }

    rpc Enable (InputEnable) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/template/enable"
            body: "*"
        };
    }

    rpc AddField (InputAddField) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/template/addField"
            body: "*"
        };
    }

    rpc EditField (InputEditField) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/template/editField"
            body: "*"
        };
    }

    rpc DelField (InputDelField) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/template/delField"
            body: "*"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

message InputList {}

message InputCreate {}

message InputEdit {}

message InputDetail {}

message InputEnable {}

message InputAddField {}

message InputEditField {}

message InputDelField {}
