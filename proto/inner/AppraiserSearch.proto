syntax = "proto3";

// 服务名
package appraiser_search;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service AppraiserSearch {
    // 根据userinfoId获取鉴定师详情
    rpc DetailByUserinfoId (InputDetailByUserinfoId) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/appraiser-search/detail-by-userinfoId"
        };
    }

	// 根据userinfoIds获取鉴定师列表
	rpc ListByUserinfoIds (InputListByUserinfoIds) returns (OutputBase) {
		option (google.api.http) = {
			get: "/inner/appraiser-search/list-by-userinfoIds"
		};
	}

    // 根据userinfoIds获取鉴定师基础列表
    rpc SimpleListByUserinfoIds (InputSimpleListByUserinfoIds) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/appraiser-search/simple-list-by-userinfoIds"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

// 根据userinfoId查询详情请求
message InputDetailByUserinfoId {
}

message InputListByUserinfoIds {
}

message InputSimpleListByUserinfoIds {
}
