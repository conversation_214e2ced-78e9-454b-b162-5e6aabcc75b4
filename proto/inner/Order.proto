syntax = "proto3";

package order;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service Order {

    rpc List (InputList) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/order/list"
        };
    }

    rpc Create (InputCreate) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/order/create"
            body: "*"
        };
    }

    rpc Detail (InputDetail) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/order/detail"
        };
    }

    rpc SimpleDetail (InputDetail) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/order/simple-detail"
        };
    }

    rpc BatchSimpleDetail (InputDetail) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/order/batch-simple-detail"
        };
    }

    rpc GetParams (InputGetParams) returns (OutputBase) {
        option (google.api.http) = {
            get: "/inner/order/get-params"
        };
    }

    rpc Submit (InputSubmit) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/order/submit"
            body: "*"
        };
    }

    rpc Cancel (InputCancel) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/order/cancel"
            body: "*"
        };
    }

	rpc Reject (InputReject) returns (OutputBase) {
		option (google.api.http) = {
			post: "/inner/order/reject"
			body: "*"
		};
	}

    rpc AssignAppraiser (InputAssignAppraiser) returns (OutputBase) {
        option (google.api.http) = {
            post: "/inner/order/assign-appraiser"
            body: "*"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}


message InputList {}

message InputCreate {}

message InputDetail {}

message BatchSimpleDetail {}

message SimpleDetail {}

message InputGetParams {}

message InputSubmit {}

message InputCancel {}

message InputAssignAppraiser {}

message InputReject {}
