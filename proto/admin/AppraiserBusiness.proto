syntax = "proto3";

// 服务名
package appraiser_business;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service AppraiserBusiness {
    // 编辑鉴定师业务类目
    rpc Edit (InputEdit) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/appraiser-business/edit"
            body: "*"
        };
    }

    // 获取鉴定师业务类目详情
    rpc Detail (InputDetail) returns (OutputBase) {
        option (google.api.http) = {
            get: "/admin/appraiser-business/detail"
        };
    }

    // 获取鉴定师对应的业务
    rpc QueryAppraiserBusiness (InputQueryAppraiserBusiness) returns (OutputBase) {
        option (google.api.http) = {
            get: "/admin/appraiser-business/query-appraiser-business"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

message InputEdit {
}

message InputDetail {}

message InputQueryAppraiserBusiness{}
