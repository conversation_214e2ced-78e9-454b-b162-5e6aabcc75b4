syntax = "proto3";

// 服务名
package business_category;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service BusinessCategory {
    // 添加业务类目
    rpc Add (InputAdd) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/business-category/add"
            body: "*"
        };
    }

    // 获取业务下的类目列表
	rpc List (InputList) returns (OutputBase) {
		option (google.api.http) = {
			get: "/admin/business-category/list"
		};
	}

    // 获取业务下的类目列表
    rpc ListByBusiness (InputListByBusiness) returns (OutputBase) {
        option (google.api.http) = {
            get: "/admin/business-category/list-by-business"
        };
    }

    // 编辑业务类目
    rpc Edit (InputEdit) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/business-category/edit"
            body: "*"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

message InputAdd {
}

message InputList {
}

message InputListByBusiness {
}

message InputEdit {
} 