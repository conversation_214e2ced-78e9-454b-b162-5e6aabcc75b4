syntax = "proto3";

// 服务名
package business;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service Business {
    // 添加业务
    rpc Add (InputAdd) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/business/add"
            body: "*"
        };
    }

    // 获取业务列表
    rpc List (InputList) returns (OutputBase) {
        option (google.api.http) = {
            get: "/admin/business/list"
        };
    }

    // 编辑业务信息
    rpc Edit (InputEdit) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/business/edit"
            body: "*"
        };
    }

    // 开关业务状态
    rpc Enable (InputEnable) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/business/enable"
            body: "*"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

message InputAdd {
}

message InputList {
}

message InputEdit {
}

message InputEnable {
} 