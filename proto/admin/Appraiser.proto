syntax = "proto3";

// 服务名
package appraiser;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service Appraiser {
    // 创建鉴定师
    rpc Create (InputCreate) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/appraiser/create"
            body: "*"
        };
    }

    // 获取鉴定师详情
    rpc Detail (InputDetail) returns (OutputBase) {
        option (google.api.http) = {
            get: "/admin/appraiser/detail"
        };
    }

    // 编辑鉴定师信息
    rpc Edit (InputEdit) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/appraiser/edit"
            body: "*"
        };
    }

    // 设置休息状态
	rpc EditRest (InputEditRest) returns (OutputBase) {
		option (google.api.http) = {
			post: "/admin/appraiser/edit-rest"
			body: "*"
		};
	}

    // 开关鉴定师状态
    rpc Enable (InputEnable) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/appraiser/enable"
            body: "*"
        };
    }

    // 获取鉴定师列表
    rpc List (InputList) returns (OutputBase) {
        option (google.api.http) = {
            get: "/admin/appraiser/list"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

message InputCreate {
}

message InputDetail {
}

message InputEdit {
}

message InputEditRest {
}

message InputEnable {
}

message InputList {
}


