syntax = "proto3";

// 服务名
package appraiser_group;

import "github.com/gogo/protobuf/gogoproto/gogo.proto";
import "google/api/annotations.proto";

service AppraiserGroup {
    // 获取相关选择数据
    rpc SelectInfo (InputSelectInfo) returns (OutputBase) {
        option (google.api.http) = {
            get: "/admin/appraiser-group/select-info"
        };
    }

    // 开启分组
    rpc Add (InputAdd) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/appraiser-group/add"
            body: "*"
        };
    }

    // 分组详情
	rpc Detail (InputDetail) returns (OutputBase) {
		option (google.api.http) = {
			get: "/admin/appraiser-group/detail"
		};
	}

    // 关闭分组
    rpc Close (InputClose) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/appraiser-group/close"
            body: "*"
        };
    }

    // 排班列表
    rpc ScheduleList (InputScheduleList) returns (OutputBase) {
        option (google.api.http) = {
            get: "/admin/appraiser-group/schedule-list"
        };
    }

    // 排班设置
    rpc ScheduleEdit (InputScheduleEdit) returns (OutputBase) {
        option (google.api.http) = {
            post: "/admin/appraiser-group/schedule-edit"
            body: "*"
        };
    }
}

message OutputBase {
    // 状态码
    int32 code = 1;
    // 信息
    string msg = 2;
    // 时间戳
    int64 nowTime = 3;
    // 请求串
    string requestId = 4;
    // 数据
    map<string, string> data = 5;
}

message InputSelectInfo {}

message InputAdd {}

message InputDetail {}

message InputClose {}

message InputScheduleList {}

message InputScheduleEdit {}
