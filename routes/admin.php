<?php
/** @var \Spin\Routing\Router $router */

// 鉴定师
$router->group(
    ['prefix' => 'admin/appraiser', 'namespace' => 'Admin', 'middleware' => []],
    function ($router) {
        // todo binlog到接口提交
        //$router->any('create', 'AppraiserController@create');   // 鉴定师创建
        $router->any('detail', 'AppraiserController@detail');   // 鉴定师详情
        $router->any('edit', 'AppraiserController@edit');       // 编辑
        $router->any('edit-rest', 'AppraiserController@editRest');   // 休息设置
        //$router->any('enable', 'AppraiserController@enable');   // 开关
        $router->any('list', 'AppraiserController@list');   // 列表
        //$router->any('bind-affiliation', 'AppraiserController@bindAffiliation');   // 绑定员工
    }
);

// 鉴定师业务
$router->group(
    ['prefix' => 'admin/appraiser-business', 'namespace' => 'Admin', 'middleware' => []],
    function ($router) {
        $router->any('query-appraiser-business', 'AppraiserBusinessController@queryAppraiserBusiness');
    }
);

// 接入的业务
$router->group(
    ['prefix' => 'admin/business', 'namespace' => 'Admin', 'middleware' => []],
    function ($router) {
        $router->any('add', 'BusinessController@add');
        $router->any('list', 'BusinessController@list');
        $router->any('edit', 'BusinessController@edit');
        $router->any('enable', 'BusinessController@enable');
    }
);

// 业务类目
$router->group(
    ['prefix' => 'admin/business-category', 'namespace' => 'Admin', 'middleware' => []],
    function ($router) {
        $router->any('add', 'BusinessCategoryController@add');      // 类目新增
        $router->any('list-by-business', 'BusinessCategoryController@listByBusiness');    // 类目列表
        $router->any('list', 'BusinessCategoryController@list');    // 全部类目列表
        $router->any('edit', 'BusinessCategoryController@edit');    // 类目编辑
    }
);

// 鉴定师下的业务类目
$router->group(
    ['prefix' => 'admin/appraiser-business', 'namespace' => 'Admin', 'middleware' => []],
    function ($router) {
        $router->any('edit', 'AppraiserBusinessController@edit');    // 业务类目编辑
        $router->any('detail', 'AppraiserBusinessController@detail'); // 业务类目详情
    }
);

// 鉴定师分组&排班
$router->group(
    ['prefix' => 'admin/appraiser-group', 'namespace' => 'Admin', 'middleware' => []],
    function ($router) {
        $router->any('select-info', 'AppraiserGroupController@selectInfo');    // 相关选择数据
        $router->any('add', 'AppraiserGroupController@add');    // 开启分组
        $router->any('detail', 'AppraiserGroupController@detail');  // 分组详情
        $router->any('close', 'AppraiserGroupController@close'); // 关闭分组
        $router->any('schedule-list', 'AppraiserGroupController@scheduleList'); // 排班列表
        $router->any('schedule-edit', 'AppraiserGroupController@scheduleEdit'); // 排班设置
    }
);
