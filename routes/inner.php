<?php


/** @var \Spin\Routing\Router $router */

// 鉴定师派单查询
$router->group(
    ['prefix' => 'inner/appraiser-dispatch', 'namespace' => 'Inner', 'middleware' => []],
    function ($router) {
        $router->any('list', 'AppraiserDispatchController@list');   // 派单鉴定师
        $router->any('test', 'AppraiserDispatchController@test');

    }
);

// 鉴定师信息查询
$router->group(
    ['prefix' => 'inner/appraiser-search', 'namespace' => 'Inner', 'middleware' => []],
    function ($router) {
        $router->any('detail-by-userinfoId', 'AppraiserSearchController@detailByUserinfoId');   // 鉴定师详情
        $router->any('list-by-userinfoIds', 'AppraiserSearchController@listByUserinfoIds');   // 鉴定师列表
        $router->any('simple-list-by-userinfoIds', 'AppraiserSearchController@simpleListByUserinfoIds'); // 鉴定师鉴定数据列表
    }
);

// 鉴定师分组&排班
$router->group(
    ['prefix' => 'inner/appraiser-group', 'namespace' => 'Inner', 'middleware' => []],
    function ($router) {
        $router->any('schedule-info', 'AppraiserGroupController@scheduleInfo');    // 排班信息
    }
);


// 订单
$router->group(
    ['prefix' => 'inner/order', 'namespace' => 'Inner', 'middleware' => []],
    function ($router) {
        $router->any('create', 'OrderController@create');   // 创建订单
        $router->any('detail', 'OrderController@detail');   // 订单详情
        $router->any('simple-detail', 'OrderController@simpleDetail');   // 简单订单详情
        $router->any('batch-simple-detail', 'OrderController@batchSimpleDetail');   // 批量简单订单详情
        $router->any('get-params', 'OrderController@getParams');   // 订单模板参数
        $router->any('submit', 'OrderController@submit');   // 提交订单
        $router->any('cancel', 'OrderController@cancel');   // 取消订单
        $router->any('reject', 'OrderController@reject');   // 打回订单
        $router->any('assign-appraiser', 'OrderController@assignAppraiser');   // 指派鉴定师
        $router->any('list', 'OrderController@list');   // 订单列表
    }
);

$router->group(
    ['prefix' => 'inner/order-data', 'namespace' => 'Inner', 'middleware' => []],
    function ($router) {
        $router->any('get-business-order-data', 'OrderDataController@getBusinessOrderData');   // 获取业务订单数据
        $router->any('center', 'OrderDataController@center');   // 获取个人数据
    }
);

// 模板
$router->group(
    ['prefix' => 'inner/template', 'namespace' => 'Inner', 'middleware' => []],
    function ($router) {
        $router->any('list', 'TemplateController@list');   // 模板列表
        $router->any('create', 'TemplateController@create');   // 创建模板
        $router->any('edit', 'TemplateController@edit');   // 编辑模板
        $router->any('detail', 'TemplateController@detail'); // 模板详情
        $router->any('enable', 'TemplateController@enable'); // 模板启用/禁用
        $router->any('add-field', 'TemplateController@addField'); // 模板添加字段
        $router->any('edit-field', 'TemplateController@editField'); // 模板编辑字段
        $router->any('del-field', 'TemplateController@delField'); // 模板删除字段
    }
);

// 字典
$router->group(
    ['prefix' => 'inner/field', 'namespace' => 'Inner', 'middleware' => []],
    function ($router) {
        $router->any('list', 'FieldController@list');   // 字典列表
        $router->any('create', 'FieldController@create');   // 字典创建
        $router->any('edit', 'FieldController@edit');   // 字典编辑
        $router->any('deleted', 'FieldController@deleted'); // 删除
        $router->any('enable', 'FieldController@enable'); // 启用禁用
    }
);
