# 项目特定单元测试规范

## 1. 项目结构

### 1.1 目录结构

```
-app                    # 应用核心代码
  -Cache               # 缓存相关类
  -Client              # 客户端相关代码
    -Bus               # 业务总线
    -SDK               # SDK实现
  -Console             # 命令行工具
    -Commands          # 自定义命令
  -Constants           # 常量定义
  -ErrCode             # 错误码定义
  -Events              # 事件类
  -Exceptions          # 异常处理
  -Http                # HTTP相关
    -Controllers       # 控制器
      -Admin           # 管理后台控制器
      -Demo            # 演示控制器
      -Inner           # 内部接口控制器
      -Zhenjin         # 特定业务控制器
    -Middleware        # 中间件
    -Requests          # 请求验证
  -Libraries           # 通用库
  -Logic               # 业务逻辑层
    -Inner             # 内部业务逻辑
  -Models              # 数据模型
    -Community         # 社区相关模型
    -Jzg               # 特定业务模型
  -Params              # 参数定义
    -Appariser         # 鉴定相关参数
    -Order             # 订单相关参数
  -Providers           # 服务提供者
  -Service             # 服务层
  -Utils               # 工具类
-bootstrap             # 框架启动文件
-config                # 配置文件
-proto                 # 协议文件
  -admin               # 管理后台协议
  -inner               # 内部协议
-routes                # 路由定义
-tests                 # 测试目录
  -Feature             # 功能测试
    -Admin             # 管理后台测试
    -Logic             # 业务逻辑测试
      -Inner           # 内部业务逻辑测试
```

### 1.2 应用分层架构

本项目采用多层架构设计：

1. **控制器层（Controllers）**：处理HTTP请求，参数验证，调用业务逻辑层
2. **业务逻辑层（Logic）**：实现核心业务规则，协调多个服务
3. **服务层（Service）**：提供特定领域的功能服务，处理数据操作
4. **模型层（Models）**：定义数据结构和数据库交互

### 1.3 路由组织

路由按照不同的业务域进行分组：

- `admin.php`：管理后台相关路由
- `inner.php`：内部服务接口路由
- `zhenjin.php`：特定业务域路由
- `demo.php`：演示用例路由
- `web.php`：通用Web路由

## 2. 数据库操作规则

### 1.1 数据排序规则
- 数据插入并取出时，按照id倒序排序。例如，若插入多条数据，在取出时应确保数据按id从大到小排列。
- 针对 `list` 方法，验证结果时，先将返回列表按 `id` 进行倒序排序，之后选取排序后的第一条数据进行比对操作。

### 1.2 数据插入规则
- 在使用 `{xxx}Model::query()->insert($data)` 插入数据时，`$data` 插入的数据字段应依据 `{xxx}Model` 文件注释中 `property` 的属性来确定。
- 具体而言，需仔细查看 `{xxx}Model` 文件中关于 `property` 的注释说明，以此明确可插入的字段。
- 字段 `is_deleted` 的存在与否，取决于对应 `model` 的注释 `property`。若注释中未提及该字段，则默认不使用 `is_deleted` 字段。
- 不应手动指定 `id` 字段，应使用数据库自增 ID 功能。
- 若需要获取插入数据的 ID，应使用 `{xxx}Model::query()->insertGetId($data)` 方法。

## 2. 异常处理规则

### 2.1 异常代码验证
- 对于 `$this->expectExceptionCode()`，若参数取常量，需使用常量数组中的第0个值。
- 例如，若常量为 `BusinessErr::BUSINESS_NO_EXIST`，则应使用 `BusinessErr::BUSINESS_NO_EXIST[0]`。

### 2.2 异常消息验证
- 验证异常消息时，应使用 `$this->expectExceptionMessage()` 方法。
- 异常消息应与代码中定义的消息完全一致，包括标点符号和空格。

## 3. 项目架构测试规范

### 3.1 控制器层测试
- 控制器测试应关注HTTP请求和响应，验证：
  - 请求参数验证
  - 返回状态码
  - 返回数据结构
  - 权限控制
- 控制器测试文件应放置在 `tests/Feature/Controller` 目录下

### 3.2 Logic层测试
- Logic层测试应关注业务逻辑，验证：
  - 业务规则实现
  - 异常处理
  - 数据转换
  - 条件分支覆盖
- Logic层测试文件应放置在 `tests/Feature/Logic` 目录下

### 3.3 Service层测试
- Service层测试应关注数据操作，验证：
  - 数据创建、读取、更新、删除操作
  - 数据查询条件
  - 数据关联处理
  - 事务处理
- Service层测试文件应放置在 `tests/Feature/Service` 目录下

### 3.4 Model层测试
- Model层测试应关注数据模型，验证：
  - 模型关联关系
  - 模型属性和方法
  - 模型查询作用域
  - 模型事件
- Model层测试文件应放置在 `tests/Feature/Model` 目录下

## 4. 测试数据准备规则

### 4.1 测试数据创建
- 测试数据应在测试方法内创建，不依赖外部数据
- 每个测试方法应创建自己需要的测试数据，不依赖其他测试方法创建的数据

### 4.2 测试数据清理
- 测试完成后，应确保所有测试数据被正确回滚
- 不应在 `tearDown()` 方法中手动删除数据，应依赖事务回滚机制

## 5. 项目特定常量使用规则

### 5.1 错误常量
- 项目中的错误常量定义在 `Constants` 目录下
- 使用常量时应确保常量存在，不得使用不存在的常量
- 错误常量通常为数组形式，第一个元素为错误码，第二个元素为错误消息

### 5.2 状态常量
- 状态常量定义在 `Constants` 目录下的状态相关文件中
- 测试中应使用这些预定义常量，而非硬编码状态值

## 6. 项目测试命名规范

### 6.1 测试方法命名
- 测试方法名应清晰表达测试内容和预期结果
- 命名格式：`test{被测方法名}_{测试场景}_{预期结果}`
- 例如：`testStore_WithValidData_ShouldCreateRecord`

### 6.2 测试数据变量命名
- 测试数据变量命名应清晰表达数据用途
- 使用有意义的变量名，避免使用 `$data1`, `$data2` 等无意义名称
- 变量命名风格应与项目代码保持一致
