# PHP 单元测试通用规范

## 1. 单元测试基本原则

### 1.1 测试的目的
- 验证代码按预期工作
- 防止回归错误
- 提高代码质量和可维护性
- 作为代码文档，展示代码的使用方式
- 促进良好的代码设计和解耦

### 1.2 测试用例覆盖范围
1. **正常路径测试**：验证功能在正常输入情况下的行为，确保功能在常规场景下稳定运行
2. **边界条件测试**：验证系统对极限或临界值的处理，包括：
   - 数值边界（最大值、最小值、零值）
   - 集合边界（空集合、单元素集合、最大容量集合）
   - 时间边界（日期变更点、时区转换点）
   - 资源边界（内存限制、连接数限制）
3. **异常流程测试**：验证系统对异常情况的处理，包括：
   - 无效参数处理
   - 缺失参数处理
   - 参数类型错误处理
   - 数据不存在情况
   - 权限错误情况
   - 资源不可用情况
4. **正常流程测试**：验证常规业务流程的正确性，包括：
   - 数据创建成功
   - 数据更新成功
   - 数据删除成功
   - 数据查询正确

### 1.3 测试结构规范
1. **单一职责原则**：每个测试方法仅测试一个功能点或一个行为
2. **AAA模式**：遵循"Arrange-Act-Assert"（准备-执行-验证）模式
   - Arrange：准备测试数据和环境
   - Act：执行被测试的代码
   - Assert：验证结果是否符合预期
3. **测试方法命名**：采用描述性命名，清晰表达测试内容和预期结果
   - 格式建议：`test{被测方法名}_{测试场景}_{预期结果}`
   - 例如：`testStore_WithValidData_ShouldCreateNewRecord`

### 1.4 测试独立性与可重复性
1. **测试隔离**：测试之间相互独立，不依赖执行顺序
2. **环境一致性**：确保测试前后环境一致，每次运行产生相同结果
3. **避免测试间依赖**：一个测试不应依赖另一个测试的执行结果
4. **自包含**：测试应包含所有必要的设置和清理代码

## 2. PHPUnit 最佳实践

### 2.1 测试类结构
1. **命名规范**：测试类名应为`{被测类名}Test`
2. **继承关系**：测试类应继承自`TestCase`或项目自定义的基础测试类
3. **setUp 和 tearDown**：使用这些方法进行测试前准备和测试后清理
4. **数据提供器**：使用`@dataProvider`注解提供多组测试数据

### 2.2 断言技术
1. **基本断言**：
   - `assertEquals()`：验证值相等
   - `assertSame()`：验证值和类型都相等
   - `assertTrue()/assertFalse()`：验证布尔值
   - `assertNull()/assertNotNull()`：验证空值
   - `assertInstanceOf()`：验证对象类型
   - `assertArrayHasKey()`：验证数组键存在
   - `assertCount()`：验证集合元素数量

2. **高级断言**：
   - `assertJsonStringEqualsJsonString()`：比较JSON字符串
   - `assertStringContainsString()`：验证字符串包含关系
   - `assertMatchesRegularExpression()`：验证正则匹配
   - `assertFileExists()/assertFileEquals()`：验证文件操作

3. **自定义断言**：
   - 创建自定义断言方法处理复杂验证逻辑
   - 使用`PHPUnit\Framework\Assert`类中的静态方法

### 2.3 异常测试
1. **期望异常**：使用`expectException()`方法验证代码是否抛出预期异常
2. **异常消息验证**：使用`expectExceptionMessage()`验证异常消息
3. **异常代码验证**：使用`expectExceptionCode()`验证异常代码

### 2.4 数据库测试
1. **事务回滚**：使用`DatabaseTransactions` trait确保测试不影响数据库
2. **数据库迁移**：使用`RefreshDatabase` trait在测试前重置数据库
3. **数据填充**：使用工厂和填充器创建测试数据
4. **查询断言**：使用`assertDatabaseHas()`和`assertDatabaseMissing()`验证数据库状态

### 2.5 测试替身（Test Doubles）
1. **模拟对象（Mocks）**：模拟复杂对象行为，验证方法调用
2. **存根（Stubs）**：提供预定义的返回值
3. **伪对象（Fakes）**：提供简化的实现
4. **间谍（Spies）**：记录方法调用情况

## 3. Laravel/Lumen 测试特性

### 3.1 HTTP 测试
1. **请求测试**：使用`get()`, `post()`, `put()`, `patch()`, `delete()`方法测试HTTP端点
2. **JSON请求**：使用`getJson()`, `postJson()`等方法测试API
3. **响应断言**：
   - `assertStatus()`：验证HTTP状态码
   - `assertJson()`：验证JSON响应结构
   - `assertJsonFragment()`：验证JSON片段
   - `assertJsonMissing()`：验证JSON不包含特定片段
   - `assertRedirect()`：验证重定向

### 3.2 认证测试
1. **模拟用户**：使用`actingAs()`方法模拟已认证用户
2. **权限测试**：验证不同权限级别的用户访问控制

### 3.3 事件测试
1. **事件断言**：使用`Event::fake()`和`Event::assertDispatched()`验证事件触发
2. **监听器测试**：验证事件监听器的行为

### 3.4 队列测试
1. **队列断言**：使用`Queue::fake()`和`Queue::assertPushed()`验证任务入队
2. **任务测试**：验证队列任务的行为

## 4. 代码分析与测试生成

### 4.1 代码追踪分析
1. **分层分析**：按照框架分层结构分析代码
   - 路由 → 控制器方法
   - 控制器方法 → Logic 类方法
   - Logic 类方法 → Service 类方法
   - Service 类方法 → Model 操作

2. **关键点分析**：
   - 参数验证规则：识别控制器中的验证方法及规则
   - 业务逻辑：分析Logic层的业务规则和处理流程
   - 数据操作：理解Service层的数据增删改查操作
   - 异常处理：识别可能抛出的异常类型和条件
   - 数据结构：分析方法的输入参数和返回值结构

### 4.2 测试场景设计
1. **基于代码分析**：根据代码分析结果设计测试场景
2. **全面覆盖**：确保测试覆盖所有关键业务路径
3. **异常模拟**：模拟各层可能出现的异常情况
4. **数据变异**：测试不同数据组合和边界条件

## 5. 单元测试规范

### 5.1 测试文件组织
1. **测试文件位置**：
   - Logic层测试：`tests/Feature/Logic/{类名}Test.php`
   - Service层测试：`tests/Feature/Service/{类名}Test.php`
   - Controller层测试：`tests/Feature/Controller/{类名}Test.php`
   - Model层测试：`tests/Feature/Model/{类名}Test.php`
   - 路由测试：`tests/Feature/{路由文件名}/{测试名称}.php`

2. **测试文件命名**：
   - 格式：`{被测类名}Test.php`
   - 例如：`AppraiserControllerTest.php`

### 5.2 数据库操作规范
1. **数据插入**：
   - 使用 `{xxxModel}::query()->insert($data)` 插入数据
   - 获取ID时使用 `{xxxModel}::query()->insertGetId($data)`
   - 不手动指定ID，使用自增ID

2. **事务控制**：
   - 使用 `DatabaseTransactions` trait 确保测试数据回滚
   - 避免测试对实际数据库造成影响

### 5.3 代码质量要求
1. **代码规范**：遵循 PSR 编码标准
2. **注释完善**：提供清晰的测试目的和步骤说明
3. **逻辑严谨**：确保测试逻辑无误，避免测试本身的bug
4. **可读性**：代码易于理解和维护
5. **健壮性**：测试应能处理各种边缘情况

### 5.4 其他规范
1. **常量使用**：从 Constants 文件夹中读取已定义的常量，不使用未定义常量
2. **参数命名**：根据代码上下文决定参数命名风格（驼峰或下划线）
3. **真实数据**：优先使用真实数据而非模拟数据
4. **避免过度模拟**：不使用`shouldReceive`方法和`this->mock()`方案

## 6. 测试执行与维护

### 6.1 测试执行
1. **单个测试执行**：`phpunit --filter=测试方法名 测试文件路径`
2. **测试组执行**：`phpunit --group=组名`
3. **全部测试执行**：`phpunit`

### 6.2 测试维护
1. **定期更新**：随代码变更同步更新测试
2. **测试重构**：定期重构测试代码，提高可维护性
3. **测试覆盖率**：定期检查并提高测试覆盖率
4. **测试性能**：优化测试执行速度，减少测试时间

### 6.3 测试文档
1. **测试说明**：为复杂测试提供详细说明
2. **测试依赖**：明确记录测试依赖的环境和数据
3. **测试结果解释**：提供测试结果的解释和分析方法
