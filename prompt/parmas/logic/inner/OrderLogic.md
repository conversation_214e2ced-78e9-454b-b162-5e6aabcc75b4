## 1. 基础参数配置

### 1.1 鉴定师ID规则 (appraiserId)
#### 有效鉴定师ID
```php
[11740300, 12260004, 100320642, 11742588]
```

#### 无效鉴定师ID
```php
[1, 3]
```

### 1.2 已有订单数据
| uri | state | 说明  | 
|----------------------|-------|-----|
| 2506061137yd0tav | 30    | 完成  |
| 2504241645qt10pf | 20    | 待鉴定 |

## 2. 核心方法参数

### 2.1 create方法参数规范

| 参数名称               | 数据类型 | 必填 | 默认值 | 说明              | 关联约束                     |
|--------------------|----------|------|--------|-----------------|------------------------------|
| categoryIdentifier | string   | 是   | -      | 业务类目            | businessId=6时须为1923      |
| items[0].imgs      | array    | 是   | -      | 图片URL数组         | -                            |
| items[0].remark    | string   | 否   | -      | 备注              | -                            |   
| businessId         | integer  | 是   | -      | 值域[1,2,3,4,5,6] | -                            |
| appraiserId        | integer  | 否   | 0      | 0触发自动分配逻辑       | 自动分配时businessId必须为6 |
 


## 3. 特殊规则说明
### 3.1 自动分配规则：当appraiserId=0时
    - 系统自动选择可用鉴定师
    - 必须满足 businessId=6
    - 必须满足 categoryIdentifier=1923
### 3.2 订单数据order表，需要构造不同状态订单数据，state状态说明如下：
    - 10: 待处理
    - 20: 待鉴定
    - 30: 完成
    - 40：取消
### 3.3 submit方法，订单状态 state 必须等于 20,才能进入业务逻辑

### 4. 构造测试数据
1. 业务表（business）,对应model BusinessModel，
2. 业务类目表（business_category）,对应model BusinessCategoryModel
3. 业务字段表（business_field）,对应model BusinessFieldModel
4. 业务模板表 (business_template) ,对应model BusinessTemplateModel
5. 业务模板字段表 (business_template_field) ,对应model BusinessTemplateFieldModel
