/*
 Navicat Premium Data Transfer

 Source Server         : test-03
 Source Server Type    : MySQL
 Source Server Version : 50718
 Source Host           : *********:3306
 Source Schema         : image_ident_server

 Target Server Type    : MySQL
 Target Server Version : 50718
 File Encoding         : 65001

 Date: 09/06/2025 13:08:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for appraiser
-- ----------------------------
DROP TABLE IF EXISTS `appraiser`;
CREATE TABLE `appraiser` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userinfo_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '微拍堂用户id',
  `nickname` varchar(100) NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) NOT NULL DEFAULT '' COMMENT '头像',
  `work_type` tinyint(4) NOT NULL DEFAULT '2' COMMENT '职业类型 1全职 2兼职',
  `is_leader` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否组长，1组长 2非组长',
  `is_trainee` tinyint(4) NOT NULL DEFAULT '2' COMMENT '是否新人 1新人 2非新人',
  `description` varchar(200) NOT NULL DEFAULT '' COMMENT '鉴定描述',
  `personal_profile` varchar(1000) NOT NULL DEFAULT '' COMMENT '个人简介',
  `certificate` varchar(200) NOT NULL DEFAULT '' COMMENT '资质证书',
  `signature_picture` varchar(255) NOT NULL DEFAULT '' COMMENT '专家签名图片',
  `total_commission` int(11) NOT NULL DEFAULT '0' COMMENT '总计佣金(总收入)',
  `total_amount` int(11) NOT NULL DEFAULT '0' COMMENT '账户金额',
  `join_time` int(11) NOT NULL DEFAULT '0' COMMENT '入驻时间',
  `affiliation_id` varchar(100) NOT NULL DEFAULT '' COMMENT '归属人id',
  `affiliation_name` varchar(50) NOT NULL DEFAULT '' COMMENT '归属人名字',
  `rest_start_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '休息开始时间',
  `rest_end_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '休息结束时间',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId` (`userinfo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=42605098 DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师主表';

-- ----------------------------
-- Table structure for appraiser_bill
-- ----------------------------
DROP TABLE IF EXISTS `appraiser_bill`;
CREATE TABLE `appraiser_bill` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userinfo_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `correlation_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联id',
  `transaction_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '交易类型 1:收入,2:支出',
  `bill_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '账单类型 1commission:佣金,2withdrawal:提现',
  `amount` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '金额(单位分)',
  `before_amount` int(11) NOT NULL DEFAULT '0' COMMENT '交易前金额(单位分)',
  `after_amount` int(11) NOT NULL DEFAULT '0' COMMENT '交易后金额(单位分)',
  `detail_json` varchar(255) NOT NULL DEFAULT '' COMMENT '详情json',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId` (`userinfo_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师流水';

-- ----------------------------
-- Table structure for appraiser_business
-- ----------------------------
DROP TABLE IF EXISTS `appraiser_business`;
CREATE TABLE `appraiser_business` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userinfo_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '微拍堂用户id',
  `business_id` int(11) NOT NULL DEFAULT '0' COMMENT '业务id',
  `dispatch_count` int(11) NOT NULL DEFAULT '0' COMMENT '最大派单量',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId` (`userinfo_id`),
  KEY `idx_businessId` (`business_id`)
) ENGINE=InnoDB AUTO_INCREMENT=392 DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师业务表(业务开关)';

-- ----------------------------
-- Table structure for appraiser_business_category
-- ----------------------------
DROP TABLE IF EXISTS `appraiser_business_category`;
CREATE TABLE `appraiser_business_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userinfo_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '微拍堂用户id',
  `business_id` int(11) NOT NULL DEFAULT '0' COMMENT '业务id',
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT '类目id',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId_businessId` (`userinfo_id`,`business_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6763 DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师关联业务下类目';

-- ----------------------------
-- Table structure for appraiser_business_extend
-- ----------------------------
DROP TABLE IF EXISTS `appraiser_business_extend`;
CREATE TABLE `appraiser_business_extend` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userinfo_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '微拍堂用户id',
  `business_id` int(11) NOT NULL DEFAULT '0' COMMENT '业务id',
  `field_name` varchar(100) NOT NULL DEFAULT '' COMMENT '属性名称',
  `field_key` varchar(100) NOT NULL DEFAULT '' COMMENT '属性key',
  `field_val` varchar(200) NOT NULL DEFAULT '' COMMENT '属性值',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId_businessId` (`userinfo_id`,`business_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8087 DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师关联业务个性化属性表';

-- ----------------------------
-- Table structure for appraiser_group
-- ----------------------------
DROP TABLE IF EXISTS `appraiser_group`;
CREATE TABLE `appraiser_group` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `userinfo_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户userinfoId',
  `is_deleted` tinyint(10) unsigned NOT NULL DEFAULT '0' COMMENT '0正常 1删除',
  `create_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId` (`userinfo_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=90 DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师分组表';

-- ----------------------------
-- Table structure for appraiser_group_category_relation
-- ----------------------------
DROP TABLE IF EXISTS `appraiser_group_category_relation`;
CREATE TABLE `appraiser_group_category_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `userinfo_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户userinfoId',
  `business_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '业务id',
  `category_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分类id',
  `is_deleted` tinyint(10) unsigned NOT NULL DEFAULT '0' COMMENT '0正常 1删除',
  `create_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId` (`userinfo_id`),
  KEY `idx_businessId_categoryId` (`business_id`,`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师分组类目关联表';

-- ----------------------------
-- Table structure for appraiser_group_members_relation
-- ----------------------------
DROP TABLE IF EXISTS `appraiser_group_members_relation`;
CREATE TABLE `appraiser_group_members_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `userinfo_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户userinfoId',
  `member_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '组员userinfoId',
  `is_deleted` tinyint(10) unsigned NOT NULL DEFAULT '0' COMMENT '0正常 1删除',
  `create_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId` (`userinfo_id`),
  KEY `idx_memberUid` (`member_uid`)
) ENGINE=InnoDB AUTO_INCREMENT=180 DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师分组组员关联表';

-- ----------------------------
-- Table structure for appraiser_group_scheduling
-- ----------------------------
DROP TABLE IF EXISTS `appraiser_group_scheduling`;
CREATE TABLE `appraiser_group_scheduling` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `userinfo_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户userinfoId',
  `member_uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '组员userinfoId',
  `assignment_date` date NOT NULL DEFAULT '0000-00-00' COMMENT '分配日期(格式:2023-01-01)',
  `assigned_volume` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分配量',
  `is_deleted` tinyint(10) unsigned NOT NULL DEFAULT '0' COMMENT '0正常 1删除',
  `create_time` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '维护字段-更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId` (`userinfo_id`),
  KEY `idx_memberUid_assignmentDate` (`member_uid`,`assignment_date`)
) ENGINE=InnoDB AUTO_INCREMENT=228 DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师分组排班表';

-- ----------------------------
-- Table structure for appraiser_withdrawal
-- ----------------------------
DROP TABLE IF EXISTS `appraiser_withdrawal`;
CREATE TABLE `appraiser_withdrawal` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userinfo_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `amount` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '提现金额',
  `name` varchar(20) NOT NULL DEFAULT '' COMMENT '姓名',
  `card_number` varchar(30) NOT NULL DEFAULT '' COMMENT '银行卡号',
  `pay_type` varchar(20) NOT NULL DEFAULT '' COMMENT '支付方式',
  `remark` varchar(100) NOT NULL DEFAULT '' COMMENT '备注',
  `pay_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '打款时间',
  `pay_req_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '打款请求时间',
  `state` int(10) unsigned NOT NULL DEFAULT '10' COMMENT '状态 0取消，10发起提现，20打款中，30到账，99失败',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId` (`userinfo_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='鉴定师提现表';

-- ----------------------------
-- Table structure for business
-- ----------------------------
DROP TABLE IF EXISTS `business`;
CREATE TABLE `business` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '业务名称',
  `notify_url` varchar(255) NOT NULL DEFAULT '' COMMENT '业务通知地址',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7999631 DEFAULT CHARSET=utf8mb4 COMMENT='业务表';

-- ----------------------------
-- Table structure for business_category
-- ----------------------------
DROP TABLE IF EXISTS `business_category`;
CREATE TABLE `business_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `business_id` int(11) NOT NULL DEFAULT '0' COMMENT '业务id',
  `category_name` varchar(100) NOT NULL DEFAULT '' COMMENT '类目名称',
  `category_identifier` varchar(100) NOT NULL DEFAULT '' COMMENT '类目标志',
  `input_template_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '输入模板ID',
  `output_template_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '输出模板ID',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7117742 DEFAULT CHARSET=utf8mb4 COMMENT='业务下类目表';

-- ----------------------------
-- Table structure for business_field
-- ----------------------------
DROP TABLE IF EXISTS `business_field`;
CREATE TABLE `business_field` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `business_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务id',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_type` varchar(32) NOT NULL DEFAULT '' COMMENT '字段类型',
  `field_key` varchar(32) NOT NULL DEFAULT '' COMMENT '字段key',
  `placeholder` varchar(100) NOT NULL DEFAULT '' COMMENT '字段提示',
  `min_length` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最小长度',
  `max_length` int(11) NOT NULL DEFAULT '0' COMMENT '最大长度',
  `biz_type` varchar(20) NOT NULL DEFAULT '' COMMENT '业务类型 输入input 输出output',
  `is_required` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否必填 0否 1是',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_businessId` (`business_id`)
) ENGINE=InnoDB AUTO_INCREMENT=578223430 DEFAULT CHARSET=utf8mb4 COMMENT='业务字段表';

-- ----------------------------
-- Table structure for business_field_options
-- ----------------------------
DROP TABLE IF EXISTS `business_field_options`;
CREATE TABLE `business_field_options` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `field_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务字段id',
  `option_name` varchar(100) NOT NULL DEFAULT '' COMMENT '选项名称',
  `option_img` varchar(100) NOT NULL DEFAULT '' COMMENT '图片地址',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_fieldId` (`field_id`)
) ENGINE=InnoDB AUTO_INCREMENT=539 DEFAULT CHARSET=utf8mb4 COMMENT='业务字段子表(选项)';

-- ----------------------------
-- Table structure for business_template
-- ----------------------------
DROP TABLE IF EXISTS `business_template`;
CREATE TABLE `business_template` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `business_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务id',
  `template_name` varchar(100) NOT NULL DEFAULT '' COMMENT '模板名称',
  `biz_type` varchar(20) NOT NULL DEFAULT '' COMMENT '业务类型 输入input 输出output',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_businessId` (`business_id`)
) ENGINE=InnoDB AUTO_INCREMENT=738046755 DEFAULT CHARSET=utf8mb4 COMMENT='业务模板表';

-- ----------------------------
-- Table structure for business_template_field
-- ----------------------------
DROP TABLE IF EXISTS `business_template_field`;
CREATE TABLE `business_template_field` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `template_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务模板id',
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父级id',
  `parent_option_name` varchar(100) NOT NULL DEFAULT '' COMMENT '父级选项名称',
  `field_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务字段id',
  `field_key` varchar(32) NOT NULL DEFAULT '' COMMENT '业务字段key',
  `output_type` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '输出类型 1订单 2订单物品',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_templateId` (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9950524 DEFAULT CHARSET=utf8mb4 COMMENT='业务模板字段表';

-- ----------------------------
-- Table structure for commission_config
-- ----------------------------
DROP TABLE IF EXISTS `commission_config`;
CREATE TABLE `commission_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `business_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务id',
  `business_category_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务类目id',
  `userinfo_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `sub_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务类型',
  `commission_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '佣金类型 1固定金额 2比例',
  `commission_amount` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '佣金金额(单位分)',
  `commission_rate` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '佣金比例',
  `state` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态 0禁用 1启用',
  `is_deleted` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId_businessId` (`userinfo_id`,`business_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='佣金配置表';

-- ----------------------------
-- Table structure for commission_detail
-- ----------------------------
DROP TABLE IF EXISTS `commission_detail`;
CREATE TABLE `commission_detail` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `userinfo_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `business_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务id',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
  `commission_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '佣金类型 1固定金额 2比例',
  `commission_amount` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '佣金金额(单位分)',
  `commission_rate` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '佣金比例',
  `order_amount` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单金额(单位分)',
  `is_settlement` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否结算 0未结算 1已结算',
  `detail_json` varchar(300) NOT NULL DEFAULT '' COMMENT '明细json',
  `remark` varchar(200) NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_userinfoId_businessId` (`userinfo_id`,`business_id`),
  KEY `idx_orderId` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='佣金明细表';

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `uri` varchar(32) NOT NULL DEFAULT '' COMMENT '鉴定单号uri',
  `userinfo_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '鉴定师ID',
  `business_master_no` varchar(32) NOT NULL DEFAULT '' COMMENT '业务主单号',
  `business_no` varchar(32) NOT NULL DEFAULT '' COMMENT '业务单号',
  `business_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务id',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '标题',
  `category_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '业务类目id',
  `category_identifier` varchar(100) NOT NULL DEFAULT '' COMMENT '业务类目标识',
  `input_template_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '输入模板ID',
  `output_template_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '输出模板ID',
  `end_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单截止时间',
  `accept_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '接单时间',
  `ident_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '鉴定时间',
  `cover` varchar(200) NOT NULL DEFAULT '' COMMENT '封面图',
  `sub_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单子类型',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  `ident_result` varchar(2000) NOT NULL DEFAULT '' COMMENT '鉴定结论',
  `ident_truth` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '鉴定结果',
  `detail_json` varchar(500) NOT NULL DEFAULT '' COMMENT '扩展信息',
  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_businessId` (`business_id`),
  KEY `idx_uri` (`uri`),
  KEY `idx_userinfoId` (`userinfo_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=174946121 DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- ----------------------------
-- Table structure for order_appraiser_transfer
-- ----------------------------
DROP TABLE IF EXISTS `order_appraiser_transfer`;
CREATE TABLE `order_appraiser_transfer` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `old_appraiser_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '老的鉴定师ID',
  `new_appraiser_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '新的鉴定师ID',
  `operator_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '操作人ID',
  `reason` varchar(200) NOT NULL DEFAULT '' COMMENT '原因',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=348 DEFAULT CHARSET=utf8mb4 COMMENT='订单鉴定师转派';

-- ----------------------------
-- Table structure for order_item
-- ----------------------------
DROP TABLE IF EXISTS `order_item`;
CREATE TABLE `order_item` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
  `order_uri` varchar(32) NOT NULL DEFAULT '' COMMENT '订单号',
  `imgs` varchar(1500) NOT NULL DEFAULT '' COMMENT '物品图片',
  `video` varchar(800) NOT NULL DEFAULT '' COMMENT '物品视频',
  `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '物品备注',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_orderId` (`order_id`),
  KEY `idx_orderUri` (`order_uri`)
) ENGINE=InnoDB AUTO_INCREMENT=5199 DEFAULT CHARSET=utf8mb4 COMMENT='订单物品表';

-- ----------------------------
-- Table structure for order_item_field
-- ----------------------------
DROP TABLE IF EXISTS `order_item_field`;
CREATE TABLE `order_item_field` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
  `order_item_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单物品id',
  `biz_type` varchar(20) NOT NULL DEFAULT '' COMMENT '业务类型 输入input 输出output',
  `field_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '字段id',
  `field_name` varchar(50) NOT NULL DEFAULT '' COMMENT '字段名称',
  `field_key` varchar(32) NOT NULL DEFAULT '' COMMENT '字段key',
  `field_value` varchar(1000) NOT NULL DEFAULT '' COMMENT '字段值',
  `field_type` varchar(32) NOT NULL DEFAULT '' COMMENT '字段类型',
  `output_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '输出类型 1订单 2订单物品',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_orderItemId` (`order_item_id`),
  KEY `idx_orderId` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11647 DEFAULT CHARSET=utf8mb4 COMMENT='订单属性信息';

SET FOREIGN_KEY_CHECKS = 1;
